{"cells": [{"cell_type": "code", "execution_count": 2, "id": "cff2246d-008f-4d83-9214-aad8dcbcc7b5", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd"]}, {"cell_type": "code", "execution_count": 3, "id": "f9a0e3a1-64b1-4d2c-9958-7a11d7e496fa", "metadata": {}, "outputs": [], "source": ["pd.set_option(\"display.max_columns\", 80)\n", "pd.set_option(\"display.width\", 120)"]}, {"cell_type": "code", "execution_count": 4, "id": "e1dcd12b-1952-4de0-9dfd-3d1e07103ff6", "metadata": {}, "outputs": [], "source": ["RANDOM_SEED= 42\n", "rng=np.random.default_rng(RANDOM_SEED)\n"]}, {"cell_type": "code", "execution_count": 5, "id": "de284cdf-99a8-4c56-b8d1-a8d1fdb9e6bc", "metadata": {}, "outputs": [], "source": ["df=pd.read_csv(\"notebooks/S1_demo_hospital_min.csv\")\n"]}, {"cell_type": "code", "execution_count": 6, "id": "00850f20-93a0-4045-864f-67f21768098f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(10, 12)\n", "  episode_id patient_id  age_years sex  hb_g_dl  creatinine_mg_dl  glucose_mg_dl  hr_bpm  sbp_mmhg  spo2_pct  temp_c  \\\n", "0       E001       P001         34   F     13.2               0.9             95      78       120        98    36.7   \n", "1       E002       P002         77   M     11.1               1.4            180      96       140        94    37.8   \n", "2       E003       P003         61   F     12.4               1.1            110      85       130        96    37.0   \n", "\n", "   readmit_30d  \n", "0            0  \n", "1            1  \n", "2            0  \n", "<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 10 entries, 0 to 9\n", "Data columns (total 12 columns):\n", " #   Column            Non-Null Count  Dtype  \n", "---  ------            --------------  -----  \n", " 0   episode_id        10 non-null     object \n", " 1   patient_id        10 non-null     object \n", " 2   age_years         10 non-null     int64  \n", " 3   sex               10 non-null     object \n", " 4   hb_g_dl           10 non-null     float64\n", " 5   creatinine_mg_dl  10 non-null     float64\n", " 6   glucose_mg_dl     10 non-null     int64  \n", " 7   hr_bpm            10 non-null     int64  \n", " 8   sbp_mmhg          10 non-null     int64  \n", " 9   spo2_pct          10 non-null     int64  \n", " 10  temp_c            10 non-null     float64\n", " 11  readmit_30d       10 non-null     int64  \n", "dtypes: float64(3), int64(6), object(3)\n", "memory usage: 1.1+ KB\n", "None\n", "readmit_30d\n", "0    0.7\n", "1    0.3\n", "Name: proportion, dtype: float64\n"]}], "source": ["print(df.shape)\n", "print(df.head(3))\n", "print(df.info())\n", "print(df['readmit_30d'].value_counts(normalize=True))"]}, {"cell_type": "code", "execution_count": 9, "id": "6aeb944e-90bc-48e7-850a-c2edc304163b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Valores de glucosa fuera de rango 0\n"]}], "source": ["ok_glucose= df['glucose_mg_dl'].between(40,600) | df['glucose_mg_dl'].isna()\n", "print('Valores de glucosa fuera de rango', (~ok_glucose).sum())\n"]}, {"cell_type": "code", "execution_count": 8, "id": "1cdacb72-baed-439c-af7f-f29f126965b4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Valores de glucosa fuera de rango 3\n"]}], "source": ["ok_glucose= df['glucose_mg_dl'].between(100,200) | df['glucose_mg_dl'].isna()\n", "print('Valores de glucosa fuera de rango', (~ok_glucose).sum())\n"]}, {"cell_type": "code", "execution_count": 10, "id": "3da8c6e5-b252-450d-8d7b-68b029aeaea9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["episode_id          0.0\n", "patient_id          0.0\n", "age_years           0.0\n", "sex                 0.0\n", "hb_g_dl             0.0\n", "creatinine_mg_dl    0.0\n", "glucose_mg_dl       0.0\n", "hr_bpm              0.0\n", "sbp_mmhg            0.0\n", "spo2_pct            0.0\n", "dtype: float64\n"]}], "source": ["missing= df.isna().mean().sort_values(ascending=False)\n", "print(missing.head(10))"]}, {"cell_type": "code", "execution_count": 11, "id": "b4e35056-17b0-4e79-b79a-1e31dd07592c", "metadata": {}, "outputs": [], "source": ["TARGET = 'readmit_30d'\n", "features = [c for c in df.columns if c not in {TARGET, 'patient_id', 'episode_id'}]"]}, {"cell_type": "code", "execution_count": 12, "id": "f41d2296-3f63-48bd-865f-08cfe6021c8a", "metadata": {}, "outputs": [{"data": {"text/plain": ["(['age_years',\n", "  'sex',\n", "  'hb_g_dl',\n", "  'creatinine_mg_dl',\n", "  'glucose_mg_dl',\n", "  'hr_bpm',\n", "  'sbp_mmhg',\n", "  'spo2_pct',\n", "  'temp_c'],\n", " 9)"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["features[:10], len(features)"]}, {"cell_type": "code", "execution_count": 13, "id": "d0d4cc9d-c944-4734-8054-41030ec931a9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["readmit_30d\n"]}], "source": ["print(TARGET)"]}, {"cell_type": "code", "execution_count": 14, "id": "6aac2b1b-18db-492b-b1e5-ec2d1bf857bf", "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n"]}, {"cell_type": "code", "execution_count": 15, "id": "d281d66a-bd43-4a9e-bcac-4a84a6abecc8", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>n</th>\n", "      <th>pct</th>\n", "    </tr>\n", "    <tr>\n", "      <th>readmit_30d</th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>No readmit</th>\n", "      <td>7</td>\n", "      <td>0.7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Readmit</th>\n", "      <td>3</td>\n", "      <td>0.3</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["             n  pct\n", "readmit_30d        \n", "No readmit   7  0.7\n", "Readmit      3  0.3"]}, "metadata": {}, "output_type": "display_data"}], "source": ["y= df[TARGET].astype(int)\n", "tab = y.value_counts().sort_index().rename(index={0:'No readmit', 1:'Readmit'}).to_frame('n')\n", "tab['pct'] = (tab['n'] / tab['n'].sum()).round(3)\n", "display(tab)"]}, {"cell_type": "code", "execution_count": 16, "id": "8e71d887-ec6e-4cac-a014-34bd053918d4", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Figure size 640x480 with 0 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig=plt.figure()"]}, {"cell_type": "code", "execution_count": 17, "id": "07c1db59-ad96-4232-941b-d9fa8ac5cc47", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["tab['n'].plot(kind='bar')\n", "plt.title('Distribución del target')\n", "plt.ylabel('N casos')\n", "plt.xticks(rotation=0)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 19, "id": "7c440b06-9475-4502-b892-511746655837", "metadata": {}, "outputs": [], "source": ["# correlación entre la temperatura con un rango mayor de 37.00 y un nivel de glucosa mayor a 150\n", "# con la problabilidad de reingreso del paciente\n"]}, {"cell_type": "code", "execution_count": 20, "id": "da44881d-4192-47af-8ee7-bb0b09b91c39", "metadata": {}, "outputs": [], "source": ["TEMP_COL='temp_c'\n", "GLUC_COL='glucose_mg_dl'\n", "TARGET = 'readmit_30d'\n"]}, {"cell_type": "code", "execution_count": 21, "id": "819837c0-e4e2-4161-ab34-75a86cad97e2", "metadata": {}, "outputs": [], "source": ["df=df.copy()"]}, {"cell_type": "code", "execution_count": 23, "id": "d6107f29-bb2b-4ab8-abad-d32ad5829dd7", "metadata": {}, "outputs": [], "source": ["df['temp_hi']=df[TEMP_COL] > 37\n", "df['gluc_hi']=df[GLUC_COL] > 150\n", "df['bot_hi']=df['temp_hi'] & df['gluc_hi']\n"]}, {"cell_type": "code", "execution_count": 24, "id": "d058567b-2db5-4aa8-93e1-1123d2a19bb1", "metadata": {}, "outputs": [], "source": ["df['tg_group'] = np.select([df['bot_hi'], df['temp_hi'] & ~df['gluc_hi'], ~df['temp_hi'] & df['gluc_hi']], ['both', 'temp_only', 'gluc_only'], default='neither')"]}, {"cell_type": "code", "execution_count": 28, "id": "1fc4f789-c964-4e9e-82fd-0241b079349b", "metadata": {}, "outputs": [], "source": ["rt = (df.groupby('tg_group')[TARGET]\n", "     .agg(['count','mean'])\n", "     .rename(columns={'count':'n','mean':'rate'}))"]}, {"cell_type": "code", "execution_count": 29, "id": "ffb4350b-def7-4b20-b120-dab802071bc8", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["rt['rate'].plot(kind='bar')\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "id": "181e648f-4046-4220-b02e-7e032f8137d1", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "anaconda-2024.02-py310", "language": "python", "name": "conda-env-anaconda-2024.02-py310-py"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 5}