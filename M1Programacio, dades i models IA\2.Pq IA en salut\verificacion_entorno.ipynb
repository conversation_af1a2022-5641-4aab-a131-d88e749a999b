{"cells": [{"cell_type": "code", "execution_count": 3, "id": "2a1ebd9d-7b79-43c3-8ec5-7a43b3d50d56", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Matplotlib is building the font cache; this may take a moment.\n"]}], "source": ["%matplotlib inline\n", "import sys, numpy as np, pandas as pd, sklearn, matplotlib"]}, {"cell_type": "code", "execution_count": 4, "id": "48e7af62-dce1-41b5-8eb6-cbdd258f02b6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["3.10.14 (main, Mar 21 2024, 16:24:04) [GCC 11.2.0]\n", "numpy 1.26.4 matplotlib 3.8.0 sklearn 1.3.0 matplotlib 3.8.0\n"]}], "source": ["print(sys.version)\n", "print('numpy',np.__version__,'matplotlib',matplotlib.__version__,'sklearn',sklearn.__version__,'matplotlib',matplotlib.__version__)"]}, {"cell_type": "code", "execution_count": 5, "id": "829510db-44f0-46d4-b4a4-cc6ac784d05b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["   x  y\n", "0  1  2\n", "1  2  3\n", "2  3  5\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["df=pd.DataFrame({'x':[1,2,3],'y':[2,3,5]})\n", "print(df.head())\n", "ax=df.plot(x='x', y='y', kind='line')\n", "ax.set_title('Verificación de entorno')\n", "df=pd.DataFrame"]}, {"cell_type": "code", "execution_count": null, "id": "76f4e69e-7512-42d5-ad8a-6430492fd912", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "anaconda-2024.02-py310", "language": "python", "name": "conda-env-anaconda-2024.02-py310-py"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 5}