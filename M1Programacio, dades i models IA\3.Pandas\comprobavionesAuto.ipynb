{"cells": [{"cell_type": "code", "execution_count": null, "id": "4c145f12", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import re \n", "df = pd.read_csv(\"hospital_general_sim_200.csv\")"]}, {"cell_type": "code", "execution_count": null, "id": "55e4a887", "metadata": {}, "outputs": [], "source": ["#Buscamos columnas sospechosas\n", "suspect_cols = [c for c in df.columns if re.search(r'(mame|surname|address|email|phone|nhc|dni)', c, flags=re.I)]\n", "print(\"Columnas sospechosas: \", suspect_cols)\n"]}], "metadata": {"kernelspec": {"display_name": "ia-bio", "language": "python", "name": "python3"}, "language_info": {"name": "python", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}