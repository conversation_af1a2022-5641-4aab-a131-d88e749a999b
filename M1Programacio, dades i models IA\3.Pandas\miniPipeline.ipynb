{"cells": [{"cell_type": "code", "execution_count": 30, "id": "08a2edf4", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from typing import List"]}, {"cell_type": "code", "execution_count": 31, "id": "4d092e70", "metadata": {}, "outputs": [], "source": ["def filtrar_adultos(df: pd.DataFrame) -> pd.DataFrame:\n", "    \"\"\"Filtrar adultos de un dataframe de pandas\n", "\n", "    Args:\n", "        df (pd.DataFrame): dataframe de pandas\n", "\n", "    Returns:\n", "        pd.DataFrame: dataframe de pandas con adultos\n", "    \"\"\"\n", "    return df.loc[df[\"age_years\"] >= 18].copy()"]}, {"cell_type": "code", "execution_count": 32, "id": "ddd312dd", "metadata": {}, "outputs": [], "source": ["def derivar_basicas(df: pd.DataFrame) -> pd.DataFrame:\n", "    def edad_band(x):\n", "        if pd.isna(x): return pd.NA\n", "        if x < 40: return '<40'\n", "        if x < 60: return '40-59'\n", "        if x < 70: return '60-74'\n", "        return '>=75'\n", "    out = df.copy()\n", "    out[\"edad_band\"] = out[\"age_years\"].map(edad_band)\n", "    out[\"taquicardia\"] = (out[\"hr_bpm\"] >= 100).astype('boolean')\n", "    return out"]}, {"cell_type": "code", "execution_count": 33, "id": "36c8336c", "metadata": {}, "outputs": [], "source": ["def seleccionar_campos(df: pd.DataFrame, keep: List[str]) -> pd.DataFrame:    \n", "    return df[keep].copy()"]}, {"cell_type": "code", "execution_count": 34, "id": "76fc0079", "metadata": {}, "outputs": [], "source": ["df=pd.read_csv(\"hospital_general_sim.csv\")"]}, {"cell_type": "code", "execution_count": 35, "id": "7e19f1bc", "metadata": {}, "outputs": [], "source": ["campos=['age_years','sex', 'bmi', 'hr_bpm', 'sbp_mmhg', 'spo2_pct', 'hb_g_dl', 'creatinine_mg_dl', 'glucose_mg_dl', 'edad_band', 'taquicardia', 'readmit_30d']"]}, {"cell_type": "code", "execution_count": 36, "id": "1af95929", "metadata": {}, "outputs": [], "source": ["base = (df\n", "    .pipe(filtrar_adultos)\n", "    .pipe(derivar_basicas)\n", "    .pipe(seleccionar_campos, keep=campos)\n", ")"]}, {"cell_type": "code", "execution_count": 37, "id": "80a49521", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["OK: base.csv creado\n"]}], "source": ["base.to_csv(\"base.csv\", index=False)\n", "print(\"OK: base.csv creado\")"]}], "metadata": {"kernelspec": {"display_name": "ia-bio", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}