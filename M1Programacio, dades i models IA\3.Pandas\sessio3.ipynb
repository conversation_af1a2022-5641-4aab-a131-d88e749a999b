{"cells": [{"cell_type": "code", "execution_count": null, "id": "b8e6f67d", "metadata": {}, "outputs": [], "source": ["# resumenes por banda de edad\n", "def edad_band(x):\n", "if pd.isna(x): return pd.NA \n", "if x < 40: return '<40'\n", "\n", "if x < 60: return '40-59'\n", "if x < 75: return '60-74'\n", "return '>=75'\n", "adultos['edad_band'] = adultos['age_years'].map(edad_band).astype('category')\n", "tabla = (adultos\n", ".groupby('edad_band')\n", ".agg(n=('readmit_30d','size'),\n", "reingreso_media=('readmit_30d','mean'),\n", "imc_median=('bmi','median'),\n", "hb_med=('hb_g_dl','mean'))\n", ".reset_index()\n", ")\n", "\n"]}], "metadata": {"kernelspec": {"display_name": "ia-bio", "language": "python", "name": "python3"}, "language_info": {"name": "python", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}