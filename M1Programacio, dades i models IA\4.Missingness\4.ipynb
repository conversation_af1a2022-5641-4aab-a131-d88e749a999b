import pandas as pd
import numpy as np


from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.pipeline import Pipeline
from sklearn.compose import ColumnTransformer
from sklearn.preprocessing import StandardScaler, OneHotEncoder
from sklearn.impute import SimpleImputer
from sklearn.linear_model import LogisticRegression



df=pd.read_csv("base03.csv")

TARGET = 'readmit_30d'
y= df [TARGET].astype(int)
X= df.drop(columns=[TARGET])

# SEPARAR TIPSOS DE CLUMNAS
num_cols = X.select_dtypes(include='number').columns.tolist()
cat_cols = X.columns.difference(num_cols).tolist()

print(num_cols)


print(cat_cols)

# Preprocesadores
num_prepro = Pipeline(steps=[
    ('imputer', SimpleImputer(strategy='median')),
    ('scaler', StandardScaler())
])

cal_pre = Pipeline(steps=[
    ('imputer', SimpleImputer(strategy='most_frequent')),
    ('onehot', OneHotEncoder(handle_unknown='ignore'))
])

prep=ColumnTransformer(transformers=[
    ('num', num_pre, num_cols),
    ('cat', cat_pre, cat_cols)
])

clf= Pipeline(steps=[
    ('prep', prep),
    ('model', LogisticRegression(max_iter=1000))
])

X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42, stratify=y)



scores = cross_val_score(clf, X_train, y_train, cv=5, scoring='roc_auc')
print("AUC CV media: ", scores.mean())

clf.fit(X_train, y_train)
from sklearn.metrics import roc_auc_score, average_precision_score


proba_test = clf.predict_proba(X_test)[:,1]
print("ROC AUC (test): ", roc_auc_score(y_test, proba_test))
print("PR AUC (test): ", average_precision_score(y_test, proba_test))