import pandas as pd
import numpy as np

# LLegim el dataset
df = pd.read_csv("../exercicis/HOSPITAL_General_Sim_min.csv")


# Definim els rangs vàlids per cada variable
rangos = {
    "glucose_mg_dl": (40, 600),
    "sbp_mmhg": (60, 260),
    "hr_bpm": (30, 220),
    "temp_c": (30, 43),
}


# Recorrem les variables i apliquem el control
for col, (low, high) in rangos.items():
    if col in df.columns:  # només si la columna existeix al dataset
        # 1. Fora del rang → NaN
        df.loc[(df[col] < low) | (df[col] > high), col] = np.nan
        
        # 2. Opcional: capar als límits
        df[col] = df[col].clip(lower=low, upper=high)

# Confirmació: mirem un resum estadístic després del control
print(df[rangos.keys()].describe())


import os

os.makedirs("figures", exist_ok=True)
try:
	df.to_csv("figures/ex2.csv", index=False)
	print("DataFrame saved to figures/ex2.csv")
except NameError:
	print("Error: 'df' is not defined. Please run the cell that loads the dataframe.")


