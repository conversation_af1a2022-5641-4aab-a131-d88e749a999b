{"cells": [{"cell_type": "code", "execution_count": null, "id": "942688fc", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "# Separar les característiques (X) de la variable objectiu (y)\n", "df = pd.read_csv(\"HOSPITAL_General_Sim_min.csv\")\n", "X = df.drop(columns=[\"episode_id\", \"patient_id\", \"admission_datetime\",\n", "                     \"discharge_datetime\", \"readmit_30d\"])\n", "y = df[\"readmit_30d\"]"]}, {"cell_type": "code", "execution_count": null, "id": "0d9ff164", "metadata": {}, "outputs": [], "source": ["# Convertir variables categòriques (sex, edad_band) a números amb OneHotEncoder\n", "from sklearn.preprocessing import OneHotEncoder\n", "from sklearn.compose import ColumnTransformer\n", "\n", "categorical_cols = [\"sex\", \"edad_band\"]\n", "preprocessor = ColumnTransformer(transformers=[\n", "    (\"cat\", OneHotEncoder(drop='first'), categorical_cols)\n", "], remainder=\"passthrough\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "0599fe21", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "ia-bio", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}