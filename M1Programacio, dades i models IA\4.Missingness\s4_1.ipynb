{"cells": [{"cell_type": "code", "execution_count": 3, "id": "71e533b1-4f83-475f-9ed3-6d52beba4794", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np"]}, {"cell_type": "code", "execution_count": 4, "id": "a17f985b-e61e-4b09-84c4-8e468dac802b", "metadata": {}, "outputs": [], "source": ["df = pd.read_csv(\"HOSPITAL_General_Sim.csv\")"]}, {"cell_type": "code", "execution_count": 5, "id": "8bd0baac-9ba5-460c-84e8-3db4d06f5c2d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                        prop     n\n", "d_dimer_ng_ml         0.3481  3481\n", "troponin_ng_l         0.3033  3033\n", "crp_mg_l              0.2472  2472\n", "ast_u_l               0.1021  1021\n", "alt_u_l               0.0983   983\n", "bilirubin_mg_dl       0.0957   957\n", "albumin_g_dl          0.0820   820\n", "wbc_10e9_per_l        0.0325   325\n", "platelets_10e9_per_l  0.0307   307\n", "creatinine_mg_dl      0.0218   218\n", "glucose_mg_dl         0.0192   192\n", "episode_id            0.0000     0\n"]}], "source": ["#Calcular la tasa de ausentes por columna (proporcion y recuento)\n", "na_col = (df.isna().mean().to_frame('prop')\n", "          .assign(n = df.isna().sum())\n", "          .sort_values('prop', ascending = False))\n", "print(na_col.head(12))"]}, {"cell_type": "code", "execution_count": 6, "id": "bc2373a2-e2a6-4349-9b5c-6b8d586e28ca", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Media de ausentes por fila:  1.3809\n", "Mediana de ausentes por fila:  1.0\n"]}], "source": ["#Ausentes por fila\n", "na_row = df.isna().sum(axis=1)\n", "print('Media de ausentes por fila: ', na_row.mean())\n", "print('Mediana de ausentes por fila: ', na_row.median())\n"]}, {"cell_type": "code", "execution_count": 7, "id": "ae9a30cf-77d1-41a9-892e-a52bd6b29fb6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                  hb_g_dl  wbc_10e9_per_l  creatinine_mg_dl\n", "hb_g_dl               NaN             NaN               NaN\n", "wbc_10e9_per_l        NaN        1.000000         -0.011914\n", "creatinine_mg_dl      NaN       -0.011914          1.000000\n"]}], "source": ["#Coausencia simple: correlación de indicadores NA\n", "cols = [c for c in ['hb_g_dl','wbc_10e9_per_l','creatinine_mg_dl'] if c in df.columns]\n", "na_ind = df.isna().astype(int)\n", "coausencia = na_ind.corr()\n", "print(coausencia.loc[cols,cols])\n"]}, {"cell_type": "code", "execution_count": 8, "id": "6cae62c0-1a41-47f8-8d22-db4ca29b9a23", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["glucose_mg_dl: fuera de [40, 600] --> 0\n", "sbp_mmhg: fuera de [60, 260] --> 0\n", "hr_bpm: fuera de [30, 220] --> 0\n", "temp_c: fuera de [30, 43] --> 0\n"]}], "source": ["#Rangos fisiológicos básicos (bandas)\n", "checks = {'glucose_mg_dl': (40, 600), 'sbp_mmhg': (60, 260),\n", "          'hr_bpm': (30, 220), 'temp_c': (30, 43)}\n", "for col, (lo,hi) in checks.items():\n", "    if col in df.columns:\n", "        out_of_range = (~df[col].between(lo,hi) & df[col].notna()).sum()\n", "        print(f\"{col}: fuera de [{lo}, {hi}] --> {out_of_range}\")"]}, {"cell_type": "code", "execution_count": 9, "id": "ad9bf839-b561-481b-bc3b-fbe05f29ebe9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [" Duplicados en creatitina:  325\n", " Duplicados en episode:  0\n", " Duplicados en patient:  6329\n"]}], "source": ["# Calculo de duplicados por columna especifica\n", "# creatinina no tiene mucho sentido buscar duplicados ya que es un dato comun\n", "print(' Duplicados en creatitina: ', df['creatinine_mg_dl'].duplicated().sum())\n", "# Para limpieza y adecuacion de los datos no puede haber duplicados en columnas como episode_id\n", "print(' Duplicados en episode: ', df['episode_id'].duplicated().sum())\n", "print(' Duplicados en patient: ', df['patient_id'].duplicated().sum())\n"]}, {"cell_type": "code", "execution_count": null, "id": "fa1f56a3-edf6-4b87-b2a4-0d61e3bed7cf", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "ia-bio", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}