import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

base = pd.read_csv("baseS03.csv")

# prevalença de l'outcome

prev = base['readmit_30d'].mean()
print(f"Prevalencia de readmisión en 30 días: {prev:.3f}")


# resum per edat_band i sexe
tab = (base
       .groupby(['edad_band', 'sex'])
       .agg(n=('readmit_30d', 'size'),
            reingreso_media=(
           'readmit_30d', 'mean'),
           cr_med=('creatinine_mg_dl', 'median'),
           hb_med=('hb_g_dl', 'median'))
       .reset_index())
print(tab)


# histograma de glucosa

plt.figure() # netegem la figura!!!!!
vals = base['glucose_mg_dl'].dropna()
plt.hist(vals, bins='auto')
plt.xlabel('Glucosa (mg/dL)')
plt.ylabel('Frecuencia')
plt.title(f'Distribución de glucosa (N={vals.shape[0]})')
plt.tight_layout()
plt.show()


#Boxplot de cretinina per edat_band
plt.figure() # netegem la figura!!!!!
labels = ['<40', '40-59', '60-74', '>=75']
data_bp = [base.loc[base['edad_band'] == b, 'creatinine_mg_dl']
           .dropna() for b in labels]
plt.boxplot(data_bp, labels=labels)
plt.ylabel('Cretinina (mg/dL)')
plt.title('Creatinina per edat_band')
plt.tight_layout()
plt.show()

# Dispersión hb vs readmit (con ligero jitter)
plt.figure()
mask = base[['hb_g_dl','readmit_30d']].notna().all(axis=1)
x = base.loc[mask, 'hb_g_dl'].values
y = base.loc[mask, 'readmit_30d'].values + np.random.uniform(-0.02, 0.02, size=mask.sum())
plt.scatter(x, y, alpha=0.8) 
plt.xlabel('Hemoglubina (g(dL)')
plt.ylabel('Reingreso (0/1 con jitter)')
plt.title(f'Relación hemoglobina y reingreso (N={mask.sum()})')
plt.tight_layout()
plt.show()          

