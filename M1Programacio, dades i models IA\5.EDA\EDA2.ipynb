{"cells": [{"cell_type": "code", "execution_count": 1, "id": "c7540a3d", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "base = pd.read_csv(\"baseS03.csv\")"]}, {"cell_type": "code", "execution_count": null, "id": "1478d3d1", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Barres de prevalença per banda edat\n", "plt.figure()\n", "prev_tab = (base.groupby('edad_band')['readmit_30d']\n", ".mean()\n", ".reindex(['<40','40-59','60-74','>=75']))\n", "plt.bar(range(len(prev_tab)), prev_tab.values)\n", "plt.xticks(range(len(prev_tab)), prev_tab.index)\n", "plt.ylabel('Prevalencia reingreso')\n", "plt.title('Reingreso por banda de edad')\n", "plt.tight_layout()\n"]}, {"cell_type": "code", "execution_count": null, "id": "4ad4bbe5", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Histograma de creatinina amb línia de referència\n", "plt.figure()\n", "vals = base['creatinine_mg_dl'].dropna()\n", "\n", "\n", "plt.hist(vals, bins=40)\n", "plt.axvline(x=np.median(vals), color='red', linestyle='--', linewidth=2)\n", "plt.xlabel('Cretinina (mg/dL)')\n", "plt.title('C<PERSON><PERSON>ina - Histograma amb mitjana')\n", "\n", "plt.tight_layout()\n"]}, {"cell_type": "code", "execution_count": null, "id": "dec0447e", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Relació de glucosa vs hemoglobina (dispersió)\n", "plt.figure()\n", "mask = base[['glucose_mg_dl']].notna().all(axis=1)\n", "\n", "plt.scatter(base.loc[mask, 'glucose_mg_dl'], base.loc[mask, 'hb_g_dl'])\n", "plt.xlabel('Glucosa (mg/dL)')\n", "plt.ylabel('Hemog<PERSON><PERSON> (g/dL)')\n", "\n", "plt.title('Glu<PERSON><PERSON> vs Hemoglobina')\n"]}], "metadata": {"kernelspec": {"display_name": "ia-bio", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}