{"cells": [{"cell_type": "code", "execution_count": 1, "id": "c356a4c7-3498-4d82-90ce-abb56140e193", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "base = pd.read_csv(\"baseS03.csv\")"]}, {"cell_type": "code", "execution_count": 2, "id": "2ef2f88d-1536-4459-b87a-90d78b446a14", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Prevalencia readmit_30d:  0.18\n"]}], "source": ["# prevalencia del outcome\n", "prev = base['readmit_30d'].mean()\n", "print(\"Prevalencia readmit_30d: \", round(prev, 3))"]}, {"cell_type": "code", "execution_count": 3, "id": "f74d1615-5045-4cfc-b7c8-18ff92a071ab", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["  edad_band sex   n  reingreso  hb_med    cr_med\n", "0     40-59   F  43   0.139535   11.30  1.222674\n", "1     40-59   M  40   0.175000   12.80  1.274655\n", "2     60-74   F  35   0.228571   12.10  1.495078\n", "3     60-74   M  31   0.161290   12.50  1.216109\n", "4       <40   F   5   0.000000   10.90  0.831816\n", "5       <40   M   7   0.142857   12.80  1.220115\n", "6      >=75   F  18   0.222222   12.05  1.554445\n", "7      >=75   M  21   0.238095   11.90  1.544285\n"]}], "source": ["# resumen por edad_band y sexo\n", "tab = (base\n", "       .groupby(['edad_band','sex'])\n", "       .agg(n=('readmit_30d','size'),\n", "            reingreso=('readmit_30d','mean'),\n", "            hb_med=('hb_g_dl','median'),\n", "            cr_med=('creatinine_mg_dl','median'))\n", "       .reset_index())\n", "print(tab)"]}, {"cell_type": "code", "execution_count": 4, "id": "b935a066-24f1-4c62-aa00-8fdcbff946b1", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Histograma de glucosa\n", "plt.figure()\n", "vals = base['glucose_mg_dl'].dropna()\n", "plt.hist(vals, bins='auto')\n", "plt.xlabel('Glucosa (mg/dL)')\n", "plt.ylabel('Frecuencia')\n", "plt.title(f'Distribución de glucosa (N={vals.shape[0]})')\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 5, "id": "fe9ecee7-d67d-4bcb-8efe-6316bb91d825", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_21132\\4051006210.py:6: MatplotlibDeprecationWarning: The 'labels' parameter of boxplot() has been renamed 'tick_labels' since Matplotlib 3.9; support for the old name will be dropped in 3.11.\n", "  plt.boxplot(data_bp, labels=labels)\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["#Boxplot de creatinina por banda de edad\n", "plt.figure()\n", "labels = ['<40','40-59','60-74','>=75']\n", "data_bp =[base.loc[base['edad_band']==b, 'creatinine_mg_dl']\n", "          .dropna() for b in labels]\n", "plt.boxplot(data_bp, labels=labels)\n", "plt.ylabel('Creatinina (mg/dL)')\n", "plt.title('<PERSON><PERSON><PERSON><PERSON> por banda de edad')\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 6, "id": "553f0dd6-e493-49d8-a291-a9d94ead1019", "metadata": {}, "outputs": [{"data": {"image/png": "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*************************************************************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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Dispersión hb vs readmit (con ligero jitter)\n", "plt.figure()\n", "mask = base[['hb_g_dl','readmit_30d']].notna().all(axis=1)\n", "x = base.loc[mask, 'hb_g_dl'].values\n", "y = base.loc[mask, 'readmit_30d'].values + np.random.uniform(-0.02, 0.02, size=mask.sum())\n", "plt.scatter(x, y, alpha=0.8) \n", "plt.xlabel('Hemoglubina (g(dL)')\n", "plt.ylabel('<PERSON><PERSON><PERSON><PERSON> (0/1 con jitter)')\n", "plt.title(f'Relación hemoglobina y reingreso (N={mask.sum()})')\n", "plt.tight_layout()\n", "plt.show()          "]}, {"cell_type": "code", "execution_count": null, "id": "e7b3e4a8-4d6b-4018-8007-70346ce2aa97", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "ia-bio", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}