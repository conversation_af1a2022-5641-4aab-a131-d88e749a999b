{"cells": [{"cell_type": "code", "execution_count": 2, "id": "e756375d-aa6e-4321-b340-ea70c48a4871", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "base= pd.read_csv(\"baseS03.csv\")"]}, {"cell_type": "code", "execution_count": 5, "id": "489d305c-0cd3-4ecb-8051-ad0d8558e3d2", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["#Barras de prevalencia por banda edad\n", "plt.figure()\n", "prev_tab=(base.groupby('edad_band')['readmit_30d']\n", "          .mean()\n", "          .reindex(['<40','40-59','60-74','>=75']))\n", "plt.bar(range(len(prev_tab)), prev_tab.values)\n", "plt.xticks(range(len(prev_tab)), prev_tab.index)\n", "plt.ylabel('Prevalencia reingreso')\n", "plt.title('Reingreso por banda de edad')\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 8, "id": "49d3175d-bac6-4e6c-a6f4-ab82a7859a9a", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Histograma de creatinina con línea de referencia\n", "plt.figure()\n", "vals = base['creatinine_mg_dl'].dropna()\n", "plt.hist(vals, bins=40)\n", "plt.axvline(x=np.median(vals), linestyle='--', color='tab:red', linewidth=2)\n", "plt.xlabel('Creatinina (mg/ml)')\n", "plt.title('Creatinina - histograma con mediana')\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 10, "id": "84184de5-33e1-4e8b-a4c0-134169299c29", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Relación de glucosa vs hemoglobina (dispersión)\n", "plt.figure()\n", "mask = base[['glucose_mg_dl','hb_g_dl']].notna().all(axis=1)\n", "plt.scatter(base.loc[mask, 'glucose_mg_dl'], base.loc[mask, 'hb_g_dl'])\n", "plt.xlabel('Glucosa (mg/dL)')\n", "plt.ylabel('Hemoglubina (g/dL)')\n", "plt.title('Glucosa vs Hemoglubina')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "b7edd673-de79-489b-be80-a97060e89117", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "anaconda-2024.02-py310", "language": "python", "name": "conda-env-anaconda-2024.02-py310-py"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 5}