{"cells": [{"cell_type": "markdown", "id": "aaebeac0", "metadata": {}, "source": ["# Ejercicio 1: <PERSON><PERSON> que “habla” por edad y sexo (básico)\n", "Construye una tabla resumen por edad_band y sex con: n, prevalencia de readmit_30d, medianas de hb_g_dl y creatinine_mg_dl, y proporción de NA de esas dos columnas. Exporta a figures/S05_ej1_tabla_resumen.csv."]}, {"cell_type": "code", "execution_count": 17, "id": "47819ad0", "metadata": {}, "outputs": [], "source": ["import os\n", "import pandas as pd\n", "\n", "# carregar dataset\n", "base = pd.read_csv(\"../HOSPITAL_General_Sim_min.csv\")\n"]}, {"cell_type": "code", "execution_count": 18, "id": "0c1431a7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Prevalencia de readmisión en 30 días: 0.180\n"]}], "source": ["\n", "# prevalença global\n", "prev = base['readmit_30d'].mean()\n", "print(f\"Prevalencia de readmisión en 30 días: {prev:.3f}\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "463d208c", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_30724\\3872995623.py:7: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  .groupby(['edad_band', 'sex'])\n"]}], "source": ["# Definir ordre correcte de les bandes d'edat\n", "orden_edat = [\"<40\", \"40-59\", \"60-74\", \">=75\"]\n", "base['edad_band'] = pd.Categorical(base['edad_band'], categories=orden_edat, ordered=True)\n", "\n", "# taula resum per edat i sexe\n", "tab = (base\n", "       .groupby(['edad_band', 'sex'], dropna=False)\n", "       .agg(\n", "           n=('readmit_30d', 'size'),\n", "           prevalencia=('readmit_30d', 'mean'),\n", "           cr_med=('creatinine_mg_dl', 'median'),\n", "           hb_med=('hb_g_dl', 'median'),\n", "           prop_NA_hb=('hb_g_dl', lambda x: x.isna().mean()),\n", "           prop_NA_creat=('creatinine_mg_dl', lambda x: x.isna().mean())\n", "       )\n", "       .reset_index())"]}, {"cell_type": "code", "execution_count": 20, "id": "66a62862", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Carpeta 'figures' ja existia 👍\n", "Taula exportada correctament a 'figures\\S06_ej1_tabla_resumen.csv' 📂\n"]}], "source": ["\n", "# crear carpeta figures si no existeix\n", "output_dir = \"figures\"\n", "if not os.path.exists(output_dir):\n", "    os.makedirs(output_dir)\n", "    print(f\"Carpeta '{output_dir}' creada correctament ✅\")\n", "else:\n", "    print(f\"Carpeta '{output_dir}' ja existia 👍\")\n", "\n", "# exportar a CSV\n", "output_file = os.path.join(output_dir, \"S06_ej1_tabla_resumen.csv\")\n", "tab.to_csv(output_file, index=False)\n", "print(f\"Taula exportada correctament a '{output_file}' 📂\")\n"]}], "metadata": {"kernelspec": {"display_name": "ia-bio", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}