{"cells": [{"cell_type": "code", "execution_count": 12, "id": "95f98ab1", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import matplotlib.pyplot as plt\n", "pd.set_option(\"display.max_columns\", 120)\n", "pd.set_option(\"display.width\", 160)\n", "df = pd.read_csv(\"../HOSPITAL_General_Sim_min.csv\")\n", "\n", "# Definir ordre correcte de les bandes d'edat\n", "orden = [\"<40\", \"40-59\", \"60-74\", \">=75\"]\n", "if \"edad_band\" in df.columns:\n", "    try:\n", "        from pandas.api.types import CategoricalDtype\n", "        dtype = CategoricalDtype(categories=orden, ordered=True)\n", "        df['edad_band'] = df['edad_band'].astype(cat_type)\n", "    except Exception:\n", "        pass"]}, {"cell_type": "markdown", "id": "aadd0e70", "metadata": {}, "source": ["## <PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": 13, "id": "f019e5ff", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_10076\\1170800729.py:13: FutureWarning: DataFrameGroupBy.apply operated on the grouping columns. This behavior is deprecated, and in a future version of pandas the grouping columns will be excluded from the operation. Either pass `include_groups=False` to exclude the groupings or explicitly select the grouping columns after groupby to silence this warning.\n", "  .apply(lambda g: pd.Series({\n"]}], "source": ["# Recompte i medias\n", "\n", "tab = (df\n", "       .groupby(['edad_band', 'sex'], dropna=False)\n", "       .agg(n=('readmit_30d', 'size'),\n", "            prev=('readmit_30d', 'mean'),\n", "            cr_med=('creatinine_mg_dl', 'median'),\n", "            hb_med=('hb_g_dl', 'median'))\n", "       .reset_index())\n", "\n", "na_grp = (df\n", "          .groupby(['edad_band', 'sex'], dropna=False)\n", "          .apply(lambda g: pd.Series({\n", "              'hb_na': g['hb_g_dl'].isna().mean()*100,\n", "              'cr_na': g['creatinine_mg_dl'].isna().mean()*100}))\n", "          .reset_index())"]}, {"cell_type": "code", "execution_count": 14, "id": "ef80607a", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "edad_band", "rawType": "object", "type": "string"}, {"name": "sex", "rawType": "object", "type": "string"}, {"name": "n", "rawType": "int64", "type": "integer"}, {"name": "prev", "rawType": "float64", "type": "float"}, {"name": "cr_med", "rawType": "float64", "type": "float"}, {"name": "hb_med", "rawType": "float64", "type": "float"}, {"name": "hb_na", "rawType": "float64", "type": "float"}, {"name": "cr_na", "rawType": "float64", "type": "float"}], "ref": "f70ab25b-5acf-4b00-8cd4-6417e44bb592", "rows": [["0", "40-59", "F", "43", "0.14", "1.2226735456612072", "11.3", "0.0", "7.0"], ["1", "40-59", "M", "40", "0.175", "1.2746550908895504", "12.8", "0.0", "2.5"], ["2", "60-74", "F", "35", "0.229", "1.495077769595822", "12.1", "0.0", "0.0"], ["3", "60-74", "M", "31", "0.161", "1.216108532057743", "12.5", "0.0", "3.2"], ["4", "<40", "F", "5", "0.0", "0.8318162841355699", "10.9", "0.0", "0.0"], ["5", "<40", "M", "7", "0.143", "1.2201148559968489", "12.8", "0.0", "0.0"], ["6", ">=75", "F", "18", "0.222", "1.5544454334414666", "12.05", "0.0", "0.0"], ["7", ">=75", "M", "21", "0.238", "1.5442851355923093", "11.9", "0.0", "0.0"]], "shape": {"columns": 8, "rows": 8}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>edad_band</th>\n", "      <th>sex</th>\n", "      <th>n</th>\n", "      <th>prev</th>\n", "      <th>cr_med</th>\n", "      <th>hb_med</th>\n", "      <th>hb_na</th>\n", "      <th>cr_na</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>40-59</td>\n", "      <td>F</td>\n", "      <td>43</td>\n", "      <td>0.140</td>\n", "      <td>1.222674</td>\n", "      <td>11.30</td>\n", "      <td>0.0</td>\n", "      <td>7.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>40-59</td>\n", "      <td>M</td>\n", "      <td>40</td>\n", "      <td>0.175</td>\n", "      <td>1.274655</td>\n", "      <td>12.80</td>\n", "      <td>0.0</td>\n", "      <td>2.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>60-74</td>\n", "      <td>F</td>\n", "      <td>35</td>\n", "      <td>0.229</td>\n", "      <td>1.495078</td>\n", "      <td>12.10</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>60-74</td>\n", "      <td>M</td>\n", "      <td>31</td>\n", "      <td>0.161</td>\n", "      <td>1.216109</td>\n", "      <td>12.50</td>\n", "      <td>0.0</td>\n", "      <td>3.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>&lt;40</td>\n", "      <td>F</td>\n", "      <td>5</td>\n", "      <td>0.000</td>\n", "      <td>0.831816</td>\n", "      <td>10.90</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>&lt;40</td>\n", "      <td>M</td>\n", "      <td>7</td>\n", "      <td>0.143</td>\n", "      <td>1.220115</td>\n", "      <td>12.80</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>&gt;=75</td>\n", "      <td>F</td>\n", "      <td>18</td>\n", "      <td>0.222</td>\n", "      <td>1.554445</td>\n", "      <td>12.05</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>&gt;=75</td>\n", "      <td>M</td>\n", "      <td>21</td>\n", "      <td>0.238</td>\n", "      <td>1.544285</td>\n", "      <td>11.90</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  edad_band sex   n   prev    cr_med  hb_med  hb_na  cr_na\n", "0     40-59   F  43  0.140  1.222674   11.30    0.0    7.0\n", "1     40-59   M  40  0.175  1.274655   12.80    0.0    2.5\n", "2     60-74   F  35  0.229  1.495078   12.10    0.0    0.0\n", "3     60-74   M  31  0.161  1.216109   12.50    0.0    3.2\n", "4       <40   F   5  0.000  0.831816   10.90    0.0    0.0\n", "5       <40   M   7  0.143  1.220115   12.80    0.0    0.0\n", "6      >=75   F  18  0.222  1.554445   12.05    0.0    0.0\n", "7      >=75   M  21  0.238  1.544285   11.90    0.0    0.0"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["tab = tab.merge(na_grp, on=['edad_band', 'sex'], how='left')\n", "tab['prev'] = tab['prev'].round(3)\n", "tab[['hb_na', 'cr_na']] = tab[['hb_na', 'cr_na']].round(1)\n", "tab"]}, {"cell_type": "code", "execution_count": 15, "id": "b1a2006b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Taula exportada correctament a 'S05_ej1_correccio_taula_resum.csv'\n"]}], "source": ["# Exportar taula\n", "\n", "tab.to_csv(\"S05_ej1_correccio_taula_resum.csv\", index=False)\n", "print(\"Taula exportada correctament a 'S05_ej1_correccio_taula_resum.csv'\")"]}], "metadata": {"kernelspec": {"display_name": "ia-bio", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}