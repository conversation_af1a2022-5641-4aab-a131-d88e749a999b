{"cells": [{"cell_type": "markdown", "id": "acacec85", "metadata": {}, "source": ["Ejercicio 2: Univariantes limpias: rangos + hist/box (básico)\n", "Comprueba rangos docentes (glucose_mg_dl, creatinine_mg_dl, hb_g_dl), convierte imposibles en NA y grafica: un histograma y un boxplot por banda de edad por variable. Exporta un informe de fuera-de-rango a figures/S05_ej2_out_of_range.csv"]}, {"cell_type": "code", "execution_count": 54, "id": "8861b478", "metadata": {}, "outputs": [], "source": ["import os\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "base = pd.read_csv(\"../HOSPITAL_General_Sim_min.csv\")"]}, {"cell_type": "code", "execution_count": 55, "id": "5463c4f3", "metadata": {}, "outputs": [], "source": ["# De<PERSON><PERSON> rangs docents\n", "ranges = {\n", "    \"glucose_mg_dl\": (40, 600),\n", "    \"creatinine_mg_dl\": (0.2, 15),\n", "    \"hb_g_dl\": (3, 22)\n", "}\n", "# Definir ordre correcte de les bandes d'edat\n", "orden_edat = [\"<40\", \"40-59\", \"60-74\", \">=75\"]\n", "base['edad_band'] = pd.Categorical(\n", "    base['edad_band'], categories=orden_edat, ordered=True)\n", "\n", "# Detectar fora de rang i substituir per NaN\n", "out_of_range = []\n", "for col, (low, high) in ranges.items():\n", "    mask = ~base[col].between(low, high) & base[col].notna()\n", "    n_out = mask.sum()\n", "    if n_out > 0:\n", "        out_of_range.append({\n", "            \"variable\": col,\n", "            \"fora_de_rang\": n_out,\n", "            \"total\": len(base),\n", "            \"proporcio\": n_out / len(base)\n", "        })\n", "    base.loc[mask, col] = np.nan  # convertir a NA"]}, {"cell_type": "code", "execution_count": 56, "id": "6d159014", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Informe de fora de rang exportat a figures/S06_ej2_out_of_range.csv\n"]}], "source": ["# Guardar informe de fora de rang\n", "os.makedirs(\"figures\", exist_ok=True)\n", "\n", "if len(out_of_range) == 0:\n", "    out_report = pd.DataFrame([{\n", "        \"variable\": \"totes\",\n", "        \"fora_de_rang\": 0,\n", "        \"total\": len(base),\n", "        \"proporcio\": 0.0\n", "    }])\n", "else:\n", "    out_report = pd.DataFrame(out_of_range)\n", "\n", "out_report.to_csv(\"figures/S06_ej2_out_of_range.csv\", index=False)\n", "print(\"Informe de fora de rang exportat a figures/S06_ej2_out_of_range.csv\")"]}, {"cell_type": "code", "execution_count": 57, "id": "18c2929c", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_36616\\1653220631.py:30: MatplotlibDeprecationWarning: The 'labels' parameter of boxplot() has been renamed 'tick_labels' since Matplotlib 3.9; support for the old name will be dropped in 3.11.\n", "  plt.boxplot(data_bp, labels=labels, patch_artist=True,\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_36616\\1653220631.py:30: MatplotlibDeprecationWarning: The 'labels' parameter of boxplot() has been renamed 'tick_labels' since Matplotlib 3.9; support for the old name will be dropped in 3.11.\n", "  plt.boxplot(data_bp, labels=labels, patch_artist=True,\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Gràfics generats i guardats correctament a 'figures/'\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_36616\\1653220631.py:30: MatplotlibDeprecationWarning: The 'labels' parameter of boxplot() has been renamed 'tick_labels' since Matplotlib 3.9; support for the old name will be dropped in 3.11.\n", "  plt.boxplot(data_bp, labels=labels, patch_artist=True,\n"]}, {"data": {"text/plain": ["<Figure size 640x480 with 0 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["<Figure size 640x480 with 0 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["<Figure size 640x480 with 0 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["<Figure size 640x480 with 0 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["<Figure size 640x480 with 0 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["<Figure size 640x480 with 0 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Gràfics\n", "# només les bandes existents\n", "labels = [b for b in orden_edat if b in base['edad_band'].unique()]\n", "\n", "# Crear carpeta figures si no existeix\n", "output_dir = \"figures\"\n", "os.makedirs(output_dir, exist_ok=True)\n", "\n", "for col in ranges.keys():\n", "    # Comprovar si la columna existeix\n", "    if col not in base.columns:\n", "        print(f\"La columna {col} no existeix, saltant...\")\n", "        continue\n", "\n", "    # Histograma\n", "    plt.figure()\n", "    plt.figure(figsize=(6, 4))\n", "    base[col].dropna().hist(bins='auto', color='#3498DB') \n", "    plt.title(f\"Histograma de {col}\")\n", "    plt.xlabel(col)\n", "    plt.ylabel(\"Freqüència\")\n", "    plt.tight_layout()\n", "    plt.savefig(os.path.join(output_dir, f\"S06_ej2_hist_{col}.png\"))\n", "    plt.close()\n", "\n", "    # Boxplot per banda d'edat\n", "    plt.figure()\n", "    plt.figure(figsize=(8, 4))\n", "    data_bp = [base.loc[base['edad_band'] == b, col].dropna() for b in labels]\n", "    plt.boxplot(data_bp, labels=labels, patch_artist=True,\n", "                boxprops=dict(facecolor='#E74C3C', color='#E74C3C'),\n", "                medianprops=dict(color='yellow'))\n", "    plt.ylabel(col)\n", "    plt.title(f\"{col} per banda d'edat\")\n", "    plt.xticks(rotation=45)\n", "    plt.tight_layout()\n", "    plt.savefig(os.path.join(output_dir, f\"S06_ej2_boxplot_{col}.png\"))\n", "    plt.close()\n", "\n", "print(\"Gràfics generats i guardats correctament a 'figures/'\")"]}], "metadata": {"kernelspec": {"display_name": "ia-bio", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}