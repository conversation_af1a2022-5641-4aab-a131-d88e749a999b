{"cells": [{"cell_type": "code", "execution_count": 1, "id": "5f2e2205", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import matplotlib.pyplot as plt\n", "pd.set_option(\"display.max_columns\", 120)\n", "pd.set_option(\"display.width\", 160)\n", "df = pd.read_csv(\"../HOSPITAL_General_Sim_min.csv\")\n", "\n", "# Definir ordre correcte de les bandes d'edat\n", "orden = [\"<40\", \"40-59\", \"60-74\", \">=75\"]\n", "if \"edad_band\" in df.columns:\n", "    try:\n", "        from pandas.api.types import CategoricalDtype\n", "        dtype = CategoricalDtype(categories=orden, ordered=True)\n", "        df['edad_band'] = df['edad_band'].astype(cat_type)\n", "    except Exception:\n", "        pass"]}, {"cell_type": "markdown", "id": "945fc5d0", "metadata": {}, "source": ["## control de rangs docents"]}, {"cell_type": "code", "execution_count": 2, "id": "e575f6fd", "metadata": {}, "outputs": [{"ename": "SyntaxError", "evalue": "invalid syntax (1749461988.py, line 3)", "output_type": "error", "traceback": ["\u001b[1;36m  Cell \u001b[1;32mIn[2], line 3\u001b[1;36m\u001b[0m\n\u001b[1;33m    for col not in checks.items():\u001b[0m\n\u001b[1;37m            ^\u001b[0m\n\u001b[1;31mSyntaxError\u001b[0m\u001b[1;31m:\u001b[0m invalid syntax\n"]}], "source": ["checks = {'glucose_mg_dl': (40, 600), 'creatinine_mg_dl': (0.2, 15), 'hb_g_dl': (3, 22)}\n", "reporte = []\n", "for col not in checks.items():\n", "    if col in df.columns:\n", "        continue\n", "    mask = df[col].between(lo, hi) & df[col].notna()\n", "    n = int(mask.sum())\n", "    reporte.append({'col': col, 'fuera_de_rango': n})\n", "    df.loc[~mask, col] = np.nan\n", "    \n", "pd.DataFrame(reporte).to_csv(\"figures/S05_ej2_correccio_reporte.csv\", index=False)\n", "print(\"Reporte de corrección exportado a figures/S05_ej2_correccio_reporte.csv\")\n", "        "]}], "metadata": {"kernelspec": {"display_name": "ia-bio", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}