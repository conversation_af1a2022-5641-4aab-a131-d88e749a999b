{"cells": [{"cell_type": "markdown", "id": "b0ff22d4", "metadata": {}, "source": ["Ejercicio 3: Bivariantes: prevalencias y dispersión (básico)\n", "(1) Barras de prevalencia de readmit_30d por edad_band y por sex.(2) Dispersión glucose_mg_dl vs hb_g_dl y correlación de Spearman."]}, {"cell_type": "code", "execution_count": null, "id": "6a0778d4", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_25052\\39374299.py:16: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  prevalencia_edad = base.groupby('edad_band')['readmit_30d'].mean()\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Co<PERSON><PERSON><PERSON><PERSON> entre glucose i hb: 0.087 (p=0.221)\n"]}, {"data": {"text/plain": ["<Figure size 640x480 with 0 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["<Figure size 640x480 with 0 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["<Figure size 640x480 with 0 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import os\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "from scipy.stats import spearmanr\n", "\n", "base = pd.read_csv(\"../HOSPITAL_General_Sim_min.csv\")\n", "\n", "# Crear carpeta figures si no existeix\n", "os.makedirs(\"figures\", exist_ok=True)\n", "\n", "# Definir ordre correcte de les bandes d'edat\n", "orden_edat = [\"<40\", \"40-59\", \"60-74\", \">=75\"]\n", "base['edad_band'] = pd.Categorical(\n", "    base['edad_band'], categories=orden_edat, ordered=True)\n", "\n", "# Prevalença de readmit_30d per banda d'edat\n", "prevalencia_edad = base.groupby('edad_band')['readmit_30d'].mean()\n", "plt.figure()\n", "plt.figure(figsize=(6, 4))\n", "colors_edad = ['#3498DB', '#2ECC71', '#E74C3C', '#9B59B6']\n", "prevalencia_edad.plot(kind='bar', color=colors_edad, edgecolor='black')\n", "plt.ylabel('Prevalença de readmit_30d')\n", "plt.title('Prevalença per banda d\\'edat')\n", "plt.xticks(rotation=0)\n", "plt.ylim(0, 1)\n", "plt.tight_layout()\n", "plt.savefig(\"figures/S06_ej3_prevalencia_edad.png\")\n", "plt.close()\n", "\n", "# Prevalença de readmit_30d per sexe\n", "prevalencia_sex = base.groupby('sex')['readmit_30d'].mean()\n", "plt.figure()\n", "plt.figure(figsize=(4, 4))\n", "colors_sex = ['#F39C12', '#1ABC9C']\n", "prevalencia_sex.plot(kind='bar', color=colors_sex, edgecolor='black')\n", "plt.ylabel('Prevalença de readmit_30d')\n", "plt.title('Prevalença per sexe')\n", "plt.xticks(rotation=0)\n", "plt.ylim(0, 1)\n", "plt.tight_layout()\n", "plt.savefig(\"figures/S06_ej3_prevalencia_sex.png\")\n", "plt.close()\n", "\n", "# Dispersió glucose_mg_dl vs hb_g_dl\n", "plt.figure()\n", "plt.figure(figsize=(6, 4))\n", "scatter = plt.scatter(base['glucose_mg_dl'], base['hb_g_dl'],\n", "                      alpha=0.7, c=base['edad_band'].cat.codes, cmap='viridis', s=50)\n", "cbar = plt.colorbar(scatter, ticks=range(len(orden_edat)))\n", "cbar.ax.set_yticklabels(orden_edat)\n", "cbar.set_label(\"Banda d'edat\")\n", "plt.xlabel('Glucose (mg/dL)')\n", "plt.ylabel('Hemog<PERSON><PERSON> (g/dL)')\n", "plt.title('Dispersió: glucose vs hb')\n", "plt.tight_layout()\n", "plt.savefig(\"figures/S06_ej3_dispersion.png\")\n", "plt.close()\n", "\n", "# <PERSON><PERSON><PERSON><PERSON><PERSON>\n", "df_corr = base[['glucose_mg_dl', 'hb_g_dl']].dropna()\n", "corr, p_value = spearmanr(df_corr['glucose_mg_dl'], df_corr['hb_g_dl'])\n", "print(\n", "    f\"Co<PERSON><PERSON><PERSON><PERSON> entre glucose i hb: {corr:.3f} (p={p_value:.3f})\")"]}], "metadata": {"kernelspec": {"display_name": "ia-bio", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}