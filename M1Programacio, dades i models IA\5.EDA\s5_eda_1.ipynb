{"cells": [{"cell_type": "code", "execution_count": 2, "id": "c356a4c7-3498-4d82-90ce-abb56140e193", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "base = pd.read_csv(\"baseS03.csv\")"]}, {"cell_type": "code", "execution_count": 3, "id": "2ef2f88d-1536-4459-b87a-90d78b446a14", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Prevalencia readmit_30d:  0.18\n"]}], "source": ["# prevalencia del outcome\n", "prev = base['readmit_30d'].mean()\n", "print(\"Prevalencia readmit_30d: \", round(prev, 3))"]}, {"cell_type": "code", "execution_count": 4, "id": "f74d1615-5045-4cfc-b7c8-18ff92a071ab", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["  edad_band sex   n  reingreso  hb_med    cr_med\n", "0     40-59   F  43   0.139535   11.30  1.222674\n", "1     40-59   M  40   0.175000   12.80  1.274655\n", "2     60-74   F  35   0.228571   12.10  1.495078\n", "3     60-74   M  31   0.161290   12.50  1.216109\n", "4       <40   F   5   0.000000   10.90  0.831816\n", "5       <40   M   7   0.142857   12.80  1.220115\n", "6      >=75   F  18   0.222222   12.05  1.554445\n", "7      >=75   M  21   0.238095   11.90  1.544285\n"]}], "source": ["# resumen por edad_band y sexo\n", "tab = (base\n", "       .groupby(['edad_band','sex'])\n", "       .agg(n=('readmit_30d','size'),\n", "            reingreso=('readmit_30d','mean'),\n", "            hb_med=('hb_g_dl','median'),\n", "            cr_med=('creatinine_mg_dl','median'))\n", "       .reset_index())\n", "print(tab)"]}, {"cell_type": "code", "execution_count": 5, "id": "b935a066-24f1-4c62-aa00-8fdcbff946b1", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Histograma de glucosa\n", "plt.figure()\n", "vals = base['glucose_mg_dl'].dropna()\n", "plt.hist(vals, bins='auto')\n", "plt.xlabel('Glucosa (mg/dL)')\n", "plt.ylabel('Frecuencia')\n", "plt.title(f'Distribución de glucosa (N={vals.shape[0]})')\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 9, "id": "fe9ecee7-d67d-4bcb-8efe-6316bb91d825", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["#Boxplot de creatinina por banda de edad\n", "plt.figure()\n", "labels = ['<40','40-59','60-74','>=75']\n", "data_bp =[base.loc[base['edad_band']==b, 'creatinine_mg_dl']\n", "          .dropna() for b in labels]\n", "plt.boxplot(data_bp, labels=labels)\n", "plt.ylabel('Creatinina (mg/dL)')\n", "plt.title('<PERSON><PERSON><PERSON><PERSON> por banda de edad')\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 19, "id": "553f0dd6-e493-49d8-a291-a9d94ead1019", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Dispersión hb vs readmit (con ligero jitter)\n", "plt.figure()\n", "mask = base[['hb_g_dl','readmit_30d']].notna().all(axis=1)\n", "x = base.loc[mask, 'hb_g_dl'].values\n", "y = base.loc[mask, 'readmit_30d'].values + np.random.uniform(-0.02, 0.02, size=mask.sum())\n", "plt.scatter(x, y, alpha=0.8) \n", "plt.xlabel('Hemoglubina (g(dL)')\n", "plt.ylabel('<PERSON><PERSON><PERSON><PERSON> (0/1 con jitter)')\n", "plt.title(f'Relación hemoglobina y reingreso (N={mask.sum()})')\n", "plt.tight_layout()\n", "plt.show()          "]}, {"cell_type": "code", "execution_count": null, "id": "e7b3e4a8-4d6b-4018-8007-70346ce2aa97", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "anaconda-2024.02-py310", "language": "python", "name": "conda-env-anaconda-2024.02-py310-py"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 5}