{"cells": [{"cell_type": "markdown", "id": "1c3efc86-1420-41e2-a059-c2ad71dad9c3", "metadata": {}, "source": ["Ejercicio 1: <PERSON><PERSON> que “habla” por edad y sexo (básico)\n", "Construye una tabla resumen por edad_band y sex con: n, prevalencia de readmit_30d, medianas de\n", "hb_g_dl y creatinine_mg_dl, y proporción de NA de esas dos columnas. Exporta a\n", "figures/S05_ej1_tabla_resumen.csv.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "87d1d5d2-4a79-4583-b981-f06bbd257a1c", "metadata": {}, "outputs": [], "source": ["import pandas as pd, numpy as np, os\n", "import matplotlib.pyplot as plt\n", "pd.set_option(\"display.max_columns\", 120)\n", "pd.set_option(\"display.width\", 160)\n", "df=pd.read_csv(\"HOSPITAL_General_Sim_min.csv\")"]}, {"cell_type": "code", "execution_count": 2, "id": "af1bb668-2b97-43ed-9272-62df3d34a900", "metadata": {}, "outputs": [], "source": ["orden = ['<40','40-59','60-74','>=75']\n", "if \"edad_band\" in df.columns:\n", "    try: \n", "        from pandas.api.types import CategoricalDtype\n", "        dtype= CategoricalDtype(categories=orden, ordered=True)\n", "        df[\"edad_band\"] = df[\"edad_band\"].astype(dtype)\n", "    except Exception:\n", "        pass"]}, {"cell_type": "markdown", "id": "4b268b0a-ae4d-4cb8-ba3b-c578d1ecad32", "metadata": {}, "source": ["### Tabla Resumen"]}, {"cell_type": "code", "execution_count": 5, "id": "30bba677-116f-4435-a62a-7a11d898222b", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_143/4000903183.py:2: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  tab =(df\n", "/tmp/ipykernel_143/4000903183.py:10: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  na_grp = (df\n"]}], "source": ["# Recuento y medidas\n", "tab =(df\n", "      .groupby(['edad_band','sex'], dropna=False)\n", "      .agg(n=('readmit_30d', 'size'),\n", "           prev=('readmit_30d','mean'),\n", "           hb_med=('hb_g_dl','median'),\n", "           cr=('creatinine_mg_dl','median'))\n", "      .reset_index())\n", "\n", "na_grp = (df\n", "          .groupby(['edad_band','sex'], dropna=False)\n", "          .apply(lambda g: pd.Series({\n", "              'hb_na_pct': g['hb_g_dl'].isna().mean()*100,\n", "              'cr_na_pct': g['creatinine_mg_dl'].isna().mean()*100}))\n", "         .reset_index())"]}, {"cell_type": "code", "execution_count": 8, "id": "e3226d67-3635-4cc4-b080-ca894538c550", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>edad_band</th>\n", "      <th>sex</th>\n", "      <th>n</th>\n", "      <th>prev</th>\n", "      <th>hb_med</th>\n", "      <th>cr</th>\n", "      <th>hb_na_pct_x</th>\n", "      <th>cr_na_pct_x</th>\n", "      <th>hb_na_pct_y</th>\n", "      <th>cr_na_pct_y</th>\n", "      <th>hb_na_pct</th>\n", "      <th>cr_na_pct</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>&lt;40</td>\n", "      <td>F</td>\n", "      <td>5</td>\n", "      <td>0.000</td>\n", "      <td>10.90</td>\n", "      <td>0.831816</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>&lt;40</td>\n", "      <td>M</td>\n", "      <td>7</td>\n", "      <td>0.143</td>\n", "      <td>12.80</td>\n", "      <td>1.220115</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>40-59</td>\n", "      <td>F</td>\n", "      <td>43</td>\n", "      <td>0.140</td>\n", "      <td>11.30</td>\n", "      <td>1.222674</td>\n", "      <td>0.0</td>\n", "      <td>7.0</td>\n", "      <td>0.0</td>\n", "      <td>6.976744</td>\n", "      <td>0.0</td>\n", "      <td>7.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>40-59</td>\n", "      <td>M</td>\n", "      <td>40</td>\n", "      <td>0.175</td>\n", "      <td>12.80</td>\n", "      <td>1.274655</td>\n", "      <td>0.0</td>\n", "      <td>2.5</td>\n", "      <td>0.0</td>\n", "      <td>2.500000</td>\n", "      <td>0.0</td>\n", "      <td>2.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>60-74</td>\n", "      <td>F</td>\n", "      <td>35</td>\n", "      <td>0.229</td>\n", "      <td>12.10</td>\n", "      <td>1.495078</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>60-74</td>\n", "      <td>M</td>\n", "      <td>31</td>\n", "      <td>0.161</td>\n", "      <td>12.50</td>\n", "      <td>1.216109</td>\n", "      <td>0.0</td>\n", "      <td>3.2</td>\n", "      <td>0.0</td>\n", "      <td>3.225806</td>\n", "      <td>0.0</td>\n", "      <td>3.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>&gt;=75</td>\n", "      <td>F</td>\n", "      <td>18</td>\n", "      <td>0.222</td>\n", "      <td>12.05</td>\n", "      <td>1.554445</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>&gt;=75</td>\n", "      <td>M</td>\n", "      <td>21</td>\n", "      <td>0.238</td>\n", "      <td>11.90</td>\n", "      <td>1.544285</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  edad_band sex   n   prev  hb_med        cr  hb_na_pct_x  cr_na_pct_x  hb_na_pct_y  cr_na_pct_y  hb_na_pct  cr_na_pct\n", "0       <40   F   5  0.000   10.90  0.831816          0.0          0.0          0.0     0.000000        0.0        0.0\n", "1       <40   M   7  0.143   12.80  1.220115          0.0          0.0          0.0     0.000000        0.0        0.0\n", "2     40-59   F  43  0.140   11.30  1.222674          0.0          7.0          0.0     6.976744        0.0        7.0\n", "3     40-59   M  40  0.175   12.80  1.274655          0.0          2.5          0.0     2.500000        0.0        2.5\n", "4     60-74   F  35  0.229   12.10  1.495078          0.0          0.0          0.0     0.000000        0.0        0.0\n", "5     60-74   M  31  0.161   12.50  1.216109          0.0          3.2          0.0     3.225806        0.0        3.2\n", "6      >=75   F  18  0.222   12.05  1.554445          0.0          0.0          0.0     0.000000        0.0        0.0\n", "7      >=75   M  21  0.238   11.90  1.544285          0.0          0.0          0.0     0.000000        0.0        0.0"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["tab = tab.merge(na_grp, on=['edad_band','sex'], how='left')\n", "tab['prev'] = tab['prev'].round(3)\n", "tab[['hb_na_pct', 'cr_na_pct']]=tab[['hb_na_pct', 'cr_na_pct']].round(1)\n", "tab\n"]}, {"cell_type": "code", "execution_count": 9, "id": "b35d2911-bd38-4568-9a4e-705b911aaa5d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Guardado -->figures/S05_E1_tabla_resumen.csv\n"]}], "source": ["# Exportar la tabla\n", "tab.to_csv(\"figures/S05_E1_tabla_resumen.csv\", index=False)\n", "print(\"Guardado -->figures/S05_E1_tabla_resumen.csv\")"]}, {"cell_type": "code", "execution_count": null, "id": "469272bc-aa7b-47c8-b2e4-c28107c9db4c", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "anaconda-2024.02-py310", "language": "python", "name": "conda-env-anaconda-2024.02-py310-py"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 5}