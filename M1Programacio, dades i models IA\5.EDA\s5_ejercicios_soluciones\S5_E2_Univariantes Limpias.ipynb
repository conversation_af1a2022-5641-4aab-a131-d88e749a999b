{"cells": [{"cell_type": "markdown", "id": "95d2ee2e-54db-45a3-9ffe-3abf855a8624", "metadata": {}, "source": ["Ejercicio 2: Univariantes limpias: rangos + hist/box (básico)\n", "Comprueba rangos docentes (glucose_mg_dl, creatinine_mg_dl, hb_g_dl), convierte imposibles en\n", "NA y grafica: un histograma y un boxplot por banda de edad por variable. Exporta un informe de\n", "fuera-de-rango a figures/S05_ej2_out_of_range.csv.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "a67788c8-7ce8-4fc4-a92c-13afef42093a", "metadata": {}, "outputs": [], "source": ["import pandas as pd, numpy as np, os\n", "import matplotlib.pyplot as plt\n", "pd.set_option(\"display.max_columns\", 120)\n", "pd.set_option(\"display.width\", 160)\n", "df=pd.read_csv(\"HOSPITAL_General_Sim_min.csv\")"]}, {"cell_type": "code", "execution_count": 2, "id": "6ac9c488-23b3-42ad-98a1-a1fedb504c8d", "metadata": {}, "outputs": [], "source": ["orden = ['<40','40-59','60-74','>=75']\n", "if \"edad_band\" in df.columns:\n", "    try: \n", "        from pandas.api.types import CategoricalDtype\n", "        dtype= CategoricalDtype(categories=orden, ordered=True)\n", "        df[\"edad_band\"] = df[\"edad_band\"].astype(dtype)\n", "    except Exception:\n", "        pass"]}, {"cell_type": "markdown", "id": "46afbd18-c23c-4c28-84f9-4a9287df3634", "metadata": {}, "source": ["### Control de rangos Docentes\n"]}, {"cell_type": "code", "execution_count": 6, "id": "28d00c2b-ec58-4f26-a1f3-56aa8ee18952", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Guardado --> figures/S05_ej2_out_of_range.csv\n"]}], "source": ["checks = {'glucose_mg_dl':(40,600),'creatinine_mg_dl':(0.2,15),'hb_g_dl':(3,22)}\n", "reporte =[]\n", "for col,(lo,hi) in checks.items():\n", "    if col not in df.columns:\n", "        continue\n", "    mask = ~df[col].between(lo,hi) & df[col].notna()\n", "    n = int(mask.sum())\n", "    reporte.append({'col': col, 'fuera_de_rango': n})\n", "    df.loc[mask, col] = np.nan\n", "pd.DataFrame(reporte).to_csv(\"figures/S05_ej2_out_of_range.csv\", index=False)\n", "print(\"Guardado --> figures/S05_ej2_out_of_range.csv\")"]}, {"cell_type": "markdown", "id": "4992e83d-4a81-4105-82ab-d55dbd9a1bdb", "metadata": {}, "source": ["### Histograma"]}, {"cell_type": "code", "execution_count": 12, "id": "89957f1c-1901-4e5c-991e-771f5ea72110", "metadata": {}, "outputs": [{"data": {"image/png": "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****************************************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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["for col in ['glucose_mg_dl','creatinine_mg_dl','hb_g_dl']:\n", "    if col not in df.columns:\n", "        continue\n", "    plt.figure()\n", "    valors = df[col].dropna()\n", "    plt.hist(valors, bins='auto')\n", "    plt.xlabel(col)\n", "    plt.ylabel('Frecuencia')\n", "    plt.title(f'Histograma de {col} (N={valors.shape[0]})')\n", "    plt.show()"]}, {"cell_type": "markdown", "id": "e6f4809d-0323-4902-b434-af24dc9e251b", "metadata": {}, "source": ["### Boxplots"]}, {"cell_type": "code", "execution_count": 14, "id": "0ad7deef-3ee6-47e7-aac0-8cff413cd9c3", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["for col in ['glucose_mg_dl','creatinine_mg_dl','hb_g_dl']:\n", "    if col not in df.columns or 'edad_band' not in df.columns:\n", "        continue\n", "    plt.figure()\n", "    labels = ['<40','40-59','60-74','>=75']\n", "    data_bp = [df.loc[df['edad_band']==b, col].dropna() for b in labels]\n", "    plt.boxplot(data_bp, labels=labels)\n", "    plt.ylabel(col)\n", "    plt.title(f'{col} por banda de edad')\n", "    plt.tight_layout()\n", "    plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "bc57afe8-3c45-4ca4-bec8-14a40c12c03b", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "anaconda-2024.02-py310", "language": "python", "name": "conda-env-anaconda-2024.02-py310-py"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 5}