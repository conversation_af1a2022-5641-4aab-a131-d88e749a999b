{"cells": [{"cell_type": "markdown", "id": "e14bb9ac-5c44-4565-9c45-d5df76263ac1", "metadata": {}, "source": ["Ejercicio 3: Bivariantes: prevalencias y dispersión (básico)\n", "(1) Barras de prevalencia de readmit_30d por edad_band y por sex.\n", "(2) Dispersión glucose_mg_dl vs hb_g_dl y correlación de Spearman."]}, {"cell_type": "code", "execution_count": 1, "id": "2951cd6e-e7a6-469f-b1c8-a76b4db9a44e", "metadata": {}, "outputs": [], "source": ["import pandas as pd, numpy as np, os\n", "import matplotlib.pyplot as plt\n", "pd.set_option(\"display.max_columns\", 120)\n", "pd.set_option(\"display.width\", 160)\n", "df=pd.read_csv(\"HOSPITAL_General_Sim_min.csv\")"]}, {"cell_type": "code", "execution_count": 2, "id": "d7527dae-cc61-4376-bd9b-1f4750038c23", "metadata": {}, "outputs": [], "source": ["orden = ['<40','40-59','60-74','>=75']\n", "if \"edad_band\" in df.columns:\n", "    try: \n", "        from pandas.api.types import CategoricalDtype\n", "        dtype= CategoricalDtype(categories=orden, ordered=True)\n", "        df[\"edad_band\"] = df[\"edad_band\"].astype(dtype)\n", "    except Exception:\n", "        pass"]}, {"cell_type": "markdown", "id": "f4eda2e6-9182-4334-b9f0-614ba66726c4", "metadata": {}, "source": ["### Barras de prevalencia de readmit_30d por edad_band."]}, {"cell_type": "code", "execution_count": 7, "id": "9667d52d-00d8-4533-8ab0-98067f00742f", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_923/2287416615.py:3: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  prev_tab= (df.groupby('edad_band')['readmit_30d'].mean()\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["if \"edad_band\" in df.columns:\n", "    plt.figure()\n", "    prev_tab= (df.groupby('edad_band')['readmit_30d'].mean()\n", "               .reindex(['<40','40-59','60-74','>=75']))\n", "    plt.bar(range(len(prev_tab)), prev_tab.values)\n", "    plt.xticks(range(len(prev_tab)), prev_tab.index)\n", "    plt.ylabel('Prevalencia reingreso')\n", "    plt.title('Reingreso por banda de edad')\n", "    plt.tight_layout()\n", "    plt.show()"]}, {"cell_type": "markdown", "id": "ebf6ecdd-ad51-4168-a205-a04393b0be7c", "metadata": {}, "source": ["### Barras de prevalencia de readmit_30d por sex."]}, {"cell_type": "code", "execution_count": 8, "id": "dc1daa67-fae0-453f-ab52-ac912446c753", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["if \"sex\" in df.columns:\n", "    plt.figure()\n", "    prev_sex= (df.groupby('sex')['readmit_30d'].mean())\n", "    plt.bar(range(len(prev_sex)), prev_sex.values)\n", "    plt.xticks(range(len(prev_sex)), prev_sex.index)\n", "    plt.ylabel('Prevalencia reingreso')\n", "    plt.title('Reingreso por sexo')\n", "    plt.tight_layout()\n", "    plt.show()"]}, {"cell_type": "markdown", "id": "d7483d1f-718b-4215-a32b-94af7d6c48d3", "metadata": {}, "source": ["### Dispersión glucose_mg_dl vs hb_g_dl"]}, {"cell_type": "code", "execution_count": 11, "id": "efd2ec94-7313-41fb-806f-5514a397e231", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.figure()\n", "mask = df[['glucose_mg_dl','hb_g_dl']].notna().all(axis=1)\n", "x= df.loc[mask, 'glucose_mg_dl'].values\n", "y= df.loc[mask, 'hb_g_dl'].values\n", "plt.scatter(x, y)\n", "plt.xlabel('Glucosa (mg/dl)')\n", "plt.ylabel('Hemog<PERSON><PERSON> (g/dL)')\n", "plt.title('Glu<PERSON><PERSON> vs Hemoglobina')\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "id": "73174eec-9e6e-4104-ba90-6e929af8cbed", "metadata": {}, "source": ["### Correlación de Spearman."]}, {"cell_type": "code", "execution_count": 12, "id": "d49b1e7d-e0f6-49f5-a3e6-5fb1f4a1a2f2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Correlac<PERSON>pearman: 0.087 (p=0.221)\n"]}], "source": ["from scipy.stats import spearmanr\n", "corr, pval = spearmanr(x,y)\n", "print(F'Correlac<PERSON>: {corr:.3f} (p={pval:.3g})')"]}, {"cell_type": "code", "execution_count": null, "id": "903bf4c3-9488-43b6-9f7f-c3f9c900d5f2", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "anaconda-2024.02-py310", "language": "python", "name": "conda-env-anaconda-2024.02-py310-py"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 5}