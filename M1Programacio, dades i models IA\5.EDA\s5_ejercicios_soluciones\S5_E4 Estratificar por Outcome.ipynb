{"cells": [{"cell_type": "markdown", "id": "f4629dcd-8457-4f31-a5ed-3c4a74ec0761", "metadata": {}, "source": ["#Ejercicio 4: Estratificar por outcome + SMD (avanzado)\n", "Compara la distribución de glucose_mg_dl, hb_g_dl y creatinine_mg_dl entre readmit_30d=0 y =1\n", "con boxplots y calcula el Standardized Mean Difference (SMD) por variable."]}, {"cell_type": "code", "execution_count": 19, "id": "d6afa69e-039a-4810-9f64-e6543805ee70", "metadata": {}, "outputs": [], "source": ["import pandas as pd, numpy as np, os\n", "import matplotlib.pyplot as plt\n", "pd.set_option(\"display.max_columns\", 120)\n", "pd.set_option(\"display.width\", 160)\n", "df=pd.read_csv(\"HOSPITAL_General_Sim_min.csv\")"]}, {"cell_type": "code", "execution_count": 20, "id": "aa435d8c-c641-4b4a-a085-15c09bb9857c", "metadata": {}, "outputs": [], "source": ["orden = ['<40','40-59','60-74','>=75']\n", "if \"edad_band\" in df.columns:\n", "    try: \n", "        from pandas.api.types import CategoricalDtype\n", "        dtype= CategoricalDtype(categories=orden, ordered=True)\n", "        df[\"edad_band\"] = df[\"edad_band\"].astype(dtype)\n", "    except Exception:\n", "        pass"]}, {"cell_type": "markdown", "id": "e76b4250-0c49-4d3f-ae00-3aafd539f1ce", "metadata": {}, "source": ["### Boxplots por outcome"]}, {"cell_type": "code", "execution_count": 21, "id": "d90e6d53-acfb-4939-bc81-300e96746e14", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["vars_ = ['glucose_mg_dl','hb_g_dl','creatinine_mg_dl']\n", "for col in vars_:\n", "    if col not in df.columns:\n", "        continue\n", "    plt.figure()\n", "    g0 = df.loc[df['readmit_30d']==0, col].dropna()\n", "    g1 = df.loc[df['readmit_30d']==1, col].dropna()\n", "    plt.boxplot([g0,g1], labels=['No ingreso', 'Reingreso'])\n", "    plt.ylabel(col)\n", "    plt.title(f'{col}: distribución por outcome')\n", "    plt.tight_layout()\n", "    plt.show()"]}, {"cell_type": "markdown", "id": "7ca43dfd-7adc-47b6-ba6a-572f277d5ec5", "metadata": {}, "source": ["### calcula el Standardized Mean Difference (SMD) por variable"]}, {"cell_type": "code", "execution_count": 22, "id": "462c6198-3ec8-4abb-834d-874d71847099", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>variable</th>\n", "      <th>SMD</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>glucose_mg_dl</td>\n", "      <td>0.128</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>hb_g_dl</td>\n", "      <td>0.128</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>creatinine_mg_dl</td>\n", "      <td>0.128</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["           variable    SMD\n", "0     glucose_mg_dl  0.128\n", "1           hb_g_dl  0.128\n", "2  creatinine_mg_dl  0.128"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["def smd(a,b):\n", "    m1, m0 = np.nanmean(a), np.nanmean(b)\n", "    s1, s0 = np.nanstd(a, ddof=1), np.nanstd(b, ddof=1)\n", "    s = np.sqrt(((len(a)-1)*s1**2 + (len(b)-1)*s0**2) / (len(a)+len(b)-2))\n", "    return(m1 -m0) / s if s>0 else np.nan\n", "\n", "rows=[]\n", "for col in vars_:\n", "    if col not in df.columns:\n", "        continue\n", "    g0: df.loc[df['readmit_30d']==0, col].dropna().values\n", "    g1: df.loc[df['readmit_30d']==1, col].dropna().values\n", "    rows.append({'variable': col, 'SMD': smd(g1, g0)})\n", "smd_tab = pd.DataFrame(rows).round(3)\n", "smd_tab\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "03e2b43c-ba10-4eb7-8f3c-fe8c0aee9963", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "418b2b3e-e639-47db-8f5d-68f5691963ff", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "anaconda-2024.02-py310", "language": "python", "name": "conda-env-anaconda-2024.02-py310-py"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 5}