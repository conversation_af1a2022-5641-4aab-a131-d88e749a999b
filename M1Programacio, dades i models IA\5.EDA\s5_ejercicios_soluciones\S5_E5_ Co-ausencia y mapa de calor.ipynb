{"cells": [{"cell_type": "markdown", "id": "0c76d4e0-bbb2-48a4-99cb-34ab8cdf7732", "metadata": {}, "source": ["Ejercicio 5: Co-ausencia (NA) y mapa de calor (avanzado)\n", "Calcula la matriz de co-ausencia (correlación entre indicadores NA) para un subconjunto de\n", "variables clínicas y visualízala con un heatmap en matplotlib."]}, {"cell_type": "code", "execution_count": 5, "id": "23d2ac0a-3fed-41ad-a401-94156352f938", "metadata": {}, "outputs": [], "source": ["import pandas as pd, numpy as np, os\n", "import matplotlib.pyplot as plt\n", "pd.set_option(\"display.max_columns\", 120)\n", "pd.set_option(\"display.width\", 160)\n", "df=pd.read_csv(\"../HOSPITAL_General_Sim.csv\")"]}, {"cell_type": "code", "execution_count": 6, "id": "9fe2ad2d-dd7c-45bb-97d7-ec8d41ace8a6", "metadata": {}, "outputs": [], "source": ["orden = ['<40','40-59','60-74','>=75']\n", "if \"edad_band\" in df.columns:\n", "    try: \n", "        from pandas.api.types import CategoricalDtype\n", "        dtype= CategoricalDtype(categories=orden, ordered=True)\n", "        df[\"edad_band\"] = df[\"edad_band\"].astype(dtype)\n", "    except Exception:\n", "        pass"]}, {"cell_type": "markdown", "id": "2412c5b1-ff6c-47ad-ad77-7311d0056d23", "metadata": {}, "source": ["# <PERSON><PERSON> de co-ausencia"]}, {"cell_type": "code", "execution_count": 9, "id": "9f98070b-55fb-4808-adca-5d8704aa87ff", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>hb_g_dl</th>\n", "      <th>wbc_10e9_per_l</th>\n", "      <th>creatinine_mg_dl</th>\n", "      <th>glucose_mg_dl</th>\n", "      <th>egfr_ml_min_1_73m2</th>\n", "      <th>sodium_mmol_l</th>\n", "      <th>potassium_mmol_l</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>hb_g_dl</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>wbc_10e9_per_l</th>\n", "      <td>NaN</td>\n", "      <td>1.000</td>\n", "      <td>-0.012</td>\n", "      <td>-0.013</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>creatinine_mg_dl</th>\n", "      <td>NaN</td>\n", "      <td>-0.012</td>\n", "      <td>1.000</td>\n", "      <td>-0.011</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>glucose_mg_dl</th>\n", "      <td>NaN</td>\n", "      <td>-0.013</td>\n", "      <td>-0.011</td>\n", "      <td>1.000</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>egfr_ml_min_1_73m2</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>sodium_mmol_l</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>potassium_mmol_l</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                    hb_g_dl  wbc_10e9_per_l  creatinine_mg_dl  glucose_mg_dl  egfr_ml_min_1_73m2  sodium_mmol_l  potassium_mmol_l\n", "hb_g_dl                 NaN             NaN               NaN            NaN                 NaN            NaN               NaN\n", "wbc_10e9_per_l          NaN           1.000            -0.012         -0.013                 NaN            NaN               NaN\n", "creatinine_mg_dl        NaN          -0.012             1.000         -0.011                 NaN            NaN               NaN\n", "glucose_mg_dl           NaN          -0.013            -0.011          1.000                 NaN            NaN               NaN\n", "egfr_ml_min_1_73m2      NaN             NaN               NaN            NaN                 NaN            NaN               NaN\n", "sodium_mmol_l           NaN             NaN               NaN            NaN                 NaN            NaN               NaN\n", "potassium_mmol_l        NaN             NaN               NaN            NaN                 NaN            NaN               NaN"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["subset = [c for c in ['hb_g_dl', 'wbc_10e9_per_l','creatinine_mg_dl','glucose_mg_dl',\n", "                      'egfr_ml_min_1_73m2','sodium_mmol_l','potassium_mmol_l'] if c in df.columns]\n", "na_ind = df[subset].isna().astype(int)\n", "coabs= na_ind.corr()\n", "coabs.round(3)"]}, {"cell_type": "markdown", "id": "f00da281-451b-415e-9f0a-69f1c774b58b", "metadata": {}, "source": ["### Mapa de calor simple "]}, {"cell_type": "code", "execution_count": 10, "id": "27a3e7d1-5227-4cbe-a231-c3a23f9353bd", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.figure()\n", "im = plt.imshow(coabs.values, vmin=0, vmax=1)\n", "plt.xticks(range(len(subset)), subset, rotation=45, ha='right')\n", "plt.yticks(range(len(subset)), subset)\n", "plt.colorbar(im, fraction=0.046, pad=0.04, label='Corr(NA)')\n", "plt.title('Co-ausencia (correlación entre NA)')\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "8b29b281-7c8e-413a-b00e-4289e0b3f621", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "anaconda-2024.02-py310", "language": "python", "name": "conda-env-anaconda-2024.02-py310-py"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 5}