{"cells": [{"cell_type": "markdown", "id": "ca31dd4b", "metadata": {}, "source": ["Ejercicio 1: Pipeline logístico + evaluación en test\n", "Entrena un pipeline con logística (imputación mediana, escalado, one-hot). Haz un split 80/20 estratificado. Evalúa ROC AUC, PR AUC y la matriz de confusión a umbral 0.5. Trae también las curvas ROC y PR (opcional)."]}, {"cell_type": "code", "execution_count": null, "id": "6d5d9781", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.compose import ColumnTransformer\n", "from sklearn.pipeline import Pipeline\n", "from sklearn.impute import SimpleImputer\n", "from sklearn.preprocessing import OneHotEncoder, StandardScaler\n", "from sklearn.linear_model import LogisticRegression\n", "from sklearn.metrics import roc_auc_score, average_precision_score, confusion_matrix, roc_curve, precision_recall_curve\n", "import matplotlib.pyplot as plt\n", "\n", "df = pd.read_csv(\"../../hospital_general_sim_min.csv\")\n", "\n", "y = df['readmit_30d'].astype(int)\n", "X = df.drop(columns=['readmit_30d'])\n", "\n"]}, {"cell_type": "code", "execution_count": 16, "id": "ddaa1dda", "metadata": {}, "outputs": [], "source": ["# Split estratificat 80/20\n", "X_train, X_test, y_train, y_test = train_test_split(\n", "    X, y, test_size=0.2, stratify=y, random_state=42\n", ")\n", "\n", "# Columnes numèriques i categòriques\n", "num_cols = X.select_dtypes(include=\"number\").columns.tolist()\n", "cat_cols = X.columns.difference(num_cols).tolist()\n", "\n"]}, {"cell_type": "code", "execution_count": 17, "id": "bd4cd44d", "metadata": {}, "outputs": [], "source": ["# Preprocessament\n", "preprocessor = ColumnTransformer([\n", "    (\"num\", Pipeline([\n", "        (\"imp\", SimpleImputer(strategy=\"median\")),\n", "        (\"sc\", StandardScaler())\n", "    ]), num_cols),\n", "    (\"cat\", <PERSON><PERSON><PERSON>([\n", "        (\"imp\", SimpleImputer(strategy=\"most_frequent\")),\n", "        (\"oh\", OneHotEncoder(handle_unknown=\"ignore\"))\n", "    ]), cat_cols)\n", "])\n", "\n", "# Pipeline amb logística\n", "log_clf = Pipeline([\n", "    (\"prep\", preprocessor),\n", "    (\"model\", LogisticRegression(max_iter=1000))\n", "])\n", "\n"]}, {"cell_type": "code", "execution_count": 18, "id": "0a418fd2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ROC AUC: 0.554\n", "PR AUC: 0.212\n", "<PERSON><PERSON> confu<PERSON> (umbral 0.5):\n", "[[33  0]\n", " [ 7  0]]\n"]}], "source": ["# Entrenem\n", "log_clf.fit(X_train, y_train)\n", "\n", "# Prediccions\n", "y_prob = log_clf.predict_proba(X_test)[:, 1]  # probabilitat classe 1\n", "y_pred = (y_prob >= 0.5).astype(int)         # classes amb llindar 0.5\n", "\n", "# Mètriques\n", "roc = roc_auc_score(y_test, y_prob)\n", "pr = average_precision_score(y_test, y_prob)\n", "cm = confusion_matrix(y_test, y_pred)\n", "\n", "print(f\"ROC AUC: {roc:.3f}\")\n", "print(f\"PR AUC: {pr:.3f}\")\n", "print(\"<PERSON><PERSON> de confusión (umbral 0.5):\")\n", "print(cm)\n", "\n"]}, {"cell_type": "code", "execution_count": 19, "id": "18a46847", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# (Opcional) Corbes ROC i PR\n", "fpr, tpr, _ = roc_curve(y_test, y_prob)\n", "prec, rec, _ = precision_recall_curve(y_test, y_prob)\n", "\n", "plt.plot(fpr, tpr)\n", "plt.xlabel(\"False Positive Rate\")\n", "plt.ylabel(\"True Positive Rate\")\n", "plt.title(\"ROC Curve\")\n", "plt.show()\n", "\n", "plt.plot(rec, prec)\n", "plt.xlabel(\"Recall\")\n", "plt.ylabel(\"Precision\")\n", "plt.title(\"Precision-Recall Curve\")\n", "plt.show()\n"]}], "metadata": {"kernelspec": {"display_name": "ia-bio", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}