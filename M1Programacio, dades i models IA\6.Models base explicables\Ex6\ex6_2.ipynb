{"cells": [{"cell_type": "markdown", "id": "567190fa", "metadata": {}, "source": ["Ejercicio 2: Comparación CV: logística vs árbol\n", "Compara LogisticRegression (con escalado) vs DecisionTreeClassifier (sin escalado) usando StratifiedKFold(5). Reporta ROC AUC y PR AUC medias (± desviación). Elige el mejor por PR AUC, ajústalo en train y evalúalo en test."]}, {"cell_type": "code", "execution_count": 32, "id": "6417245a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Llibreries i csv carregats\n"]}], "source": ["import pandas as pd, numpy as np, os\n", "import matplotlib.pyplot as plt\n", "from sklearn.model_selection import train_test_split, StratifiedKFold, cross_val_score\n", "from sklearn.compose import ColumnTransformer\n", "from sklearn.pipeline import Pipeline\n", "from sklearn.impute import SimpleImputer\n", "from sklearn.preprocessing import OneHotEncoder, StandardScaler\n", "from sklearn.linear_model import LogisticRegression\n", "from sklearn.tree import DecisionTreeClassifier\n", "from sklearn.metrics import (roc_auc_score, average_precision_score, roc_curve, confusion_matrix, brier_score_loss, precision_recall_curve)\n", "from sklearn.calibration import calibration_curve, CalibratedClassifierCV\n", "\n", "pd.set_option(\"display.max_columns\", 120)\n", "pd.set_option(\"display.width\", 160)\n", "\n", "df = pd.read_csv(\"../../hospital_general_sim_min.csv\")\n", "\n", "print(\"Llibreries i csv carregats\")\n"]}, {"cell_type": "code", "execution_count": 33, "id": "edf681c4", "metadata": {}, "outputs": [], "source": ["# Variable objectiu i predictors\n", "y = df[\"readmit_30d\"].astype(int)\n", "X = df.drop(columns=[\"readmit_30d\",\"episode_id\",\"patient_id\",\"admission_datetime\",\"discharge_datetime\"], errors=\"ignore\")\n"]}, {"cell_type": "code", "execution_count": 34, "id": "7f9f5e76", "metadata": {}, "outputs": [], "source": ["# Separació train/test estratificada\n", "X_train, X_test, y_train, y_test = train_test_split(\n", "    X, y, test_size=0.2, stratify=y, random_state=42\n", ")\n"]}, {"cell_type": "markdown", "id": "4a0873b6", "metadata": {}, "source": ["Preprocessament"]}, {"cell_type": "code", "execution_count": 35, "id": "13a4e2b6", "metadata": {}, "outputs": [], "source": ["# Numèriques i categòriques\n", "num_cols = X.select_dtypes(include=\"number\").columns.tolist()\n", "cat_cols = X.columns.difference(num_cols).tolist()\n", "\n", "# Per logística (imputació + escalat + one-hot)\n", "prep_log = ColumnTransformer([\n", "    (\"num\", Pipeline([\n", "        (\"imp\", SimpleImputer(strategy=\"median\")),\n", "        (\"sc\", StandardScaler())\n", "    ]), num_cols),\n", "    (\"cat\", <PERSON><PERSON><PERSON>([\n", "        (\"imp\", SimpleImputer(strategy=\"most_frequent\")),\n", "        (\"oh\", OneHotEncoder(handle_unknown=\"ignore\"))\n", "    ]), cat_cols)\n", "])\n", "\n", "# Per arbre (imputació + one-hot, sense escalat)\n", "prep_tree = ColumnTransformer([\n", "    (\"num\", SimpleImputer(strategy=\"median\"), num_cols),\n", "    (\"cat\", <PERSON><PERSON><PERSON>([\n", "        (\"imp\", SimpleImputer(strategy=\"most_frequent\")),\n", "        (\"oh\", OneHotEncoder(handle_unknown=\"ignore\"))\n", "    ]), cat_cols)\n", "])\n"]}, {"cell_type": "markdown", "id": "931e83b0", "metadata": {}, "source": ["Pipeline amb models"]}, {"cell_type": "code", "execution_count": 36, "id": "ab8cbfc0", "metadata": {}, "outputs": [], "source": ["log_clf = Pipeline([\n", "    (\"prep\", prep_log),\n", "    (\"model\", LogisticRegression(max_iter=1000))\n", "])\n", "\n", "tree_clf = Pipeline([\n", "    (\"prep\", prep_tree),\n", "    (\"model\", DecisionTreeClassifier(max_depth=4, min_samples_leaf=50, random_state=42))\n", "])"]}, {"cell_type": "markdown", "id": "902217e0", "metadata": {}, "source": ["Validació <PERSON>"]}, {"cell_type": "code", "execution_count": 37, "id": "8d26e368", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Logistic Regression --> ROC AUC CV: 0.557 +- 0.087 | PR AUC CV: 0.293 +- 0.087\n", "Decision Tree --> ROC AUC CV: 0.585 +- 0.043 | PR AUC CV: 0.213 +- 0.024\n"]}], "source": ["cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)\n", "\n", "results = {}\n", "for name, clf in [(\"Logistic Regression\", log_clf), (\"Decision Tree\", tree_clf)]:\n", "    auc = cross_val_score(clf, X_train, y_train, cv=cv, scoring=\"roc_auc\")\n", "    ap = cross_val_score(clf, X_train, y_train, cv=cv, scoring=\"average_precision\")\n", "    results[name] = {\"auc\": auc, \"ap\": ap}\n", "    print(f\"{name} --> ROC AUC CV: {auc.mean():.3f} +- {auc.std():.3f} | \" \n", "          f\"PR AUC CV: {ap.mean():.3f} +- {ap.std():.3f}\")\n", "    "]}, {"cell_type": "markdown", "id": "41ab2151", "metadata": {}, "source": ["Escollir millor model per PR AUC"]}, {"cell_type": "code", "execution_count": null, "id": "acb59020", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "->Millor model segons PR AUC CV: Logistic Regression\n"]}], "source": ["mean_pr_auc = {name: vals[\"ap\"].mean() for name, vals in results.items()}\n", "best_name = max(mean_pr_auc, key=mean_pr_auc.get)\n", "best_model = log_clf if best_name == \"Logistic Regression\" else tree_clf\n", "\n", "print(f\"\\nMillor model segons PR AUC CV: {best_name}\")"]}, {"cell_type": "markdown", "id": "a209b7b0", "metadata": {}, "source": ["Entrenar millor model amb train i avaluar en test\n"]}, {"cell_type": "code", "execution_count": null, "id": "bed09f26", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Resultats en TEST:\n", "ROC AUC test: 0.554\n", "PR AUC test: 0.214\n", "<PERSON><PERSON><PERSON> de confusió (umbral 0.5):\n", "[[33  0]\n", " [ 7  0]]\n"]}], "source": ["best_model.fit(X_train, y_train)\n", "y_pred_proba = best_model.predict_proba(X_test)[:, 1]\n", "y_pred = (y_pred_proba >= 0.5).astype(int)\n", "\n", "print(\"\\nResultats en TEST\")\n", "print(f\"ROC AUC test: {roc_auc_score(y_test, y_pred_proba):.3f}\")\n", "print(f\"PR AUC test: {average_precision_score(y_test, y_pred_proba):.3f}\")\n", "print(f\"<PERSON><PERSON><PERSON> de confusió (umbral 0.5):\\n{confusion_matrix(y_test, y_pred)}\")"]}], "metadata": {"kernelspec": {"display_name": "ia-bio", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}