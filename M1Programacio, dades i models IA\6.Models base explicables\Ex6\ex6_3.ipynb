{"cells": [{"cell_type": "markdown", "id": "935e478f", "metadata": {}, "source": ["Ejercicio 3: Selección de umbral\n", "Con la logística de (1), compara tres criterios de umbral en test: <PERSON><PERSON>, top-20% (capacidad), y coste FN:FP = 5:1. Para cada uno, muestra Sensibilidad, Especificidad, FP y FN."]}, {"cell_type": "code", "execution_count": 34, "id": "c74f2e58", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.preprocessing import StandardScaler, OneHotEncoder\n", "from sklearn.linear_model import LogisticRegression\n", "from sklearn.pipeline import Pipeline\n", "from sklearn.metrics import roc_curve, confusion_matrix\n", "from sklearn.compose import ColumnTransformer\n", "from sklearn.impute import SimpleImputer\n"]}, {"cell_type": "markdown", "id": "7cf290b3", "metadata": {}, "source": ["Partim del millor model (Logística) entrenat a l'exercici 1"]}, {"cell_type": "code", "execution_count": 35, "id": "d456b807", "metadata": {}, "outputs": [], "source": ["# Dad<PERSON> i split estratificat\n", "df = pd.read_csv(\"../../hospital_general_sim_min.csv\")\n", "X = df.drop(columns=[\"readmit_30d\", \"episode_id\", \"patient_id\", \"admission_datetime\", \"discharge_datetime\"], errors=\"ignore\")\n", "y = df[\"readmit_30d\"]\n", "\n", "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, stratify=y, random_state=42\n", ")\n"]}, {"cell_type": "code", "execution_count": 36, "id": "a68d5f0a", "metadata": {}, "outputs": [], "source": ["# Prepocessador amb ColumnTransformer\n", "num_cols = X.select_dtypes(include=[\"int64\", \"float64\"]).columns\n", "cat_cols = X.select_dtypes(include=[\"object\"]).columns\n", "\n", "prepocessor = ColumnTransformer([\n", "    (\"num\", Pipeline([\n", "        (\"imp\", SimpleImputer(strategy=\"median\")),\n", "        (\"sc\", StandardScaler())\n", "    ]), num_cols),\n", "    (\"cat\", <PERSON><PERSON><PERSON>([\n", "        (\"imp\", SimpleImputer(strategy=\"most_frequent\")),\n", "        (\"oh\", OneHotEncoder(handle_unknown=\"ignore\"))\n", "    ]), cat_cols)\n", "])\n", "\n"]}, {"cell_type": "code", "execution_count": 37, "id": "dc0bafca", "metadata": {}, "outputs": [{"data": {"text/html": ["<style>#sk-container-id-1 {\n", "  /* Definition of color scheme common for light and dark mode */\n", "  --sklearn-color-text: #000;\n", "  --sklearn-color-text-muted: #666;\n", "  --sklearn-color-line: gray;\n", "  /* Definition of color scheme for unfitted estimators */\n", "  --sklearn-color-unfitted-level-0: #fff5e6;\n", "  --sklearn-color-unfitted-level-1: #f6e4d2;\n", "  --sklearn-color-unfitted-level-2: #ffe0b3;\n", "  --sklearn-color-unfitted-level-3: chocolate;\n", "  /* Definition of color scheme for fitted estimators */\n", "  --sklearn-color-fitted-level-0: #f0f8ff;\n", "  --sklearn-color-fitted-level-1: #d4ebff;\n", "  --sklearn-color-fitted-level-2: #b3dbfd;\n", "  --sklearn-color-fitted-level-3: cornflowerblue;\n", "\n", "  /* Specific color for light theme */\n", "  --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, white)));\n", "  --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-icon: #696969;\n", "\n", "  @media (prefers-color-scheme: dark) {\n", "    /* Redefinition of color scheme for dark theme */\n", "    --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, #111)));\n", "    --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-icon: #878787;\n", "  }\n", "}\n", "\n", "#sk-container-id-1 {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "#sk-container-id-1 pre {\n", "  padding: 0;\n", "}\n", "\n", "#sk-container-id-1 input.sk-hidden--visually {\n", "  border: 0;\n", "  clip: rect(1px 1px 1px 1px);\n", "  clip: rect(1px, 1px, 1px, 1px);\n", "  height: 1px;\n", "  margin: -1px;\n", "  overflow: hidden;\n", "  padding: 0;\n", "  position: absolute;\n", "  width: 1px;\n", "}\n", "\n", "#sk-container-id-1 div.sk-dashed-wrapped {\n", "  border: 1px dashed var(--sklearn-color-line);\n", "  margin: 0 0.4em 0.5em 0.4em;\n", "  box-sizing: border-box;\n", "  padding-bottom: 0.4em;\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "#sk-container-id-1 div.sk-container {\n", "  /* jup<PERSON>r's `normalize.less` sets `[hidden] { display: none; }`\n", "     but bootstrap.min.css set `[hidden] { display: none !important; }`\n", "     so we also need the `!important` here to be able to override the\n", "     default hidden behavior on the sphinx rendered scikit-learn.org.\n", "     See: https://github.com/scikit-learn/scikit-learn/issues/21755 */\n", "  display: inline-block !important;\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-1 div.sk-text-repr-fallback {\n", "  display: none;\n", "}\n", "\n", "div.sk-parallel-item,\n", "div.sk-serial,\n", "div.sk-item {\n", "  /* draw centered vertical line to link estimators */\n", "  background-image: linear-gradient(var(--sklearn-color-text-on-default-background), var(--sklearn-color-text-on-default-background));\n", "  background-size: 2px 100%;\n", "  background-repeat: no-repeat;\n", "  background-position: center center;\n", "}\n", "\n", "/* Parallel-specific style estimator block */\n", "\n", "#sk-container-id-1 div.sk-parallel-item::after {\n", "  content: \"\";\n", "  width: 100%;\n", "  border-bottom: 2px solid var(--sklearn-color-text-on-default-background);\n", "  flex-grow: 1;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel {\n", "  display: flex;\n", "  align-items: stretch;\n", "  justify-content: center;\n", "  background-color: var(--sklearn-color-background);\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel-item {\n", "  display: flex;\n", "  flex-direction: column;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel-item:first-child::after {\n", "  align-self: flex-end;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel-item:last-child::after {\n", "  align-self: flex-start;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel-item:only-child::after {\n", "  width: 0;\n", "}\n", "\n", "/* Serial-specific style estimator block */\n", "\n", "#sk-container-id-1 div.sk-serial {\n", "  display: flex;\n", "  flex-direction: column;\n", "  align-items: center;\n", "  background-color: var(--sklearn-color-background);\n", "  padding-right: 1em;\n", "  padding-left: 1em;\n", "}\n", "\n", "\n", "/* Toggleable style: style used for estimator/Pipeline/ColumnTransformer box that is\n", "clickable and can be expanded/collapsed.\n", "- Pipeline and ColumnTransformer use this feature and define the default style\n", "- Estimators will overwrite some part of the style using the `sk-estimator` class\n", "*/\n", "\n", "/* Pipeline and ColumnTransformer style (default) */\n", "\n", "#sk-container-id-1 div.sk-toggleable {\n", "  /* Default theme specific background. It is overwritten whether we have a\n", "  specific estimator or a Pipeline/ColumnTransformer */\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "/* Toggleable label */\n", "#sk-container-id-1 label.sk-toggleable__label {\n", "  cursor: pointer;\n", "  display: flex;\n", "  width: 100%;\n", "  margin-bottom: 0;\n", "  padding: 0.5em;\n", "  box-sizing: border-box;\n", "  text-align: center;\n", "  align-items: start;\n", "  justify-content: space-between;\n", "  gap: 0.5em;\n", "}\n", "\n", "#sk-container-id-1 label.sk-toggleable__label .caption {\n", "  font-size: 0.6rem;\n", "  font-weight: lighter;\n", "  color: var(--sklearn-color-text-muted);\n", "}\n", "\n", "#sk-container-id-1 label.sk-toggleable__label-arrow:before {\n", "  /* <PERSON> on the left of the label */\n", "  content: \"▸\";\n", "  float: left;\n", "  margin-right: 0.25em;\n", "  color: var(--sklearn-color-icon);\n", "}\n", "\n", "#sk-container-id-1 label.sk-toggleable__label-arrow:hover:before {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "/* Toggleable content - dropdown */\n", "\n", "#sk-container-id-1 div.sk-toggleable__content {\n", "  display: none;\n", "  text-align: left;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 div.sk-toggleable__content.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 div.sk-toggleable__content pre {\n", "  margin: 0.2em;\n", "  border-radius: 0.25em;\n", "  color: var(--sklearn-color-text);\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 div.sk-toggleable__content.fitted pre {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 input.sk-toggleable__control:checked~div.sk-toggleable__content {\n", "  /* Expand drop-down */\n", "  display: block;\n", "  width: 100%;\n", "  overflow: visible;\n", "}\n", "\n", "#sk-container-id-1 input.sk-toggleable__control:checked~label.sk-toggleable__label-arrow:before {\n", "  content: \"▾\";\n", "}\n", "\n", "/* Pipeline/ColumnTransformer-specific style */\n", "\n", "#sk-container-id-1 div.sk-label input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-1 div.sk-label.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator-specific style */\n", "\n", "/* Colorize estimator box */\n", "#sk-container-id-1 div.sk-estimator input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-1 div.sk-estimator.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "#sk-container-id-1 div.sk-label label.sk-toggleable__label,\n", "#sk-container-id-1 div.sk-label label {\n", "  /* The background is the default theme color */\n", "  color: var(--sklearn-color-text-on-default-background);\n", "}\n", "\n", "/* On hover, darken the color of the background */\n", "#sk-container-id-1 div.sk-label:hover label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "/* Label box, darken color on hover, fitted */\n", "#sk-container-id-1 div.sk-label.fitted:hover label.sk-toggleable__label.fitted {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator label */\n", "\n", "#sk-container-id-1 div.sk-label label {\n", "  font-family: monospace;\n", "  font-weight: bold;\n", "  display: inline-block;\n", "  line-height: 1.2em;\n", "}\n", "\n", "#sk-container-id-1 div.sk-label-container {\n", "  text-align: center;\n", "}\n", "\n", "/* Estimator-specific */\n", "#sk-container-id-1 div.sk-estimator {\n", "  font-family: monospace;\n", "  border: 1px dotted var(--sklearn-color-border-box);\n", "  border-radius: 0.25em;\n", "  box-sizing: border-box;\n", "  margin-bottom: 0.5em;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 div.sk-estimator.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "/* on hover */\n", "#sk-container-id-1 div.sk-estimator:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-1 div.sk-estimator.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Specification for estimator info (e.g. \"i\" and \"?\") */\n", "\n", "/* Common style for \"i\" and \"?\" */\n", "\n", ".sk-estimator-doc-link,\n", "a:link.sk-estimator-doc-link,\n", "a:visited.sk-estimator-doc-link {\n", "  float: right;\n", "  font-size: smaller;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1em;\n", "  height: 1em;\n", "  width: 1em;\n", "  text-decoration: none !important;\n", "  margin-left: 0.5em;\n", "  text-align: center;\n", "  /* unfitted */\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted,\n", "a:link.sk-estimator-doc-link.fitted,\n", "a:visited.sk-estimator-doc-link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "div.sk-estimator:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "div.sk-estimator.fitted:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "/* Span, style for the box shown on hovering the info icon */\n", ".sk-estimator-doc-link span {\n", "  display: none;\n", "  z-index: 9999;\n", "  position: relative;\n", "  font-weight: normal;\n", "  right: .2ex;\n", "  padding: .5ex;\n", "  margin: .5ex;\n", "  width: min-content;\n", "  min-width: 20ex;\n", "  max-width: 50ex;\n", "  color: var(--sklearn-color-text);\n", "  box-shadow: 2pt 2pt 4pt #999;\n", "  /* unfitted */\n", "  background: var(--sklearn-color-unfitted-level-0);\n", "  border: .5pt solid var(--sklearn-color-unfitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted span {\n", "  /* fitted */\n", "  background: var(--sklearn-color-fitted-level-0);\n", "  border: var(--sklearn-color-fitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link:hover span {\n", "  display: block;\n", "}\n", "\n", "/* \"?\"-specific style due to the `<a>` HTML tag */\n", "\n", "#sk-container-id-1 a.estimator_doc_link {\n", "  float: right;\n", "  font-size: 1rem;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1rem;\n", "  height: 1rem;\n", "  width: 1rem;\n", "  text-decoration: none;\n", "  /* unfitted */\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "}\n", "\n", "#sk-container-id-1 a.estimator_doc_link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "#sk-container-id-1 a.estimator_doc_link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "#sk-container-id-1 a.estimator_doc_link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "}\n", "\n", ".estimator-table summary {\n", "    padding: .5rem;\n", "    font-family: monospace;\n", "    cursor: pointer;\n", "}\n", "\n", ".estimator-table details[open] {\n", "    padding-left: 0.1rem;\n", "    padding-right: 0.1rem;\n", "    padding-bottom: 0.3rem;\n", "}\n", "\n", ".estimator-table .parameters-table {\n", "    margin-left: auto !important;\n", "    margin-right: auto !important;\n", "}\n", "\n", ".estimator-table .parameters-table tr:nth-child(odd) {\n", "    background-color: #fff;\n", "}\n", "\n", ".estimator-table .parameters-table tr:nth-child(even) {\n", "    background-color: #f6f6f6;\n", "}\n", "\n", ".estimator-table .parameters-table tr:hover {\n", "    background-color: #e0e0e0;\n", "}\n", "\n", ".estimator-table table td {\n", "    border: 1px solid rgba(106, 105, 104, 0.232);\n", "}\n", "\n", ".user-set td {\n", "    color:rgb(255, 94, 0);\n", "    text-align: left;\n", "}\n", "\n", ".user-set td.value pre {\n", "    color:rgb(255, 94, 0) !important;\n", "    background-color: transparent !important;\n", "}\n", "\n", ".default td {\n", "    color: black;\n", "    text-align: left;\n", "}\n", "\n", ".user-set td i,\n", ".default td i {\n", "    color: black;\n", "}\n", "\n", ".copy-paste-icon {\n", "    background-image: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCA0NDggNTEyIj48IS0tIUZvbnQgQXdlc29tZSBGcmVlIDYuNy4yIGJ5IEBmb250YXdlc29tZSAtIGh0dHBzOi8vZm9udGF3ZXNvbWUuY29tIExpY2Vuc2UgLSBodHRwczovL2ZvbnRhd2Vzb21lLmNvbS9saWNlbnNlL2ZyZWUgQ29weXJpZ2h0IDIwMjUgRm9udGljb25zLCBJbmMuLS0+PHBhdGggZD0iTTIwOCAwTDMzMi4xIDBjMTIuNyAwIDI0LjkgNS4xIDMzLjkgMTQuMWw2Ny45IDY3LjljOSA5IDE0LjEgMjEuMiAxNC4xIDMzLjlMNDQ4IDMzNmMwIDI2LjUtMjEuNSA0OC00OCA0OGwtMTkyIDBjLTI2LjUgMC00OC0yMS41LTQ4LTQ4bDAtMjg4YzAtMjYuNSAyMS41LTQ4IDQ4LTQ4ek00OCAxMjhsODAgMCAwIDY0LTY0IDAgMCAyNTYgMTkyIDAgMC0zMiA2NCAwIDAgNDhjMCAyNi41LTIxLjUgNDgtNDggNDhMNDggNTEyYy0yNi41IDAtNDgtMjEuNS00OC00OEwwIDE3NmMwLTI2LjUgMjEuNS00OCA0OC00OHoiLz48L3N2Zz4=);\n", "    background-repeat: no-repeat;\n", "    background-size: 14px 14px;\n", "    background-position: 0;\n", "    display: inline-block;\n", "    width: 14px;\n", "    height: 14px;\n", "    cursor: pointer;\n", "}\n", "</style><body><div id=\"sk-container-id-1\" class=\"sk-top-container\"><div class=\"sk-text-repr-fallback\"><pre>Pipeline(steps=[(&#x27;prepocessor&#x27;,\n", "                 ColumnTransformer(transformers=[(&#x27;num&#x27;,\n", "                                                  Pipeline(steps=[(&#x27;imp&#x27;,\n", "                                                                   SimpleImputer(strategy=&#x27;median&#x27;)),\n", "                                                                  (&#x27;sc&#x27;,\n", "                                                                   StandardScaler())]),\n", "                                                  Index([&#x27;length_of_stay_days&#x27;, &#x27;age_years&#x27;, &#x27;bmi&#x27;, &#x27;hr_bpm&#x27;, &#x27;sbp_mmhg&#x27;,\n", "       &#x27;spo2_pct&#x27;, &#x27;temp_c&#x27;, &#x27;hb_g_dl&#x27;, &#x27;creatinine_mg_dl&#x27;, &#x27;glucose_mg_dl&#x27;,\n", "       &#x27;wbc_10e9_per_l&#x27;, &#x27;egfr_ml_min_1_73m2&#x27;],\n", "      dtype=&#x27;object&#x27;)),\n", "                                                 (&#x27;cat&#x27;,\n", "                                                  Pipeline(steps=[(&#x27;imp&#x27;,\n", "                                                                   SimpleImputer(strategy=&#x27;most_frequent&#x27;)),\n", "                                                                  (&#x27;oh&#x27;,\n", "                                                                   OneHotEncoder(handle_unknown=&#x27;ignore&#x27;))]),\n", "                                                  Index([&#x27;sex&#x27;, &#x27;edad_band&#x27;], dtype=&#x27;object&#x27;))])),\n", "                (&#x27;log_reg&#x27;,\n", "                 LogisticRegression(max_iter=1000, solver=&#x27;liblinear&#x27;))])</pre><b>In a Jupyter environment, please rerun this cell to show the HTML representation or trust the notebook. <br />On GitHub, the HTML representation is unable to render, please try loading this page with nbviewer.org.</b></div><div class=\"sk-container\" hidden><div class=\"sk-item sk-dashed-wrapped\"><div class=\"sk-label-container\"><div class=\"sk-label fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-1\" type=\"checkbox\" ><label for=\"sk-estimator-id-1\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow\"><div><div>Pipeline</div></div><div><a class=\"sk-estimator-doc-link fitted\" rel=\"noreferrer\" target=\"_blank\" href=\"https://scikit-learn.org/1.7/modules/generated/sklearn.pipeline.Pipeline.html\">?<span>Documentation for Pipeline</span></a><span class=\"sk-estimator-doc-link fitted\">i<span>Fitted</span></span></div></label><div class=\"sk-toggleable__content fitted\" data-param-prefix=\"\">\n", "        <div class=\"estimator-table\">\n", "            <details>\n", "                <summary>Parameters</summary>\n", "                <table class=\"parameters-table\">\n", "                  <tbody>\n", "                    \n", "        <tr class=\"user-set\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('steps',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">steps&nbsp;</td>\n", "            <td class=\"value\">[(&#x27;prepocessor&#x27;, ...), (&#x27;log_reg&#x27;, ...)]</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('transform_input',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">transform_input&nbsp;</td>\n", "            <td class=\"value\">None</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('memory',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">memory&nbsp;</td>\n", "            <td class=\"value\">None</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('verbose',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">verbose&nbsp;</td>\n", "            <td class=\"value\">False</td>\n", "        </tr>\n", "    \n", "                  </tbody>\n", "                </table>\n", "            </details>\n", "        </div>\n", "    </div></div></div><div class=\"sk-serial\"><div class=\"sk-item sk-dashed-wrapped\"><div class=\"sk-label-container\"><div class=\"sk-label fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-2\" type=\"checkbox\" ><label for=\"sk-estimator-id-2\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow\"><div><div>prepocessor: ColumnTransformer</div></div><div><a class=\"sk-estimator-doc-link fitted\" rel=\"noreferrer\" target=\"_blank\" href=\"https://scikit-learn.org/1.7/modules/generated/sklearn.compose.ColumnTransformer.html\">?<span>Documentation for prepocessor: ColumnTransformer</span></a></div></label><div class=\"sk-toggleable__content fitted\" data-param-prefix=\"prepocessor__\">\n", "        <div class=\"estimator-table\">\n", "            <details>\n", "                <summary>Parameters</summary>\n", "                <table class=\"parameters-table\">\n", "                  <tbody>\n", "                    \n", "        <tr class=\"user-set\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('transformers',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">transformers&nbsp;</td>\n", "            <td class=\"value\">[(&#x27;num&#x27;, ...), (&#x27;cat&#x27;, ...)]</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('remainder',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">remainder&nbsp;</td>\n", "            <td class=\"value\">&#x27;drop&#x27;</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('sparse_threshold',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">sparse_threshold&nbsp;</td>\n", "            <td class=\"value\">0.3</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('n_jobs',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">n_jobs&nbsp;</td>\n", "            <td class=\"value\">None</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('transformer_weights',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">transformer_weights&nbsp;</td>\n", "            <td class=\"value\">None</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('verbose',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">verbose&nbsp;</td>\n", "            <td class=\"value\">False</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('verbose_feature_names_out',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">verbose_feature_names_out&nbsp;</td>\n", "            <td class=\"value\">True</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('force_int_remainder_cols',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">force_int_remainder_cols&nbsp;</td>\n", "            <td class=\"value\">&#x27;deprecated&#x27;</td>\n", "        </tr>\n", "    \n", "                  </tbody>\n", "                </table>\n", "            </details>\n", "        </div>\n", "    </div></div></div><div class=\"sk-parallel\"><div class=\"sk-parallel-item\"><div class=\"sk-item\"><div class=\"sk-label-container\"><div class=\"sk-label fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-3\" type=\"checkbox\" ><label for=\"sk-estimator-id-3\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow\"><div><div>num</div></div></label><div class=\"sk-toggleable__content fitted\" data-param-prefix=\"prepocessor__num__\"><pre>Index([&#x27;length_of_stay_days&#x27;, &#x27;age_years&#x27;, &#x27;bmi&#x27;, &#x27;hr_bpm&#x27;, &#x27;sbp_mmhg&#x27;,\n", "       &#x27;spo2_pct&#x27;, &#x27;temp_c&#x27;, &#x27;hb_g_dl&#x27;, &#x27;creatinine_mg_dl&#x27;, &#x27;glucose_mg_dl&#x27;,\n", "       &#x27;wbc_10e9_per_l&#x27;, &#x27;egfr_ml_min_1_73m2&#x27;],\n", "      dtype=&#x27;object&#x27;)</pre></div></div></div><div class=\"sk-serial\"><div class=\"sk-item\"><div class=\"sk-serial\"><div class=\"sk-item\"><div class=\"sk-estimator fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-4\" type=\"checkbox\" ><label for=\"sk-estimator-id-4\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow\"><div><div>SimpleImputer</div></div><div><a class=\"sk-estimator-doc-link fitted\" rel=\"noreferrer\" target=\"_blank\" href=\"https://scikit-learn.org/1.7/modules/generated/sklearn.impute.SimpleImputer.html\">?<span>Documentation for SimpleImputer</span></a></div></label><div class=\"sk-toggleable__content fitted\" data-param-prefix=\"prepocessor__num__imp__\">\n", "        <div class=\"estimator-table\">\n", "            <details>\n", "                <summary>Parameters</summary>\n", "                <table class=\"parameters-table\">\n", "                  <tbody>\n", "                    \n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('missing_values',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">missing_values&nbsp;</td>\n", "            <td class=\"value\">nan</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"user-set\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('strategy',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">strategy&nbsp;</td>\n", "            <td class=\"value\">&#x27;median&#x27;</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('fill_value',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">fill_value&nbsp;</td>\n", "            <td class=\"value\">None</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('copy',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">copy&nbsp;</td>\n", "            <td class=\"value\">True</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('add_indicator',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">add_indicator&nbsp;</td>\n", "            <td class=\"value\">False</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('keep_empty_features',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">keep_empty_features&nbsp;</td>\n", "            <td class=\"value\">False</td>\n", "        </tr>\n", "    \n", "                  </tbody>\n", "                </table>\n", "            </details>\n", "        </div>\n", "    </div></div></div><div class=\"sk-item\"><div class=\"sk-estimator fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-5\" type=\"checkbox\" ><label for=\"sk-estimator-id-5\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow\"><div><div>StandardScaler</div></div><div><a class=\"sk-estimator-doc-link fitted\" rel=\"noreferrer\" target=\"_blank\" href=\"https://scikit-learn.org/1.7/modules/generated/sklearn.preprocessing.StandardScaler.html\">?<span>Documentation for StandardScaler</span></a></div></label><div class=\"sk-toggleable__content fitted\" data-param-prefix=\"prepocessor__num__sc__\">\n", "        <div class=\"estimator-table\">\n", "            <details>\n", "                <summary>Parameters</summary>\n", "                <table class=\"parameters-table\">\n", "                  <tbody>\n", "                    \n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('copy',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">copy&nbsp;</td>\n", "            <td class=\"value\">True</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('with_mean',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">with_mean&nbsp;</td>\n", "            <td class=\"value\">True</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('with_std',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">with_std&nbsp;</td>\n", "            <td class=\"value\">True</td>\n", "        </tr>\n", "    \n", "                  </tbody>\n", "                </table>\n", "            </details>\n", "        </div>\n", "    </div></div></div></div></div></div></div></div><div class=\"sk-parallel-item\"><div class=\"sk-item\"><div class=\"sk-label-container\"><div class=\"sk-label fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-6\" type=\"checkbox\" ><label for=\"sk-estimator-id-6\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow\"><div><div>cat</div></div></label><div class=\"sk-toggleable__content fitted\" data-param-prefix=\"prepocessor__cat__\"><pre>Index([&#x27;sex&#x27;, &#x27;edad_band&#x27;], dtype=&#x27;object&#x27;)</pre></div></div></div><div class=\"sk-serial\"><div class=\"sk-item\"><div class=\"sk-serial\"><div class=\"sk-item\"><div class=\"sk-estimator fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-7\" type=\"checkbox\" ><label for=\"sk-estimator-id-7\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow\"><div><div>SimpleImputer</div></div><div><a class=\"sk-estimator-doc-link fitted\" rel=\"noreferrer\" target=\"_blank\" href=\"https://scikit-learn.org/1.7/modules/generated/sklearn.impute.SimpleImputer.html\">?<span>Documentation for SimpleImputer</span></a></div></label><div class=\"sk-toggleable__content fitted\" data-param-prefix=\"prepocessor__cat__imp__\">\n", "        <div class=\"estimator-table\">\n", "            <details>\n", "                <summary>Parameters</summary>\n", "                <table class=\"parameters-table\">\n", "                  <tbody>\n", "                    \n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('missing_values',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">missing_values&nbsp;</td>\n", "            <td class=\"value\">nan</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"user-set\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('strategy',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">strategy&nbsp;</td>\n", "            <td class=\"value\">&#x27;most_frequent&#x27;</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('fill_value',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">fill_value&nbsp;</td>\n", "            <td class=\"value\">None</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('copy',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">copy&nbsp;</td>\n", "            <td class=\"value\">True</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('add_indicator',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">add_indicator&nbsp;</td>\n", "            <td class=\"value\">False</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('keep_empty_features',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">keep_empty_features&nbsp;</td>\n", "            <td class=\"value\">False</td>\n", "        </tr>\n", "    \n", "                  </tbody>\n", "                </table>\n", "            </details>\n", "        </div>\n", "    </div></div></div><div class=\"sk-item\"><div class=\"sk-estimator fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-8\" type=\"checkbox\" ><label for=\"sk-estimator-id-8\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow\"><div><div>OneHotEncoder</div></div><div><a class=\"sk-estimator-doc-link fitted\" rel=\"noreferrer\" target=\"_blank\" href=\"https://scikit-learn.org/1.7/modules/generated/sklearn.preprocessing.OneHotEncoder.html\">?<span>Documentation for OneHotEncoder</span></a></div></label><div class=\"sk-toggleable__content fitted\" data-param-prefix=\"prepocessor__cat__oh__\">\n", "        <div class=\"estimator-table\">\n", "            <details>\n", "                <summary>Parameters</summary>\n", "                <table class=\"parameters-table\">\n", "                  <tbody>\n", "                    \n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('categories',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">categories&nbsp;</td>\n", "            <td class=\"value\">&#x27;auto&#x27;</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('drop',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">drop&nbsp;</td>\n", "            <td class=\"value\">None</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('sparse_output',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">sparse_output&nbsp;</td>\n", "            <td class=\"value\">True</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('dtype',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">dtype&nbsp;</td>\n", "            <td class=\"value\">&lt;class &#x27;numpy.float64&#x27;&gt;</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"user-set\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('handle_unknown',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">handle_unknown&nbsp;</td>\n", "            <td class=\"value\">&#x27;ignore&#x27;</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('min_frequency',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">min_frequency&nbsp;</td>\n", "            <td class=\"value\">None</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('max_categories',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">max_categories&nbsp;</td>\n", "            <td class=\"value\">None</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('feature_name_combiner',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">feature_name_combiner&nbsp;</td>\n", "            <td class=\"value\">&#x27;concat&#x27;</td>\n", "        </tr>\n", "    \n", "                  </tbody>\n", "                </table>\n", "            </details>\n", "        </div>\n", "    </div></div></div></div></div></div></div></div></div></div><div class=\"sk-item\"><div class=\"sk-estimator fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-9\" type=\"checkbox\" ><label for=\"sk-estimator-id-9\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow\"><div><div>LogisticRegression</div></div><div><a class=\"sk-estimator-doc-link fitted\" rel=\"noreferrer\" target=\"_blank\" href=\"https://scikit-learn.org/1.7/modules/generated/sklearn.linear_model.LogisticRegression.html\">?<span>Documentation for LogisticRegression</span></a></div></label><div class=\"sk-toggleable__content fitted\" data-param-prefix=\"log_reg__\">\n", "        <div class=\"estimator-table\">\n", "            <details>\n", "                <summary>Parameters</summary>\n", "                <table class=\"parameters-table\">\n", "                  <tbody>\n", "                    \n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('penalty',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">penalty&nbsp;</td>\n", "            <td class=\"value\">&#x27;l2&#x27;</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('dual',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">dual&nbsp;</td>\n", "            <td class=\"value\">False</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('tol',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">tol&nbsp;</td>\n", "            <td class=\"value\">0.0001</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('C',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">C&nbsp;</td>\n", "            <td class=\"value\">1.0</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('fit_intercept',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">fit_intercept&nbsp;</td>\n", "            <td class=\"value\">True</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('intercept_scaling',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">intercept_scaling&nbsp;</td>\n", "            <td class=\"value\">1</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('class_weight',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">class_weight&nbsp;</td>\n", "            <td class=\"value\">None</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('random_state',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">random_state&nbsp;</td>\n", "            <td class=\"value\">None</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"user-set\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('solver',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">solver&nbsp;</td>\n", "            <td class=\"value\">&#x27;liblinear&#x27;</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"user-set\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('max_iter',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">max_iter&nbsp;</td>\n", "            <td class=\"value\">1000</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('multi_class',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">multi_class&nbsp;</td>\n", "            <td class=\"value\">&#x27;deprecated&#x27;</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('verbose',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">verbose&nbsp;</td>\n", "            <td class=\"value\">0</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('warm_start',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">warm_start&nbsp;</td>\n", "            <td class=\"value\">False</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('n_jobs',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">n_jobs&nbsp;</td>\n", "            <td class=\"value\">None</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('l1_ratio',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">l1_ratio&nbsp;</td>\n", "            <td class=\"value\">None</td>\n", "        </tr>\n", "    \n", "                  </tbody>\n", "                </table>\n", "            </details>\n", "        </div>\n", "    </div></div></div></div></div></div></div><script>function copyToClipboard(text, element) {\n", "    // Get the parameter prefix from the closest toggleable content\n", "    const toggleableContent = element.closest('.sk-toggleable__content');\n", "    const paramPrefix = toggleableContent ? toggleableContent.dataset.paramPrefix : '';\n", "    const fullParamName = paramPrefix ? `${paramPrefix}${text}` : text;\n", "\n", "    const originalStyle = element.style;\n", "    const computedStyle = window.getComputedStyle(element);\n", "    const originalWidth = computedStyle.width;\n", "    const originalHTML = element.innerHTML.replace('Copied!', '');\n", "\n", "    navigator.clipboard.writeText(fullParamName)\n", "        .then(() => {\n", "            element.style.width = originalWidth;\n", "            element.style.color = 'green';\n", "            element.innerHTML = \"Copied!\";\n", "\n", "            setTimeout(() => {\n", "                element.innerHTML = originalHTML;\n", "                element.style = originalStyle;\n", "            }, 2000);\n", "        })\n", "        .catch(err => {\n", "            console.error('Failed to copy:', err);\n", "            element.style.color = 'red';\n", "            element.innerHTML = \"Failed!\";\n", "            setTimeout(() => {\n", "                element.innerHTML = originalHTML;\n", "                element.style = originalStyle;\n", "            }, 2000);\n", "        });\n", "    return false;\n", "}\n", "\n", "document.querySelectorAll('.fa-regular.fa-copy').forEach(function(element) {\n", "    const toggleableContent = element.closest('.sk-toggleable__content');\n", "    const paramPrefix = toggleableContent ? toggleableContent.dataset.paramPrefix : '';\n", "    const paramName = element.parentElement.nextElementSibling.textContent.trim();\n", "    const fullParamName = paramPrefix ? `${paramPrefix}${paramName}` : paramName;\n", "\n", "    element.setAttribute('title', fullParamName);\n", "});\n", "</script></body>"], "text/plain": ["Pipeline(steps=[('prepocessor',\n", "                 ColumnTransformer(transformers=[('num',\n", "                                                  Pipeline(steps=[('imp',\n", "                                                                   SimpleImputer(strategy='median')),\n", "                                                                  ('sc',\n", "                                                                   StandardScaler())]),\n", "                                                  Index(['length_of_stay_days', 'age_years', 'bmi', 'hr_bpm', 'sbp_mmhg',\n", "       'spo2_pct', 'temp_c', 'hb_g_dl', 'creatinine_mg_dl', 'glucose_mg_dl',\n", "       'wbc_10e9_per_l', 'egfr_ml_min_1_73m2'],\n", "      dtype='object')),\n", "                                                 ('cat',\n", "                                                  Pipeline(steps=[('imp',\n", "                                                                   SimpleImputer(strategy='most_frequent')),\n", "                                                                  ('oh',\n", "                                                                   OneHotEncoder(handle_unknown='ignore'))]),\n", "                                                  Index(['sex', 'edad_band'], dtype='object'))])),\n", "                ('log_reg',\n", "                 LogisticRegression(max_iter=1000, solver='liblinear'))])"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["# Entrenem la logística\n", "log_clf = Pipeline([\n", "    (\"prepocessor\", prepocessor),\n", "    (\"log_reg\", LogisticRegression(max_iter=1000, solver=\"liblinear\"))\n", "])\n", "log_clf.fit(X_train, y_train)\n", "\n"]}, {"cell_type": "code", "execution_count": 38, "id": "0604b7c3", "metadata": {}, "outputs": [], "source": ["# Prediccions de probabilitat\n", "y_proba = log_clf.predict_proba(X_test)[:, 1]\n", "\n", "def get_metrics_at_threshold(y_true, y_proba, thr, labels=''):\n", "    y_pred = (y_proba >= thr).astype(int)\n", "    tn, fp, fn, tp = confusion_matrix(y_true, y_pred).ravel()\n", "    sens = tp / (tp + fn) if (tp + fn) > 0 else 0\n", "    spec = tn / (tn + fp) if (tn + fp) > 0 else 0\n", "    return {\n", "        \"criteri\": thr,\n", "        \"sensibilitat\": sens,\n", "        \"especificitat\": spec,\n", "        \"FP\": fp,\n", "        \"FN\": fn\n", "    }"]}, {"cell_type": "code", "execution_count": 39, "id": "7f53826f", "metadata": {}, "outputs": [], "source": ["# Youden\n", "fpr, tpr, thr_list = roc_curve(y_test, y_proba)\n", "youden_idx = np.argmax(tpr - fpr)\n", "thr_youden = thr_list[youden_idx]\n", "y_pred_youden = (y_proba >= th_youden).astype(int)\n", "res_youden = get_metrics_at_threshold(y_test, y_proba, y_pred_youden, \"Youden\")"]}, {"cell_type": "code", "execution_count": 40, "id": "8f398fde", "metadata": {}, "outputs": [], "source": ["#Top-20% (capacitat)\n", "th_top20 = np.percentile(y_proba, 80)  # 20% més alt\n", "y_pred_top20 = (y_proba >= th_top20).astype(int)\n", "res_top20 = get_metrics_at_threshold(y_test, y_proba, y_pred_top20, \"Top-20%\")"]}, {"cell_type": "code", "execution_count": 41, "id": "df92ad24", "metadata": {"notebookRunGroups": {"groupValue": "1"}}, "outputs": [], "source": ["# Cost FN:FP = 5:1\n", "best_cost = np.inf\n", "best_th = 0.5\n", "for th in np.linspace(0,1,200):\n", "    y_pred = (y_proba >= th).astype(int)\n", "    tn, fp, fn, tp = confusion_matrix(y_test, y_pred).ravel()\n", "    cost = 5*fn + 1*fp\n", "    if cost < best_cost:\n", "        best_cost = cost\n", "        best_th = th\n", "th_cost = best_th\n", "y_pred_cost = (y_proba >= th_cost).astype(int)\n", "res_cost = get_metrics_at_threshold(y_test, y_proba, y_pred_cost, \"Cost FN:FP=5:1\")"]}, {"cell_type": "code", "execution_count": 42, "id": "095fa568", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                                             criteri  sensibilitat  \\\n", "0  [0, 0, 1, 0, 1, 0, 0, 1, 0, 1, 1, 1, 1, 0, 1, ...      0.142857   \n", "1  [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 0, 1, ...      1.000000   \n", "2  [0, 0, 1, 0, 1, 0, 0, 1, 0, 1, 1, 1, 1, 0, 1, ...      0.142857   \n", "\n", "   especificitat  FP  FN  \n", "0       0.575758  14   6  \n", "1       0.242424  25   0  \n", "2       0.575758  14   6  \n", "\n", "Llindars trobats:\n", "Youden: 0.156\n", "Top-20%: 0.261\n", "Cost 5:1: 0.151\n"]}], "source": ["# Mostrar resultats\n", "import pandas as pd\n", "results = pd.DataFrame([res_youden, res_top20, res_cost])\n", "print(results)\n", "print(\"\\nLlindars trobats:\")\n", "print(f\"Youden: {th_youden:.3f}\")\n", "print(f\"Top-20%: {th_top20:.3f}\")\n", "print(f\"Cost 5:1: {th_cost:.3f}\")\n"]}], "metadata": {"kernelspec": {"display_name": "ia-bio", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}