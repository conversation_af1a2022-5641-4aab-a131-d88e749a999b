{"cells": [{"cell_type": "markdown", "id": "7470e538-9351-4cfd-b991-fbec6ac5af97", "metadata": {}, "source": ["Ejercicio 1: Pipeline logístico + evaluación en test\n", "Entrena un pipeline con logística (imputación mediana, escalado, one-hot). Haz un split 80/20\n", "estratificado. Evalúa ROC AUC, PR AUC y la matriz de confusión a umbral 0.5. Trae también las\n", "curvas ROC y PR (opcional)."]}, {"cell_type": "code", "execution_count": 3, "id": "ac319d0b-07c9-4065-9970-9502ac661a3b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Librerias y csv cargados\n"]}], "source": ["import pandas as pd, numpy as np, os\n", "import matplotlib.pyplot as plt\n", "from sklearn.model_selection import train_test_split, StratifiedKFold, cross_val_score\n", "from sklearn.compose import ColumnTransformer\n", "from sklearn.pipeline import Pipeline\n", "from sklearn.impute import SimpleImputer\n", "from sklearn.preprocessing import OneHotEncoder, StandardScaler\n", "from sklearn.linear_model import LogisticRegression\n", "from sklearn.tree import DecisionTreeClassifier\n", "from sklearn.metrics import (roc_auc_score, average_precision_score, roc_curve,\n", "                             confusion_matrix, brier_score_loss, precision_recall_curve)\n", "from sklearn.calibration import calibration_curve, CalibratedClassifierCV\n", "\n", "pd.set_option(\"display.max_columns\", 120)\n", "pd.set_option(\"display.width\", 160)\n", "\n", "df=pd.read_csv(\"HOSPITAL_General_Sim_min.csv\")\n", "print(\"Librerias y csv cargados\")"]}, {"cell_type": "code", "execution_count": 5, "id": "7aea6511-2793-4937-ad15-7529c2fa08ae", "metadata": {}, "outputs": [], "source": ["# Variable objetivo y precictores\n", "y= df[\"readmit_30d\"].astype(int)\n", "X = df.drop(columns=[\"readmit_30d\",\"episode_id\",\"patient_id\",\"admission_datetime\",\"discharge_datetime\"], errors=\"ignore\")"]}, {"cell_type": "code", "execution_count": 7, "id": "5b4941a3-d763-439e-8fad-90cc4ad6056d", "metadata": {}, "outputs": [], "source": ["# Tipos\n", "num_cols= X.select_dtypes(include=\"number\").columns.tolist()\n", "cat_cols= X.columns.difference(num_cols).tolist()"]}, {"cell_type": "code", "execution_count": 9, "id": "682c7384-5ce1-41aa-aa00-4651a1a7f1ad", "metadata": {}, "outputs": [], "source": ["# funcion para crear columna edad_band (si no existe)\n", "if \"edad_band\" not in df.columns and \"age_years\" in df.columns:\n", "    def _edad_band(x):\n", "        if pd.isna(x): return pd.NA\n", "        if x < 40: return \"<40\"\n", "        if x < 60: return \"40-59\"\n", "        if x < 75: return \"60-74\"\n", "        return \">=75\"\n", "    df[\"edad_band\"] = df[\"age_years\"].map(_edad_band)"]}, {"cell_type": "code", "execution_count": 11, "id": "06b9a418-d174-4a5f-bf8b-b27df9579777", "metadata": {}, "outputs": [], "source": ["# Preprocesado de los datos\n", "prep_log = ColumnTransformer([\n", "    (\"num\", Pipeline([(\"imp\", SimpleImputer(strategy=\"median\")),\n", "                      (\"sc\", StandardScaler())]), num_cols),\n", "    (\"cat\", Pipeline([(\"imp\", SimpleImputer(strategy=\"most_frequent\")),\n", "                      (\"oh\", OneHotEncoder(handle_unknown=\"ignore\"))]), cat_cols)\n", "])"]}, {"cell_type": "code", "execution_count": 12, "id": "593bccd2-212f-44ec-938a-a2ead93870ed", "metadata": {}, "outputs": [], "source": ["prep_tree = ColumnTransformer([\n", "    (\"num\", SimpleImputer(strategy=\"median\"), num_cols),\n", "    (\"cat\", Pipeline([(\"imp\", SimpleImputer(strategy=\"most_frequent\")),\n", "                      (\"oh\", OneHotEncoder(handle_unknown=\"ignore\"))]), cat_cols)\n", "])"]}, {"cell_type": "code", "execution_count": 13, "id": "a35625dd-8e27-4ff3-a175-a88f037283e3", "metadata": {}, "outputs": [], "source": ["### Split, entrenamiento y métricas en test"]}, {"cell_type": "code", "execution_count": 15, "id": "a32c0e41-6857-4a2e-a332-76206a0cfff4", "metadata": {}, "outputs": [], "source": ["clf = Pipeline([(\"prep\", prep_log), (\"model\", LogisticRegression(max_iter=1000))])\n", "X_train, X_test, y_train, y_test =train_test_split(X, y, test_size=0.20, stratify=y, random_state=42)"]}, {"cell_type": "code", "execution_count": 18, "id": "b629bb8f-037b-4444-92d9-4e21a43baa5a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ROC AUC (test):  0.554\n", "PR AUC (test):  0.214\n"]}], "source": ["clf.fit(X_train, y_train)\n", "proba = clf.predict_proba(X_test)[:,1]\n", "print(\"ROC AUC (test): \", f\"{roc_auc_score(y_test, proba):.3f}\")\n", "print(\"PR AUC (test): \", f\"{average_precision_score(y_test, proba):.3f}\")"]}, {"cell_type": "code", "execution_count": 20, "id": "1584ee48-70ca-4340-828a-0f8f66679eba", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Confusion (thr=0.5): TN 33 FP 0 FN 7 TP 0\n"]}], "source": ["# <PERSON><PERSON> de confusion umbral 0.5\n", "pred05= (proba >=0.5).astype(int)\n", "tn, fp, fn, tp = confusion_matrix(y_test, pred05).ravel()\n", "print(\"Confusion (thr=0.5): TN\", tn, \"FP\", fp, \"FN\", fn, \"TP\", tp)"]}, {"cell_type": "code", "execution_count": 21, "id": "d7e6807d-acf9-4a5f-8d43-af1897ca4d89", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# opcional curva ROC\n", "plt.figure()\n", "fpr, tpr, thr= roc_curve(y_test, proba)\n", "plt.plot(fpr, tpr)\n", "plt.xlabel(\"FPR\")\n", "plt.ylabel(\"TPR\")\n", "plt.title(\"ROC\")\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 22, "id": "26ca2418-cadb-45af-b294-723c7eec069f", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# opcional curva PR\n", "plt.figure()\n", "prec, rec, thr2= precision_recall_curve(y_test, proba)\n", "plt.plot(prec, rec)\n", "plt.xlabel(\"Recall\")\n", "plt.ylabel(\"Precision\")\n", "plt.title(\"PR\")\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "4134cc5d-f2e3-4f3c-88fc-2aaf7f5d2b9c", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "anaconda-2024.02-py310", "language": "python", "name": "conda-env-anaconda-2024.02-py310-py"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 5}