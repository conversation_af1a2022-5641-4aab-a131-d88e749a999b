{"cells": [{"cell_type": "markdown", "id": "f70cc104-dabc-436d-ba08-94671cfdc6b3", "metadata": {}, "source": ["Ejercicio 2: Comparación CV: logística vs árbol\n", "Compara LogisticRegression (con escalado) vs DecisionTreeClassifier (sin escalado) usando\n", "StratifiedKFold(5). Reporta ROC AUC y PR AUC medias (± desviación). Elige el mejor por PR\n", "AUC, a<PERSON><PERSON><PERSON><PERSON> en train y evalúalo en test."]}, {"cell_type": "code", "execution_count": 2, "id": "7641f304-952c-4d94-ae4c-da3b3fd1feb3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Librerias y csv cargados\n"]}], "source": ["import pandas as pd, numpy as np, os\n", "import matplotlib.pyplot as plt\n", "from sklearn.model_selection import train_test_split, StratifiedKFold, cross_val_score\n", "from sklearn.compose import ColumnTransformer\n", "from sklearn.pipeline import Pipeline\n", "from sklearn.impute import SimpleImputer\n", "from sklearn.preprocessing import OneHotEncoder, StandardScaler\n", "from sklearn.linear_model import LogisticRegression\n", "from sklearn.tree import DecisionTreeClassifier\n", "from sklearn.metrics import (roc_auc_score, average_precision_score, roc_curve,\n", "                             confusion_matrix, brier_score_loss, precision_recall_curve)\n", "from sklearn.calibration import calibration_curve, CalibratedClassifierCV\n", "\n", "pd.set_option(\"display.max_columns\", 120)\n", "pd.set_option(\"display.width\", 160)\n", "\n", "df=pd.read_csv(\"../HOSPITAL_General_Sim_min.csv\")\n", "print(\"Librerias y csv cargados\")\n", "\n", "# Variable objetivo y precictores\n", "y= df[\"readmit_30d\"].astype(int)\n", "X = df.drop(columns=[\"readmit_30d\",\"episode_id\",\"patient_id\",\"admission_datetime\",\"discharge_datetime\"], errors=\"ignore\")\n", "\n", "# Tipos\n", "num_cols= X.select_dtypes(include=\"number\").columns.tolist()\n", "cat_cols= X.columns.difference(num_cols).tolist()\n", "\n", "# funcion para crear columna edad_band (si no existe)\n", "if \"edad_band\" not in df.columns and \"age_years\" in df.columns:\n", "    def _edad_band(x):\n", "        if pd.isna(x): return pd.NA\n", "        if x < 40: return \"<40\"\n", "        if x < 60: return \"40-59\"\n", "        if x < 75: return \"60-74\"\n", "        return \">=75\"\n", "    df[\"edad_band\"] = df[\"age_years\"].map(_edad_band)\n", "\n", "    # Preprocesado de los datos\n", "prep_log = ColumnTransformer([\n", "    (\"num\", Pipeline([(\"imp\", SimpleImputer(strategy=\"median\")),\n", "                      (\"sc\", StandardScaler())]), num_cols),\n", "    (\"cat\", Pipeline([(\"imp\", SimpleImputer(strategy=\"most_frequent\")),\n", "                      (\"oh\", OneHotEncoder(handle_unknown=\"ignore\"))]), cat_cols)\n", "])\n", "\n", "prep_tree = ColumnTransformer([\n", "    (\"num\", SimpleImputer(strategy=\"median\"), num_cols),\n", "    (\"cat\", Pipeline([(\"imp\", SimpleImputer(strategy=\"most_frequent\")),\n", "                      (\"oh\", OneHotEncoder(handle_unknown=\"ignore\"))]), cat_cols)\n", "])"]}, {"cell_type": "code", "execution_count": 3, "id": "ea455719-9ba5-492c-85c6-efccc81648b6", "metadata": {}, "outputs": [], "source": ["### CV de ambos modelos y evaluacion en test del mejor\n", "log_clf = Pipeline([(\"prep\", prep_log), (\"model\", LogisticRegression(max_iter=1000))])\n", "tree_clf = Pipeline([(\"prep\", prep_tree), \n", "                     (\"model\", DecisionTreeClassifier(max_depth=2,min_samples_leaf= 50, random_state=42))])"]}, {"cell_type": "code", "execution_count": 4, "id": "50381b31-0e3e-4118-905d-e64f86cbd723", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Logística --> ROC AUC:  0.482 +- 0.076  | PR AUC:  0.207 +- 0.029\n", "Arbol --> ROC AUC:  0.442 +- 0.025  | PR AUC:  0.175 +- 0.008\n"]}], "source": ["cv= StratifiedKFold(n_splits=5, shuffle=True, random_state=42)\n", "\n", "def resumen_cv(clf, X, y):\n", "    auc = cross_val_score(clf, X, y, cv=cv, scoring=\"roc_auc\")\n", "    ap = cross_val_score(clf, X, y, cv=cv, scoring=\"average_precision\")\n", "    return auc.mean(), auc.std(), ap.mean(), ap.std()\n", "\n", "m1 = resumen_cv(log_clf, X, y)\n", "m2 = resumen_cv(tree_clf, X, y)\n", "print(\"Logística --> ROC AUC: \", f\"{m1[0]:.3f} +- {m1[1]:.3f}\",\n", "      \" | PR AUC: \", f\"{m1[2]:.3f} +- {m1[3]:.3f}\")\n", "print(\"Arbol --> ROC AUC: \", f\"{m2[0]:.3f} +- {m2[1]:.3f}\",\n", "      \" | PR AUC: \", f\"{m2[2]:.3f} +- {m2[3]:.3f}\")"]}, {"cell_type": "code", "execution_count": 5, "id": "4e388c05-a57f-4d87-84c9-3a59f3c38759", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Test ROC AUC: 0.554 | PR AUC:  0.214\n"]}], "source": ["mejor = log_clf if m1[2] >= m2[2] else tree_clf\n", "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.20, stratify=y, random_state=42)\n", "mejor.fit(X_train, y_train)\n", "proba= mejor.predict_proba(X_test)[:,1]\n", "print(\"Test ROC AUC:\", f\"{roc_auc_score(y_test, proba):.3f}\", \n", "      \"| PR AUC: \", f\"{average_precision_score(y_test, proba):.3f}\")"]}], "metadata": {"kernelspec": {"display_name": "ia-bio", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}