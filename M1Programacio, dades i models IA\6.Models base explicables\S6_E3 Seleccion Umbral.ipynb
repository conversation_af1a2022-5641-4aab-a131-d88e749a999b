{"cells": [{"cell_type": "markdown", "id": "25e1df32-2415-462d-a375-0365c37ccd86", "metadata": {}, "source": ["Ejercicio 3: Selección de umbral\n", "Con la logística de (1), compara tres criterios de umbral en test: <PERSON><PERSON>, top-20% (capacidad), y\n", "coste FN:FP = 5:1. Para cada uno, muestra Sensibilidad, Especificidad, FP y FN."]}, {"cell_type": "code", "execution_count": 1, "id": "e6883abc-d07a-4da7-8f90-3699a24f70a0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Librerias y csv cargados\n"]}], "source": ["import pandas as pd, numpy as np, os\n", "import matplotlib.pyplot as plt\n", "from sklearn.model_selection import train_test_split, StratifiedKFold, cross_val_score\n", "from sklearn.compose import ColumnTransformer\n", "from sklearn.pipeline import Pipeline\n", "from sklearn.impute import SimpleImputer\n", "from sklearn.preprocessing import OneHotEncoder, StandardScaler\n", "from sklearn.linear_model import LogisticRegression\n", "from sklearn.tree import DecisionTreeClassifier\n", "from sklearn.metrics import (roc_auc_score, average_precision_score, roc_curve,\n", "                             confusion_matrix, brier_score_loss, precision_recall_curve)\n", "from sklearn.calibration import calibration_curve, CalibratedClassifierCV\n", "\n", "pd.set_option(\"display.max_columns\", 120)\n", "pd.set_option(\"display.width\", 160)\n", "\n", "df=pd.read_csv(\"HOSPITAL_General_Sim_min.csv\")\n", "print(\"Librerias y csv cargados\")\n", "\n", "# Variable objetivo y precictores\n", "y= df[\"readmit_30d\"].astype(int)\n", "X = df.drop(columns=[\"readmit_30d\",\"episode_id\",\"patient_id\",\"admission_datetime\",\"discharge_datetime\"], errors=\"ignore\")\n", "\n", "# Tipos\n", "num_cols= X.select_dtypes(include=\"number\").columns.tolist()\n", "cat_cols= X.columns.difference(num_cols).tolist()\n", "\n", "# funcion para crear columna edad_band (si no existe)\n", "if \"edad_band\" not in df.columns and \"age_years\" in df.columns:\n", "    def _edad_band(x):\n", "        if pd.isna(x): return pd.NA\n", "        if x < 40: return \"<40\"\n", "        if x < 60: return \"40-59\"\n", "        if x < 75: return \"60-74\"\n", "        return \">=75\"\n", "    df[\"edad_band\"] = df[\"age_years\"].map(_edad_band)\n", "\n", "    # Preprocesado de los datos\n", "prep_log = ColumnTransformer([\n", "    (\"num\", Pipeline([(\"imp\", SimpleImputer(strategy=\"median\")),\n", "                      (\"sc\", StandardScaler())]), num_cols),\n", "    (\"cat\", Pipeline([(\"imp\", SimpleImputer(strategy=\"most_frequent\")),\n", "                      (\"oh\", OneHotEncoder(handle_unknown=\"ignore\"))]), cat_cols)\n", "])\n", "\n", "prep_tree = ColumnTransformer([\n", "    (\"num\", SimpleImputer(strategy=\"median\"), num_cols),\n", "    (\"cat\", Pipeline([(\"imp\", SimpleImputer(strategy=\"most_frequent\")),\n", "                      (\"oh\", OneHotEncoder(handle_unknown=\"ignore\"))]), cat_cols)\n", "])\n"]}, {"cell_type": "code", "execution_count": 2, "id": "f749eaa9-6e64-4fde-988f-5babf6c8c3ef", "metadata": {}, "outputs": [], "source": ["# Entrenamiento y comparación de Umbrales\n", "clf = Pipeline([(\"prep\", prep_log), (\"model\", LogisticRegression(max_iter=1000))])\n", "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.20, stratify=y, random_state=42)"]}, {"cell_type": "code", "execution_count": 4, "id": "b3c019d2-f8ed-4e74-95f6-c095a7dab619", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ROC AUC (test):  0.554\n", "PR AUC (test):  0.214\n"]}], "source": ["clf.fit(X_train, y_train)\n", "proba = clf.predict_proba(X_test)[:,1]\n", "print(\"ROC AUC (test): \", f\"{roc_auc_score(y_test, proba):.3f}\")\n", "print(\"PR AUC (test): \", f\"{average_precision_score(y_test, proba):.3f}\")"]}, {"cell_type": "code", "execution_count": 6, "id": "c1842347-af05-497e-a9bd-15e73e9c3029", "metadata": {}, "outputs": [], "source": ["# Youden\n", "fpr, tpr, thr = roc_curve(y_test, proba)\n", "youden = tpr - fpr\n", "thr_youden = thr[youden.argmax()]\n", "\n", "# Capacidad 20%\n", "k = int(0.20 * len(proba))\n", "thr_cap = np.partition(proba, -k)[-k]\n", "\n", "# coste 5.1\n", "ration = 5\n", "grid = np.linspace(0.01, 0.99, 99)\n"]}, {"cell_type": "code", "execution_count": 7, "id": "b93a408b-d968-4618-9e03-14e189ec82e8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Youden: thr=0.150 | Sens=0.857 | Esp=0.455 | FP=18 | FN=1\n", "Capacidad 20%: thr=0.271 | Sens=0.000 | Esp=0.758 | FP=8 | FN=7\n", "Coste 5:1: thr=0.120 | Sens=0.857 | Esp=0.394 | FP=20 | FN=1\n"]}], "source": ["def coste(th):\n", "    pred = (proba >= th).astype(int)\n", "    tn, fp, fn, tp = confusion_matrix(y_test, pred).ravel()\n", "    return fp*1 + fn*ration\n", "thr_coste = grid[np.argmin([coste(t) for t in grid])]\n", "\n", "for etiqueta, thr_ in [(\"<PERSON>den\", thr_youden), (\"Capacidad 20%\", thr_cap), (\"Coste 5:1\", thr_coste)]:\n", "    pred = (proba >= thr_).astype(int)\n", "    tn, fp, fn, tp = confusion_matrix(y_test, pred).ravel()\n", "    sens = tp/(tp+fn) if (tp+fn)>0 else np.nan\n", "    esp  = tn/(tn+fp) if (tn+fp)>0 else np.nan\n", "    print(f\"{etiqueta}: thr={thr_:.3f} | Sens={sens:.3f} | Esp={esp:.3f} | FP={fp} | FN={fn}\")"]}, {"cell_type": "code", "execution_count": null, "id": "ea1d6ea5-8cbf-4cfc-a22e-8eae7df88c5a", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "anaconda-2024.02-py310", "language": "python", "name": "conda-env-anaconda-2024.02-py310-py"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 5}