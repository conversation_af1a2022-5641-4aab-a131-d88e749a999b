{"cells": [{"cell_type": "code", "execution_count": 3, "id": "bba30167-4522-4566-a1f6-581dc7f246a1", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "\n", "from sklearn.model_selection import train_test_split, cross_val_score\n", "from sklearn.compose import ColumnTransformer\n", "from sklearn.pipeline import Pipeline\n", "from sklearn.impute import SimpleImputer\n", "from sklearn.preprocessing import OneHotEncoder, StandardScaler\n", "from sklearn.linear_model import LogisticRegression\n", "from sklearn.tree import DecisionTreeClassifier\n", "from sklearn.model_selection import StratifiedKFold\n"]}, {"cell_type": "code", "execution_count": 4, "id": "ac195fda-3ae2-4797-a579-3aee5e649fd1", "metadata": {}, "outputs": [], "source": ["df= pd.read_csv(\"../baseS03.csv\")"]}, {"cell_type": "code", "execution_count": 5, "id": "a3e37865-f439-4ce0-a475-96e02b710a18", "metadata": {}, "outputs": [], "source": ["y=df['readmit_30d'].astype(int)\n", "X = df.drop(columns=['readmit_30d'])"]}, {"cell_type": "code", "execution_count": 6, "id": "cce094a3-e701-4865-9571-6c7bad91ae3d", "metadata": {}, "outputs": [], "source": ["# Tipos\n", "num_cols = X.select_dtypes(include=\"number\").columns.tolist()\n", "cat_cols = X.columns.difference(num_cols).tolist()"]}, {"cell_type": "code", "execution_count": 7, "id": "f6ac6672-4780-4c45-b20d-3042e2c96b92", "metadata": {}, "outputs": [], "source": ["# Preprocesado para Logística: imputación + escaladado + OneHot\n", "prep_log = ColumnTransformer([\n", "    (\"num\", Pipeline([(\"imp\", SimpleImputer(strategy=\"median\")),\n", "                      (\"sc\", StandardScaler())]), num_cols),\n", "    (\"cat\", Pipeline([(\"imp\", SimpleImputer(strategy=\"most_frequent\")),\n", "                      (\"oh\", OneHotEncoder(handle_unknown=\"ignore\"))]), cat_cols)])\n", "    "]}, {"cell_type": "code", "execution_count": 8, "id": "fe39d506-7a9f-4d52-94db-3647d2b86297", "metadata": {}, "outputs": [], "source": ["# Preprocesado para ARBOL: imputación + OneHot (Sin escalado)\n", "prep_tree = ColumnTransformer ([\n", "    (\"num\", SimpleImputer(strategy=\"median\"), num_cols),\n", "    (\"cat\", Pipeline([(\"imp\", SimpleImputer(strategy=\"most_frequent\")),\n", "                      (\"oh\", OneHotEncoder(handle_unknown=\"ignore\"))]), cat_cols)])"]}, {"cell_type": "code", "execution_count": null, "id": "a661fe3e-bb97-436b-ad96-bfc674232c75", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Logística --> ROC AUC CV: 0.509 | PR AUC CV: 0.215\n", "Arbol --> ROC AUC CV: 0.451 | PR AUC CV: 0.174\n"]}], "source": ["# Preprocessament\n", "preprocessor = ColumnTransformer([\n", "    (\"num\", Pipeline([\n", "        (\"imp\", SimpleImputer(strategy=\"median\")),\n", "        (\"sc\", StandardScaler())\n", "    ]), num_cols),\n", "    (\"cat\", <PERSON><PERSON><PERSON>([\n", "        (\"imp\", SimpleImputer(strategy=\"most_frequent\")),\n", "        (\"oh\", OneHotEncoder(handle_unknown=\"ignore\"))\n", "    ]), cat_cols)\n", "])"]}, {"cell_type": "code", "execution_count": 10, "id": "03341d90-7ae7-4d39-aeb5-d7914be3b813", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "feature", "rawType": "object", "type": "string"}, {"name": "coef", "rawType": "float64", "type": "float"}, {"name": "OR_aprox", "rawType": "float64", "type": "float"}], "ref": "4f679b45-4b02-46d6-9893-17ac9a7f6743", "rows": [["14", "cat__taquicardia_False", "0.5777831050665323", "1.7820833568134589"], ["7", "num__glucose_mg_dl", "0.3986329692365152", "1.****************"], ["2", "num__hr_bpm", "0.3159498673092932", "1.3715614938342946"], ["3", "num__sbp_mmhg", "0.2850526398432138", "1.329832028428532"], ["8", "cat__edad_band_40-59", "0.15460119492004776", "1.1671923860216145"], ["0", "num__age_years", "0.13543575976397992", "1.1450356361121987"], ["6", "num__creatinine_mg_dl", "0.13509780463840335", "1.1446487308320006"], ["9", "cat__edad_band_60-74", "0.09915444210712478", "1.104236827052565"], ["11", "cat__edad_band_>=75", "0.0335425129323236", "1.0341114059000056"], ["13", "cat__sex_M", "0.026901237591575422", "1.0272663424552653"], ["12", "cat__sex_F", "-0.026095515437015323", "0.9742420300112508"], ["1", "num__bmi", "-0.02905419090451501", "0.9713638239560659"], ["4", "num__spo2_pct", "-0.1427964632277422", "0.8669305031758361"], ["5", "num__hb_g_dl", "-0.14843276077578305", "0.8620579693308994"], ["10", "cat__edad_band_<40", "-0.28649242780493467", "0.7508927644160482"]], "shape": {"columns": 3, "rows": 15}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>feature</th>\n", "      <th>coef</th>\n", "      <th>OR_aprox</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>cat__taquicardia_False</td>\n", "      <td>0.577783</td>\n", "      <td>1.782083</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>num__glucose_mg_dl</td>\n", "      <td>0.398633</td>\n", "      <td>1.489787</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>num__hr_bpm</td>\n", "      <td>0.315950</td>\n", "      <td>1.371561</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>num__sbp_mmhg</td>\n", "      <td>0.285053</td>\n", "      <td>1.329832</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>cat__edad_band_40-59</td>\n", "      <td>0.154601</td>\n", "      <td>1.167192</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>num__age_years</td>\n", "      <td>0.135436</td>\n", "      <td>1.145036</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>num__creatinine_mg_dl</td>\n", "      <td>0.135098</td>\n", "      <td>1.144649</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>cat__edad_band_60-74</td>\n", "      <td>0.099154</td>\n", "      <td>1.104237</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>cat__edad_band_&gt;=75</td>\n", "      <td>0.033543</td>\n", "      <td>1.034111</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>cat__sex_M</td>\n", "      <td>0.026901</td>\n", "      <td>1.027266</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>cat__sex_F</td>\n", "      <td>-0.026096</td>\n", "      <td>0.974242</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>num__bmi</td>\n", "      <td>-0.029054</td>\n", "      <td>0.971364</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>num__spo2_pct</td>\n", "      <td>-0.142796</td>\n", "      <td>0.866931</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>num__hb_g_dl</td>\n", "      <td>-0.148433</td>\n", "      <td>0.862058</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>cat__edad_band_&lt;40</td>\n", "      <td>-0.286492</td>\n", "      <td>0.750893</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                   feature      coef  OR_aprox\n", "14  cat__taquicardia_False  0.577783  1.782083\n", "7       num__glucose_mg_dl  0.398633  1.489787\n", "2              num__hr_bpm  0.315950  1.371561\n", "3            num__sbp_mmhg  0.285053  1.329832\n", "8     cat__edad_band_40-59  0.154601  1.167192\n", "0           num__age_years  0.135436  1.145036\n", "6    num__creatinine_mg_dl  0.135098  1.144649\n", "9     cat__edad_band_60-74  0.099154  1.104237\n", "11     cat__edad_band_>=75  0.033543  1.034111\n", "13              cat__sex_M  0.026901  1.027266\n", "12              cat__sex_F -0.026096  0.974242\n", "1                 num__bmi -0.029054  0.971364\n", "4            num__spo2_pct -0.142796  0.866931\n", "5             num__hb_g_dl -0.148433  0.862058\n", "10      cat__edad_band_<40 -0.286492  0.750893"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["#Ajuste final con  todos los datos\n", "log_cf.fit(X,y)\n", "feat = log_cf.named_steps[\"prep\"].get_feature_names_out()\n", "coef = log_cf.named_steps[\"model\"].coef_.ravel()\n", "coef_tab=(pd.DataFrame({\"feature\": feat, \"coef\": coef, \"OR_aprox\":np.exp(coef)})\n", "          .sort_values(\"coef\", ascending=False))\n", "coef_tab.head(15)"]}, {"cell_type": "code", "execution_count": 11, "id": "909c45c4-986b-46da-a612-debe067b6795", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["|--- num__bmi <= 24.15\n", "|   |--- class: 0\n", "|--- num__bmi >  24.15\n", "|   |--- num__bmi <= 29.75\n", "|   |   |--- class: 0\n", "|   |--- num__bmi >  29.75\n", "|   |   |--- class: 0\n", "\n"]}], "source": ["# ajusta el pipeline de arbol y importar datos\n", "tree_cf.fit(X,y)\n", "from sklearn.tree import export_text\n", "print(export_text(tree_cf.named_steps[\"model\"], feature_names=list(feat)))"]}, {"cell_type": "code", "execution_count": null, "id": "731db5ba-279f-4882-8b0e-0f1556117a39", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "ia-bio", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}