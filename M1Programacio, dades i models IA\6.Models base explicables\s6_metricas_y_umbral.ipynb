{"cells": [{"cell_type": "code", "execution_count": 5, "id": "b15ce3bb-6145-43ae-9d35-7f46550733c0", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "\n", "from sklearn.model_selection import train_test_split, cross_val_score\n", "from sklearn.compose import ColumnTransformer\n", "from sklearn.pipeline import Pipeline\n", "from sklearn.impute import SimpleImputer\n", "from sklearn.preprocessing import OneHotEncoder, StandardScaler\n", "from sklearn.linear_model import LogisticRegression\n", "from sklearn.tree import DecisionTreeClassifier\n", "from sklearn.model_selection import StratifiedKFold"]}, {"cell_type": "code", "execution_count": 6, "id": "c742cc4c-186d-47c7-84e5-3adaf2d5e6ef", "metadata": {}, "outputs": [], "source": ["df= pd.read_csv(\"../baseS03.csv\")\n", "y=df['readmit_30d'].astype(int)\n", "X = df.drop(columns=['readmit_30d'])\n", "# Tipos\n", "num_cols = X.select_dtypes(include=\"number\").columns.tolist()\n", "cat_cols = X.columns.difference(num_cols).tolist()\n", "# Preprocesado para Logística: imputación + escaladado + OneHot\n", "prep_log = ColumnTransformer([\n", "    (\"num\", Pipeline([(\"imp\", SimpleImputer(strategy=\"median\")),\n", "                      (\"sc\", StandardScaler())]), num_cols),\n", "    (\"cat\", Pipeline([(\"imp\", SimpleImputer(strategy=\"most_frequent\")),\n", "                      (\"oh\", OneHotEncoder(handle_unknown=\"ignore\"))]), cat_cols)])\n", "log_cf = Pipeline([(\"prep\", prep_log),\n", "                   (\"model\", LogisticRegression(max_iter=1000))])\n", "    "]}, {"cell_type": "code", "execution_count": 7, "id": "ae3af2a9-8e8c-4e2d-930f-d46331f370d4", "metadata": {}, "outputs": [], "source": ["# importamos el resto de sklearn necesarios\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.metrics import (roc_auc_score, roc_curve, precision_recall_curve, confusion_matrix, average_precision_score, brier_score_loss)\n", "from sklearn.calibration import calibration_curve, CalibratedClassifierCV"]}, {"cell_type": "code", "execution_count": 8, "id": "6858b9df-54cf-4b00-a16e-1adf62756920", "metadata": {}, "outputs": [], "source": ["X_train, X_Test, y_train, y_test = train_test_split(X, y, test_size=0.2, stratify=y, random_state=42)"]}, {"cell_type": "code", "execution_count": 9, "id": "12ee054a-3cbd-4fde-b04a-0c69f85d146d", "metadata": {}, "outputs": [], "source": ["log_cf.fit(X_train, y_train)\n", "proba = log_cf.predict_proba(X_Test)[:,1]"]}, {"cell_type": "code", "execution_count": 10, "id": "f6dd87a7-0886-4348-ab65-02c2d1a0a146", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ROC AUC (test):  0.524\n", "PR AUC (test):  0.212\n"]}], "source": ["print(\"ROC AUC (test): \", f\"{roc_auc_score(y_test, proba):.3f}\")\n", "print(\"PR AUC (test): \", f\"{average_precision_score(y_test, proba):.3f}\")\n"]}, {"cell_type": "code", "execution_count": 11, "id": "998d3b99-b832-433f-aba0-8f6d1c6b573b", "metadata": {}, "outputs": [], "source": ["# Umbral 1: <PERSON><PERSON>\n", "fpr, tpr, thr = roc_curve(y_test, proba)\n", "youden = tpr - fpr\n", "thr_youden = thr[youden.argmax()]"]}, {"cell_type": "code", "execution_count": 12, "id": "e6ee4e6b-1536-4216-9737-8583157ded2c", "metadata": {}, "outputs": [], "source": ["# Umbral 2: Capacidad de riesgo 20%\n", "k = int(0.20 * len(proba))\n", "thr_cap = np.partition(proba, -k)[-k] "]}, {"cell_type": "code", "execution_count": 13, "id": "b1017d5a-e6c2-4ae1-b273-6b102aff3508", "metadata": {}, "outputs": [], "source": ["# Umbral 3: <PERSON><PERSON><PERSON><PERSON> (FN)\n", "ratio = 5\n", "grid = np.linspace(0.01,0.99,99)\n", "def coste(th):\n", "    pred = (proba >=th).astype(int)\n", "    tn, fp, fn, tp = confusion_matrix(y_test, pred).ravel()\n", "    return fp*1 + fn*ratio\n", "\n", "thr_coste= grid[np.argmin([coste(t) for t in grid])]"]}, {"cell_type": "code", "execution_count": 14, "id": "0c9215a1-fa91-470e-b212-502203ea8ed9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Youden: thr=0.233 | Sens=0.571429 | Esp=0.757576 | FP8 | FN3\n", "Capacidad 20%: thr=0.279 | Sens=0.000000 | Esp=0.757576 | FP8 | FN7\n", "Coste 5:1: thr=0.210 | Sens=0.571429 | Esp=0.727273 | FP9 | FN3\n"]}], "source": ["# Resumen por umbral\n", "for etiqueta,thr_ in[(\"<PERSON>den\",thr_youden),(\"Capacidad 20%\",thr_cap),(\"Coste 5:1\",thr_coste)]:\n", "    pred= (proba >= thr_).astype(int)\n", "    tn, fp, fn, tp = confusion_matrix(y_test, pred).ravel()\n", "    sens =tp/(tp+fn) if (tp+fn)>0 else np.nan\n", "    esp = tn/(tn+fp) if (tn+fp)>0 else np.nan\n", "    print(f\"{etiqueta}: thr={thr_:.3f} | Sens={sens:3f} | Esp={esp:3f} | FP{fp} | FN{fn}\")"]}, {"cell_type": "code", "execution_count": 15, "id": "aba73b57-4518-408e-988f-168423619d5e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Brier score (sin calibrar):  0.164\n", "Brier score (calibrado):  0.164\n"]}], "source": ["#Calibracion Brier \n", "print(\"Brier score (sin calibrar): \", f\"{brier_score_loss(y_test, proba):.3f}\")\n", "cal = CalibratedClassifierCV(log_cf, method=\"isotonic\", cv=3)\n", "cal.fit(X_train, y_train)\n", "proba_cal= cal.predict_proba(X_Test)[:,1]\n", "print (\"Brier score (calibrado): \", f\"{brier_score_loss(y_test, proba):.3f}\")"]}, {"cell_type": "code", "execution_count": 16, "id": "bc1b163f-e08b-4b52-a79a-e2540baf248b", "metadata": {}, "outputs": [], "source": ["# curva de calibracion\n", "prob_true, prob_pred = calibration_curve(y_test, proba, n_bins=10,strategy=\"quantile\")"]}, {"cell_type": "code", "execution_count": 17, "id": "3cd40453-4ee9-422e-831c-afa06d2d3273", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[0.25 0.   0.25 0.25 0.   0.   0.   1.   0.   0.  ]\n", "[0.05419416 0.0839328  0.11194685 0.1313881  0.14946193 0.1763017\n", " 0.20949139 0.25232256 0.32393092 0.48109964]\n"]}], "source": ["print(prob_true)\n", "print(prob_pred)"]}, {"cell_type": "code", "execution_count": null, "id": "c670ddb8-1c20-4a75-ad30-f91a654d58bd", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "ia-bio", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}