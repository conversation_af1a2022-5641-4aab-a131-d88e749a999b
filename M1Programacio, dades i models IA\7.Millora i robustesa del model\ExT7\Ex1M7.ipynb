{"cells": [{"cell_type": "markdown", "id": "499a823e", "metadata": {}, "source": ["# Tuning robusto y evaluación en test\n", "Realiza un GridSearchCV multi-métrica (AUC-ROC y AUPRC) sobre una Regresión Logística, con refit por AUPRC. Informa:\n", "• Hiperparámetros óptimos y métricas medias de CV.\n", "• En test: ROC AUC, AUPRC, matriz de confusión a umbral 0.5.\n", "• Dibuja curva ROC y curva PR (una figura por gráfico, sin estilos)."]}, {"cell_type": "code", "execution_count": 20, "id": "ccb1d8ad", "metadata": {}, "outputs": [], "source": ["import numpy as np, pandas as pd, matplotlib.pyplot as plt\n", "from sklearn.model_selection import train_test_split, StratifiedKFold,GridSearchCV\n", "from sklearn.compose import ColumnTransformer\n", "from sklearn.pipeline import Pipeline\n", "from sklearn.impute import SimpleImputer\n", "from sklearn.preprocessing import OneHotEncoder, StandardScaler\n", "from sklearn.linear_model import LogisticRegression\n", "from sklearn.metrics import (roc_auc_score, average_precision_score, roc_curve, precision_recall_curve, confusion_matrix)"]}, {"cell_type": "code", "execution_count": 21, "id": "e29a0bb4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Data frame loaded (200, 19)\n"]}], "source": ["pd.set_option(\"display.max_columns\", 120)\n", "pd.set_option(\"display.width\", 160)\n", "df= pd.read_csv(\"../../hospital_general_sim_min.csv\")\n", "print(\"Data frame loaded\", df.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "42f4d629", "metadata": {}, "outputs": [], "source": ["# Target i predictors \n", "y = df[\"readmit_30d\"].astype(int)\n", "X = df.drop(columns=[\"readmit_30d\"])"]}, {"cell_type": "code", "execution_count": 23, "id": "dc687893", "metadata": {}, "outputs": [], "source": ["num_cols = X.select_dtypes(include=\"number\").columns.tolist()\n", "cat_cols = X.columns.difference(num_cols).tolist()\n", "\n", "# --- Preprocessament\n", "# Numèriques: imputa mediana i escala\n", "# Categòriques: imputa el mode i one-hot encode\n", "preprocessor = ColumnTransformer([(\"num\", Pipeline([(\"imputer\", SimpleImputer(strategy=\"median\")),\n", "                                            (\"scaler\", StandardScaler())]), num_cols),\n", "                          (\"cat\", Pipeline([(\"imputer\", SimpleImputer(strategy=\"most_frequent\")),\n", "                                            (\"ebcoder\", OneHotEncoder(handle_unknown=\"ignore\", sparse_output=False))\n", "]), cat_cols)\n", "])"]}, {"cell_type": "code", "execution_count": 24, "id": "fde922cd", "metadata": {}, "outputs": [], "source": ["# --- Pipeline amb preprocesament i model de regressió logística\n", "pipe = Pipeline([\n", "(\"preprocessor\", preprocessor),\n", "(\"model\", LogisticRegression(max_iter=1000, solver=\"lbfgs\"))\n", "])"]}, {"cell_type": "code", "execution_count": 25, "id": "a5168b33", "metadata": {}, "outputs": [], "source": ["# --- Split train/test estratificat (80/20)\n", "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.20, stratify=y, random_state=42)"]}, {"cell_type": "code", "execution_count": 26, "id": "21a1a7c9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== Inici del tuning (GridSearchCV, refit=AUPRC) ===\n", "Millors hiperparàmetres: {'model__C': np.float64(0.01), 'model__penalty': 'l2'}\n", "CV ROC AUC: 0.602\n", "CV AUPRC: 0.322\n"]}], "source": ["# --- Cross-validation estratificada\n", "cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)\n", "\n", "# --- <PERSON><PERSON> de hiperparàmetres per Logistic Regression\n", "param_grid = {\n", "    \"model__C\": np.logspace(-3, 3, 7), # regularització L2\n", "    \"model__penalty\":[\"l2\"],\n", "    }\n", "\n", "# --- Mètriques multi-mètrica: ROC AUC i AUPRC\n", "scoring = {\n", "    \"roc_auc\": \"roc_auc\",\n", "    \"auprc\": \"average_precision\"\n", "}\n", "\n", "# --- GridSearchCV amb refit per AUPRC\n", "grid_search = GridSearchCV(\n", "    estimator=pipe,\n", "    param_grid=param_grid,\n", "    scoring=scoring,\n", "    refit=\"auprc\",\n", "    cv=cv,\n", "    n_jobs=-1,\n", "    return_train_score=False\n", ")\n", "\n", "# --- Entrenament\n", "print(\"\\n=== Inici del tuning (GridSearchCV, refit=AUPRC) ===\")\n", "grid_search.fit(X_train, y_train)\n", "\n", "# --- Millors hiperparàmetres i resultats mitjans de CV\n", "print(\"Millors hiperparàmetres:\", grid_search.best_params_)\n", "best_idx = grid_search.best_index_\n", "print(\"CV ROC AUC:\", f\"{grid_search.cv_results_['mean_test_roc_auc'][best_idx]:.3f}\")\n", "print(\"CV AUPRC:\", f\"{grid_search.cv_results_['mean_test_auprc'][best_idx]:.3f}\")\n"]}, {"cell_type": "code", "execution_count": 27, "id": "4c0edc80", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== Ava<PERSON><PERSON><PERSON>ó en test ===\n", "ROC AUC (test): 0.584\n", "PR AUC (test): 0.223\n"]}], "source": ["\n", "# --- <PERSON><PERSON><PERSON><PERSON>ó sobre el test set\n", "best_model = grid_search.best_estimator_\n", "y_proba = best_model.predict_proba(X_test)[:, 1]  # probabilitats de classe positiva\n", "\n", "roc_auc_test = roc_auc_score(y_test, y_proba)\n", "auprc_test = average_precision_score(y_test, y_proba)\n", "print(\"\\n=== Avaluació en test ===\")\n", "print(\"ROC AUC (test):\", f\"{roc_auc_test:.3f}\")\n", "print(\"PR AUC (test):\", f\"{auprc_test:.3f}\")\n"]}, {"cell_type": "code", "execution_count": 28, "id": "f293823a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Confusió (thr=0.5): TN 33 FP 0 FN 7 TP 0\n"]}], "source": ["\n", "# --- <PERSON><PERSON><PERSON> de confusió amb umbral 0.5\n", "y_pred_05 = (y_proba >= 0.5).astype(int)\n", "tn, fp, fn, tp = confusion_matrix(y_test, y_pred_05).ravel()\n", "print(\"Confusió (thr=0.5): TN\", tn, \"FP\", fp, \"FN\", fn, \"TP\", tp)\n"]}, {"cell_type": "code", "execution_count": 29, "id": "ffce16c0", "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAnYAAAHWCAYAAAD6oMSKAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjUsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvWftoOwAAAAlwSFlzAAAPYQAAD2EBqD+naQAAOTdJREFUeJzt3QmYjfX///H3mAUzGNkNGlSKRDJf61ciS/hRSpSyRVH6CkWky9aijSRbZUuXrWzVL5I2e18ZpF/84ss09mSXZcaM+3+9P7/rnP8sx5gzzpxz5jPPx3Xdzbnvcy+fc+6Yl892hziO4wgAAADyvAKBLgAAAAB8g2AHAABgCYIdAACAJQh2AAAAliDYAQAAWIJgBwAAYAmCHQAAgCUIdgAAAJYg2AEAAFiCYAcgR+bMmSMhISHuJSwsTMqXLy+PPPKI7Nmzx+Mxly9flmnTpknDhg0lOjpaChcuLNWrV5dhw4bJiRMnPB5z5coV+eSTT6RFixZSqlQpCQ8PlzJlysh//dd/yZdffmnev5akpCSZPHmy/POf/5QbbrhBIiIipEKFCtK5c2dZs2aN5GVPPPGE3Hfffeb1Pffck+6eXG0ZPXq0T649depU8/9BRrt37zbf8datW31yHQDZF8IjxQDkhP5C79Wrl8yePVtuu+02uXTpkmzYsEFee+01KVq0qPzv//6vCVEuFy5ckLZt28r69evlqaeeMsFMg92mTZvknXfekSJFisjq1avl1ltvdR+j53zggQfkm2++MYGxY8eOUq5cOfnrr7/k66+/lrlz58qiRYvk/vvvv2o5jx8/boLPjh07TAhq06aNlChRQg4dOiSff/65fPbZZxIfHy+1a9eWvGbbtm0SFxcn//73v83PnTt3ytmzZ93vf/XVV/Lqq6+675FLxYoVzXK9atasacL2jz/+mOk9/X9j3759eT44A3mOBjsA8Nbs2bP1H4XOzz//nG77mDFjzPZZs2al2/7UU0+Z7QsXLsx0rt9//92Jjo52br/9diclJcW9/emnnzbHfPzxxx7LsHv3bueXX37Jspxt2rRxwsLCnO+++87j+5s3b3YSExMdX7hw4YLjT507d3YaNGjg9T3yFb1fTZs29fjeli1bzLU3bNiQK9cG4BlNsQB8SmuO1J9//unedvToUZk1a5a0bt1aunTpkumYatWqyYsvvii//fabLF++3H3MjBkzzDHdu3f3eK1bbrlFatWqddWyaE3cypUrpXfv3tK8eXOP+/zjH/+QG2+80bzWJkptqrxas/Mff/zh3la5cmVT67h06VKpU6eOFCpUSMaMGWNeN2nSJNM5UlNTTfPvgw8+6N6WnJxsatS0Nq1gwYJSunRpU9OlNZLXot/vsmXLpFu3buItreXU5vCoqChTU6rfsdb+paW1bVpLGhMTY8pWtmxZuffee2X79u3uz6/3S2vkXE28us2lbt26ppl9+vTpXpcPQM4R7AD4VEJCgjusufzwww+SkpJimlWvxvWeNse6jtE+eVkdcy3ahJv23L6mfciGDBkiAwYMME3DDz30kAlm2tycsZ+hluXw4cPmfaV9A7UJ+Y033pCuXbuaZlN9rZ9f+8pdvHjxmp9Nv59mzZp5VebXX39dHn30UalRo4Z8+umnpv/iuXPnTBjVplwXbTbXYPzWW2+ZMmnfSA2tp0+fNu9rqKxatarZps3puui2tPRzaLCmxw/gP2F+vBYAC2lNlIY2Vx87rYG6++67pUOHDu599u/fb35WqVLlqudxvefaNzvHXIsvzpGVY8eOmTCUNsRq2NGwp7V82t/QRde11kv7+CkNVRoGlyxZkq4WT/v6aS2i7v/0009f9doapLSPYtq+c9dy4MABGTVqlDz77LMyadIk9/aWLVua2k+tcdTaPB3I8vvvv8vEiRPl8ccfd++Xtpwa6PT6xYoVkwYNGni83l133WUCoZ7Lm3ICyDlq7ABcF/2lriNVdcCEDlLQARM6KEFHyeaEp6bQYKXNwGlDnSpZsqS0b99ePv74Y/eI3VOnTpnvRJuUXd/Lf//3f0vx4sXNvhqMXcudd95pBoh4GpCQltb+adOtN9/XqlWrzDW0HGmvqc3ITZs2dV9TB5fcdNNN8vbbb8uECRNMM212Rh9npKOXlQ5UAeAfBDsA10VHpv7888/y/fffS9++fWXXrl2mqS8tVx82VzOtJ673KlWqlO1jrsUX58iKTu/iiY6+1TDjalZesGCBmXKlZ8+e6frIabOmTguiwTjtov0LdTRvVrSpVgOZN1z9HrVGMOM1tabOdU0Ni999953pe6dNsVrzpiFSm5y12Ta7XOW7VrMyAN+hKRbAddEO8q4BE9rfS5tmddDD4sWLpVOnTu7tWlOlAyP69evn8TyuQRPaLOg6RgNHVsdciwaTl156yZzDNddbdoKIhjAdMOBytZB1tdoyva4OOtBpRvS1/qxfv77p1+ai04Ro7Z42x3qiNaBZ0eO9nSdOj1F6b2JjY7PcV9+fOXOme146bTrWwSU64CO7AyJOnjyZ7roAch81dgB8Smt4tDl25MiR7uY7bVrUWixtCtSaoYw0OLz55pty++23uwc66DF9+vQxx2itoCd79+4189NdjdY0aZ82DShao+jJli1b3H3xXKM6M55TJ0L2RmhoqBmtqoFy3bp15hr6+dPSEbXal02DsAbjjEva+fw80T5revyZM2eyXS4NmRqw9XvzdE1XQM9Im5tffvllueOOO9KFSQ2/WdXG6cjaAgUKXPOzAPAdauwA+JSGuuHDh8vQoUNl/vz57s732ldLO9Hr+tq1a03fMg0GP/30k5mgWGuodCCBhiIXPUbDgTZhasDTCYp1AILWoGkzp9aELVy4MMspTzQUam2dBjzXBMVaxiNHjpjAps2kOvpTm211JKj2L9PpUcaOHWtCkA5i0EEH3tJraVjVEa86yCDjNC86lci8efPMNZ977jmpV6+eqaE8ePCgGRGsI2b1816NjjjV0aY6OXGrVq2yVSYNrvq5RowYYb5XV59IbaLdvHmzmf5EB1BosNUBFg8//LAZVKHNxRqMdbs+JcRFg55+/xrWddCI1njqNhe9t9pnMO1E1QBy2VXmtwOALGU1+e3FixedG2+80bnlllvSTTicnJzsTJkyxalfv75TpEgRp2DBgs6tt97qDB061Dl+/LjH6+jxOkFx8+bNnRIlSpjJhkuXLm0mHp4/f76Tmpp6zbJqeSZNmuQ0bNjQKVasmDlHTEyM8+CDDzpfffVVpgmLGzVq5ERFRTkVKlRwRo0a5cyYMcN81oSEBPd+sbGxTrt27bK8rp5Hj3vsscc8vn/58mXnnXfecWrXru0UKlTIfCe33Xab07dvX2fPnj1Znls/d+XKlZ1nnnnG63u0fPlyp1mzZua70Hugn6VTp07Ot99+a97/888/nZ49e5qy6Peg5apVq5bz7rvvpruff/zxh9OqVSunaNGi5jp6Hpdz5845kZGRzvjx47P8HAB8i0eKAUAeNX78eDOlig7U0FrBYKLN31oTqbWd1NgB/kMfOwDIo/r37y/R0dEyZcoUCSY6hYo2Q2uTPKEO8C+CHQDkUdqnTZ8ckXYEbzDQWjrtS/n8888HuihAvkNTLAAAgCWosQMAALAEwQ4AAMASBDsAAABL5LsJinUmfH14tk6GmpceNg4AAPInx3HMc5r1UYX6NJes5Ltgp6HO9ZBxAACAvEJHnFesWDHLffJdsHM9WFu/nGLFigW6OAAAAFk6e/asqZRyZZis5Ltg52p+1VBHsAMAAHlFdrqQMXgCAADAEgQ7AAAASxDsAAAALEGwAwAAsATBDgAAwBIEOwAAAEsQ7AAAACxBsAMAALAEwQ4AAMASBDsAAABLBDTYrV27Vtq3by8xMTHmMRnLly+/5jFr1qyRunXrSqFChaRq1aoyffp0v5QVAAAg2AU02J0/f15q164tkydPztb+CQkJ0rZtW2nSpIls27ZNXnrpJRkwYIAsWbIk18sKAAAQ7MICefE2bdqYJbu0du7GG2+UiRMnmvXq1avLli1b5J133pGHHnooF0sKAAAQ/AIa7Ly1adMmadWqVbptrVu3lpkzZ8rly5clPDw8YGUDAAB5j+M4cvFy6nWfp3B4qOlWFmh5KtgdPXpUypYtm26brqekpMjx48elfPnymY5JSkoyi8vZs2f9UlYAABD8oa7T9E0Sn3jqus+1c2xriYwIfKzKc6NiM6ZhvSmetruMGzdOoqOj3UulSpX8Uk4AABDcLl5O9UmoCyaBj5ZeKFeunKm1S+vYsWMSFhYmJUuW9HjM8OHDZfDgwelq7Ah3AAAgrS0vt5DIiFC5nqbYYJCngl3Dhg3lyy+/TLftm2++kbi4uKv2rytYsKBZAAAArkZDXTA0pebppti///5btm/fbhbXdCb6ev/+/e7atu7du7v379evnyQmJpoauF27dsmsWbPMwIkXXnghYJ8BAAAgWAQ0mupUJc2aNXOvu5pMe/ToIXPmzJEjR464Q56qUqWKrFixQgYNGiRTpkwxExtPmjSJqU4AAAACHezuuece9+AHTzTcZdS0aVPZunVrLpcMAAAg78lzo2IBAADgGcEOAADAEgQ7AAAASxDsAAAALEGwAwAAsATBDgAAwBIEOwAAAEsQ7AAAACxBsAMAALAEwQ4AAMASBDsAAABLEOwAAAAsQbADAACwBMEOAADAEgQ7AAAASxDsAAAALEGwAwAAsATBDgAAwBIEOwAAAEsQ7AAAACxBsAMAALAEwQ4AAMASBDsAAABLEOwAAAAsQbADAACwBMEOAADAEgQ7AAAASxDsAAAALEGwAwAAsATBDgAAwBIEOwAAAEsQ7AAAACxBsAMAALAEwQ4AAMASBDsAAABLEOwAAAAsQbADAACwBMEOAADAEgQ7AAAASxDsAAAALEGwAwAAsATBDgAAwBJhgS4AAHjDcRy5eDk10MUAYIELyfb9XUKwA5CnQl2n6ZskPvFUoIsCAEGJplgAeYbW1BHqAPhaXOwNUjg8VGxAjR2APGnLyy0kMsKOv4gBBFbh8FAJCQkRGxDsAORJGuoiI/grDADSoikWAADAEgQ7AAAASxDsAAAALEGwAwAAsATBDgAAwBIEOwAAAEsQ7AAAACxBsAMAALAEwQ4AAMASBDsAAABLEOwAAAAsQbADAACwBMEOAADAEgEPdlOnTpUqVapIoUKFpG7durJu3bos9583b57Url1bIiMjpXz58tKrVy85ceKE38oLAAAQrAIa7BYtWiQDBw6UESNGyLZt26RJkybSpk0b2b9/v8f9169fL927d5fevXvLb7/9Jp999pn8/PPP0qdPH7+XHQAAINgENNhNmDDBhDQNZtWrV5eJEydKpUqVZNq0aR73/+mnn6Ry5coyYMAAU8v3z3/+U/r27Stbtmzxe9kBAACCTcCCXXJyssTHx0urVq3Sbdf1jRs3ejymUaNGcvDgQVmxYoU4jiN//vmnLF68WNq1a3fV6yQlJcnZs2fTLQAAADYKWLA7fvy4pKamStmyZdNt1/WjR49eNdhpH7suXbpIRESElCtXTooXLy7vv//+Va8zbtw4iY6Odi9aIwgAAGCjgA+eCAkJSbeuNXEZt7ns3LnTNMOOHDnS1PZ9/fXXkpCQIP369bvq+YcPHy5nzpxxLwcOHPD5ZwAAAAgGYYG6cKlSpSQ0NDRT7dyxY8cy1eKlrX1r3LixDBkyxKzXqlVLoqKizKCLV1991YySzahgwYJmAQAAsF3Aauy0KVWnN1m9enW67bquTa6eXLhwQQoUSF9kDYeumj4AAID8LKBNsYMHD5YZM2bIrFmzZNeuXTJo0CAz1YmraVWbUXV6E5f27dvL0qVLzajZffv2yYYNG0zTbL169SQmJiaAnwQAACAfN8UqHQShkwuPHTtWjhw5IjVr1jQjXmNjY837ui3tnHY9e/aUc+fOyeTJk+X55583AyeaN28ub775ZgA/BQAAQHAIcfJZG6ZOd6KjY3UgRbFixQJdHABeuJCcIjVGrjKvd45tLZERAf23KQAEXXYJ+KhYAAAA+AbBDgAAwBIEOwAAAEsQ7AAAACxBsAMAALAEwQ4AAMASBDsAAABLEOwAAAAsQbADAACwBMEOAADAEgQ7AAAASxDsAAAALEGwAwAAsATBDgAAwBIEOwAAAEsQ7AAAACxBsAMAALAEwQ4AAMASBDsAAABLEOwAAAAsQbADAACwRFigCwAg/3EcRy5eTvX6uAvJ3h8DAPkJwQ6A30Ndp+mbJD7xVKCLAgDWoSkWgF9pTd31hrq42BukcHioz8oEALagxg5AwGx5uYVERngf0DTUhYSE5EqZACAvI9gBCBgNdZER/DUEAL5CUywAAIAlCHYAAACWINgBAABYgmAHAABgCYIdAACAJQh2AAAAliDYAQAAWIJgBwAAYAmCHQAAgCUIdgAAAJYg2AEAAFiCYAcAAJCfg11KSop8++238sEHH8i5c+fMtsOHD8vff//t6/IBAAAgm8LES4mJiXLffffJ/v37JSkpSVq2bClFixaVt956Sy5duiTTp0/39pQAAAAIRI3dc889J3FxcXLq1CkpXLiwe3vHjh3lu+++80WZAAAA4I8au/Xr18uGDRskIiIi3fbY2Fg5dOhQTsoAAACAQNTYXblyRVJTUzNtP3jwoGmSBQAAQB4JdtqnbuLEie71kJAQM2hi1KhR0rZtW1+XDwAAALnVFPvuu+9Ks2bNpEaNGmawRNeuXWXPnj1SqlQpWbBggbenAwAAQKCCXUxMjGzfvl0WLlwo8fHxpmm2d+/e8thjj6UbTAEAAIAgD3Zr166VRo0aSa9evcySdm47fe/uu+/2dRkBAACQG33stBn25MmTmbafOXPGvAcAAIA8EuwcxzEDJjI6ceKEREVF+apcAAAAyK2m2AcffND81FDXs2dPKViwoPs9nf5kx44dpokWAAAAQR7soqOj3TV2Ol9d2oESOllxgwYN5Mknn8ydUgIAAMB3wW727NnmZ+XKleWFF16g2RUAACCvj4rViYgBAABgQbBTixcvlk8//VT2798vycnJ6d7bunWrr8oGAACA3BwVO2nSJDN/XZkyZWTbtm1Sr149KVmypOzbt0/atGnj7ekAAAAQqGA3depU+fDDD2Xy5Mlm0MTQoUNl9erVMmDAADOXHQAAAPJIsNPmV9e0Jjoy9ty5c+Z1t27deFYsAABAXgp25cqVM5MRq9jYWPnpp5/M64SEBDMVCgAAAPJIsGvevLl8+eWX5nXv3r1l0KBB0rJlS+nSpYt07NgxN8oIAACA3Ah22r9uxIgR5nW/fv1kzpw5Ur16dRkzZoxMmzZNctJnr0qVKlKoUCGpW7eurFu3Lsv9k5KSzPW1tlCffnHTTTfJrFmzvL4uAACA5PfpTgoUKGAWl86dO5tFHTp0SCpUqJDtcy1atEgGDhxowl3jxo3lgw8+MCNrd+7cKTfeeKPHY/Raf/75p8ycOVNuvvlmOXbsmKSkpHj7MQAAAKzjdY2dJ0ePHpV//etfJmh5Y8KECaY5t0+fPqbWb+LEiVKpUqWr1vx9/fXXsmbNGlmxYoW0aNHCPAVDp1vhGbUAAABeBLvTp0/LY489JqVLl5aYmBgzn92VK1dk5MiRUrVqVTOIwpsmUZ3YOD4+Xlq1apVuu65v3LjR4zFffPGFxMXFyVtvvWVqBqtVq2Yeb3bx4sUsm27Pnj2bbgEAAMjXTbEvvfSSrF27Vnr06GFqznTQhP68dOmSrFy5Upo2berVhY8fPy6pqalStmzZdNt1XWsAPdFJkNevX2/64y1btsyc45lnnpGTJ09eNVSOGzfO9P8DAACwXbZr7L766iuZPXu2vPPOO6bmTKc20Rqz77//3utQl1ZISEi6dT1vxm0uWkOo782bN880wbZt29Y05+oAjqvV2g0fPtxMnOxaDhw4kOOyAgAAWFFjd/jwYalRo4Z5rU2vWmumfeNyqlSpUhIaGpqpdk4HQ2SsxXMpX768aYKNjo52b9O+eRoGDx48KLfcckumY3TkrC4AAAC2y3aNndaWhYeHu9c1lEVFReX4wvo4Mp3eRB9HlpauX20whI6c1YD5999/u7ft3r3bjNKtWLFijssCAACQr2rstFasZ8+e7tov7Vun89hlDHdLly7N9sUHDx5sHkWmAyIaNmxo5sjTR5bpeV3NqDqFyty5c816165d5ZVXXpFevXqZfnPax27IkCHyxBNPmMebAQAA5GfZDnY6aCKtxx9//Lovrk+r0MeTjR07Vo4cOSI1a9Y0U5no5MNKt2nQcylSpIip0dOpVTQMlixZ0sxr9+qrr153WQAAAPK6ECefPeBVpzvRPno6kKJYsWKBLg6Q71xITpEaI1eZ1zvHtpbICK/nSQeAfOWsF9nFJxMUAwAAIPD4pzIAr2lF/8XLqTk69kJyzo4DAFwbwQ6A16Gu0/RNEp94KtBFAQBkQFMsAK9oTZ0vQl1c7A1SODzUJ2UCAPwfauwA5NiWl1tIZETOwpmGuqs9ZQYA4Mcau08++cRMFhwTEyOJiYlm28SJE+Xzzz/PYTEA5EUa6nRUa04WQh0ABEGwmzZtmplYWJ/Tevr0aUlN/b+O0MWLFzfhDgAAAHkk2L3//vvy0UcfyYgRI8xjxVx0wuBff/3V1+UDAABAbgW7hIQEqVOnTqbt+qix8+fPe3s6AAAABCrYValSRbZv355p+8qVK6VGjRq+KhcAAABye1TskCFDpH///nLp0iUzn9XmzZtlwYIFMm7cOJkxY4a3pwMAAECggl2vXr0kJSVFhg4dKhcuXJCuXbtKhQoV5L333pNHHnnEV+UCAACAP+axe/LJJ81y/PhxuXLlipQpUyYnpwEAAEAg+9iNGTNG9u7da16XKlWKUAcAAJBXg92SJUukWrVq0qBBA5k8ebL89ddfuVMyAAAA5G6w27Fjh1maN28uEyZMMP3rdLLi+fPnmz53AAAAyEOPFLv99tvl9ddfl3379skPP/xgpkAZOHCglCtXzvclBAAAQO4Fu7SioqKkcOHCEhERIZcvX77e0wEAAMCfwU6fPvHaa6+ZCYn1UWJbt26V0aNHy9GjR3NaDgAAAPh7upOGDRuaSYnvuOMOM6edax47AAAA5LFg16xZM/OECe1nBwAAgDwc7HTQBAAAAPJosBs8eLC88sorZqCEvs6KToECAACAIA1227Ztc4941dcAAADIo8FO56rz9BoAAAB5eLqTJ554Qs6dO5dp+/nz5817AAAAyCPB7uOPP5aLFy9m2q7b5s6d66tyAQAAILdGxZ49e1YcxzGL1tgVKlTI/V5qaqqsWLFCypQp4+31AQAA4O9gV7x4cQkJCTFLtWrVMr2v28eMGeOrcgEAACC3gp0OmtDauubNm8uSJUukRIkS7vf0ObGxsbESExPj7fUBAADg72DXtGlT93Nib7zxRlNDBwAAgDwW7Hbs2CE1a9aUAgUKyJkzZ+TXX3+96r61atXyZfkAAADgy2B35513ytGjR83gCH2ttXXaLJuRbteBFAAAAAjSYKfNr6VLl3a/BgAAQB4NdjowwtNrAAAA5PEJir/66iv3+tChQ81UKI0aNZLExERflw8AAAC5Fexef/11KVy4sHm9adMmmTx5srz11ltSqlQpGTRokLenAwAAgL+nO3E5cOCA3Hzzzeb18uXLpVOnTvLUU09J48aN5Z577vFVuQAAAJDbNXZFihSREydOmNfffPONtGjRwrzWR4x5eoYsAAAAgrTGrmXLltKnTx+pU6eO7N69W9q1a2e2//bbb1K5cuXcKCMAAAByo8ZuypQp0rBhQ/nrr7/Mo8VKlixptsfHx8ujjz7q7ekAAAAQqBo7HQGrAyYyGjNmjK/KBAAAAH8EO3X69GmZOXOm7Nq1yzxtonr16tK7d2+Jjo7OyekAAAAQiKbYLVu2yE033STvvvuunDx5Uo4fP25e67atW7f6okwAAADwR42dzlXXoUMH+eijjyQs7P8OT0lJMQMqBg4cKGvXrs1JOQAAAODvYKc1dmlDnTlJWJh5AkVcXNz1lgcAAAD+aootVqyY7N+/3+PExUWLFs1pOQAAAODvYNelSxczUGLRokUmzB08eFAWLlxommKZ7gQAACAPNcW+8847ZiRs9+7dTd86FR4eLk8//bS88cYbuVFGAAAA5Eawi4iIkPfee0/GjRsne/fuFcdxzLNjIyMjvT0VAAAAAtEUe+HCBenfv79UqFBBypQpY5pey5cvL7Vq1SLUAQAA5KVgN2rUKJkzZ455Nuwjjzwiq1evNs2vAAAAyGNNsUuXLjVPm9BQpx5//HFp3LixpKamSmhoaG6WEcBVaFeIi5dT/XrNC8n+vR4AIBeCnY6AbdKkiXu9Xr16Zv66w4cPS6VKlby4JABfhbpO0zdJfOKpQBcFAJDXmmK1Zk4HTqSlwc41MhaAf2lNXSBDXVzsDVI4nNp6AMiTNXZaO9CzZ08pWLCge9ulS5ekX79+EhUVla7JFoB/bXm5hURG+DdkaajTqY8AAHkw2PXo0SPTNu1nByDwNNRFRng9exEAwDLZ/k0we/bs3C0JAAAA/PtIMQAAAASngAe7qVOnSpUqVaRQoUJSt25dWbduXbaO27Bhgxm8ceedd+Z6GQEAAPKCgAa7RYsWycCBA2XEiBGybds2M51KmzZtZP/+/Vked+bMGfOs2nvvvddvZQUAAAh2AQ12EyZMkN69e5vHk1WvXl0mTpxo5sSbNm1alsf17dtXunbtKg0bNvRbWQEAAIJdwIJdcnKyxMfHS6tWrdJt1/WNGzdmOYhj79695hFnAAAAuM5g98knn5jHicXExEhiYqLZprVtn3/+ebbPcfz4cTPpcdmyZdNt1/WjR496PGbPnj0ybNgwmTdvnulflx1JSUly9uzZdAsAAICNvA522kw6ePBgadu2rZw+fdqEM1W8eHET7ryVcYJTnQjZ06Sneh1tfh0zZoxUq1Yt2+cfN26cREdHuxcefwYAAGzldbB7//335aOPPjIDHkJD//9M93FxcfLrr79m+zylSpUyx2esnTt27FimWjx17tw52bJlizz77LOmtk6XsWPHyi+//GJef//99x6vM3z4cDPYwrXoM28BAABs5PVU9QkJCVKnTp1M2/VRY+fPn8/2efS5szq9yerVq6Vjx47u7bp+//33Z9q/WLFimYKjTpWigW7x4sVmyhRPtFxpH4MGAABgK6+DnQao7du3S2xsbLrtK1eulBo1anh1Lm3S7datm6nt0xGuH374oZnqRJ8/66ptO3TokMydO1cKFCggNWvWTHd8mTJlzPx3GbcDAADkR14HuyFDhkj//v3l0qVLpj/c5s2bZcGCBaYv24wZM7w6V5cuXeTEiROmSfXIkSMmoK1YscIdGnXbtea0AwAAwP8JcTSdeUn72L366qvu/moVKlSQ0aNHmznpgp2OitVBFNrfTpt3gbzqQnKK1Bi5yrzeOba1REZ4/e80AEAe4E12ydFvgieffNIsOmXJlStXTJMoAAAAAuu6/omvI1sBAACQhwdPeJpnzmXfvn3XWyYAAAD4I9gNHDgw3frly5dl27Zt8vXXX5uBFQAAAMgjwe65557zuH3KlClmAmEAAADkoWfFetKmTRtZsmSJr04HAACAQAU7ffpDiRIlfHU6AAAA5HZTrD5OLO3gCZ0GT5/3+tdff5lHfAEAACCPBLsHHngg3bo+6qt06dJyzz33yG233ebLsgEAACC3gl1KSopUrlxZWrduLeXKlfPmUAAAAARTH7uwsDB5+umnJSkpKfdKBAAAAP8Mnqhfv76Ztw4AAAB5vI/dM888I88//7wcPHhQ6tatK1FRUener1Wrli/LBwAAAF8HuyeeeEImTpwoXbp0MesDBgxwv6ejZHV0rP5MTU3N7ikBAAAQiGD38ccfyxtvvCEJCQm+vD4AAAD8Hey0Rk7Fxsb66toAAAAI1OCJtBMTAwAAIA8PnqhWrdo1w93Jkyevt0wAAADI7WA3ZswYiY6Ozsl1AAAAEEzB7pFHHpEyZcrkXmkAAACQ+33s6F8HAABgSbBzjYoFAABAHm+KvXLlSu6WBAAAAP59ViwAAACCE8EOAADAEgQ7AAAASxDsAAAALEGwAwAAsATBDgAAwBIEOwAAAEsQ7AAAACxBsAMAAMhvT54A4Hv6qL6Ll1NzdOyF5JwdBwCwF8EOCGCo6zR9k8Qnngp0UQAAlqApFggQranzRaiLi71BCoeH+qRMAIC8jRo7IAhsebmFREbkLJxpqAsJCfF5mQAAeQ/BDggCGuoiI/jjCAC4PjTFAgAAWIJgBwAAYAmCHQAAgCUIdgAAAJYg2AEAAFiCYAcAAGAJgh0AAIAlCHYAAACWINgBAABYgmAHAABgCYIdAACAJQh2AAAAliDYAQAAWIJgBwAAYAmCHQAAgCUIdgAAAJYg2AEAAFiCYAcAAGAJgh0AAIAlCHYAAACWINgBAABYgmAHAABgiYAHu6lTp0qVKlWkUKFCUrduXVm3bt1V9126dKm0bNlSSpcuLcWKFZOGDRvKqlWr/FpeAACAYBXQYLdo0SIZOHCgjBgxQrZt2yZNmjSRNm3ayP79+z3uv3btWhPsVqxYIfHx8dKsWTNp3769ORYAACC/C3EcxwnUxevXry933XWXTJs2zb2tevXq8sADD8i4ceOydY7bb79dunTpIiNHjszW/mfPnpXo6Gg5c+aMqfUDAuVCcorUGPl/Nc47x7aWyIiwQBcJABCEvMkuAauxS05ONrVurVq1Srdd1zdu3Jitc1y5ckXOnTsnJUqUyKVSAgAA5B0BqyI4fvy4pKamStmyZdNt1/WjR49m6xzjx4+X8+fPS+fOna+6T1JSklnSpl4AAAAbBXzwREhISLp1bRnOuM2TBQsWyOjRo00/vTJlylx1P23S1epL11KpUiWflBsAACDYBCzYlSpVSkJDQzPVzh07dixTLV5GGuZ69+4tn376qbRo0SLLfYcPH27apF3LgQMHfFJ+AACAYBOwYBcREWGmN1m9enW67breqFGjLGvqevbsKfPnz5d27dpd8zoFCxY0HQ3TLgAAADYK6DC8wYMHS7du3SQuLs7MSffhhx+aqU769evnrm07dOiQzJ071x3qunfvLu+99540aNDAXdtXuHBh08wKAACQnwU02Ok0JSdOnJCxY8fKkSNHpGbNmmaOutjYWPO+bks7p90HH3wgKSkp0r9/f7O49OjRQ+bMmROQzwAAABAsAjqPXSAwjx2CBfPYAQCsmccOAAAAvkWwAwAAsATBDgAAwBIEOwAAAEsQ7AAAACxBsAMAALAEwQ4AAMASBDsAAABLEOwAAAAsQbADAACwBMEOAADAEgQ7AAAAS/DUcVjBcRy5eDlV8pILyXmrvACA4EewgxWhrtP0TRKfeCrQRQEAIKBoikWepzV1eTnUxcXeIIXDQwNdDACABaixg1W2vNxCIiPyVkjSUBcSEhLoYgAALECwg1U01EVG8L81ACB/oikWAADAEgQ7AAAASxDsAAAALEGwAwAAsATBDgAAwBIEOwAAAEsQ7AAAACxBsAMAALAEwQ4AAMASBDsAAABLEOwAAAAsQbADAACwBMEOAADAEgQ7AAAASxDsAAAALEGwAwAAsATBDgAAwBIEOwAAAEsQ7AAAACxBsAMAALAEwQ4AAMASBDsAAABLEOwAAAAsQbADAACwBMEOAADAEgQ7AAAASxDsAAAALEGwAwAAsATBDgAAwBIEOwAAAEsQ7AAAACxBsAMAALAEwQ4AAMASBDsAAABLEOwAAAAsQbADAACwBMEOAADAEgQ7AAAASxDsAAAALEGwAwAAsATBDgAAwBIEOwAAAEsQ7AAAACwR8GA3depUqVKlihQqVEjq1q0r69aty3L/NWvWmP10/6pVq8r06dP9VlYAAIBgFtBgt2jRIhk4cKCMGDFCtm3bJk2aNJE2bdrI/v37Pe6fkJAgbdu2Nfvp/i+99JIMGDBAlixZ4veyAwAABJsQx3GcQF28fv36ctddd8m0adPc26pXry4PPPCAjBs3LtP+L774onzxxReya9cu97Z+/frJL7/8Ips2bcrWNc+ePSvR0dFy5swZKVasmOQG/UovXk7NlXMjswvJqRL36rfm9c6xrSUyIizQRQIAwGe8yS4B+w2YnJws8fHxMmzYsHTbW7VqJRs3bvR4jIY3fT+t1q1by8yZM+Xy5csSHh6e6ZikpCSzpP1ycpuGuhojV+X6dQAAAIKiKfb48eOSmpoqZcuWTbdd148ePerxGN3uaf+UlBRzPk+05k9TrmupVKmSDz8Fgklc7A1SODw00MUAACBgAt5mFRISkqkZM+O2a+3vabvL8OHDZfDgwelq7HI73Gm40CZB+Jd+71n9vwMAgO0CFuxKlSoloaGhmWrnjh07lqlWzqVcuXIe9w8LC5OSJUt6PKZgwYJm8ScNF/TzAgAA+aYpNiIiwkxbsnr16nTbdb1Ro0Yej2nYsGGm/b/55huJi4vz2L8OAAAgPwnodCfaRDpjxgyZNWuWGek6aNAgM9WJjnR1NaN2797dvb9uT0xMNMfp/nqcDpx44YUXAvgpAAAAgkNA2wu7dOkiJ06ckLFjx8qRI0ekZs2asmLFComNjTXv67a0c9rpRMb6vgbAKVOmSExMjEyaNEkeeuihAH4KAACA4BDQeewCwR/z2AEAAAQiuwT8kWIAAADwDYIdAACAJQh2AAAAliDYAQAAWIJgBwAAYAmCHQAAgCUIdgAAAJYg2AEAAFiCYAcAAGAJgh0AAIAlAvqs2EBwPUFNH88BAAAQ7FyZJTtPgc13we7cuXPmZ6VKlQJdFAAAAK8yjD4zNishTnbin0WuXLkihw8flqJFi0pISEiupmsNjwcOHLjmA3vhH9yT4MR9CT7ck+DEfcm/98VxHBPqYmJipECBrHvR5bsaO/1CKlas6Lfr6U3mD2Bw4Z4EJ+5L8OGeBCfuS/68L9HXqKlzYfAEAACAJQh2AAAAliDY5ZKCBQvKqFGjzE8EB+5JcOK+BB/uSXDivgSngkF2X/Ld4AkAAABbUWMHAABgCYIdAACAJQh2AAAAliDYXYepU6dKlSpVpFChQlK3bl1Zt25dlvuvWbPG7Kf7V61aVaZPn+63suYX3tyTpUuXSsuWLaV06dJm7qGGDRvKqlWr/Fre/MLbPysuGzZskLCwMLnzzjtzvYz5jbf3JCkpSUaMGCGxsbGmk/hNN90ks2bN8lt58wtv78u8efOkdu3aEhkZKeXLl5devXrJiRMn/FZe261du1bat29vJgbWhxosX778mscE/He9Dp6A9xYuXOiEh4c7H330kbNz507nueeec6KiopzExESP++/bt8+JjIw0++n+epwev3jxYr+X3Vbe3hN9/80333Q2b97s7N692xk+fLg5fuvWrX4vu828vS8up0+fdqpWreq0atXKqV27tt/Kmx/k5J506NDBqV+/vrN69WonISHB+fe//+1s2LDBr+W2nbf3Zd26dU6BAgWc9957z/yO0fXbb7/deeCBB/xedlutWLHCGTFihLNkyRIdaOosW7Ysy/2D4Xc9wS6H6tWr5/Tr1y/dtttuu80ZNmyYx/2HDh1q3k+rb9++ToMGDXK1nPmJt/fEkxo1ajhjxozJhdLlXzm9L126dHFefvllZ9SoUQS7AN+TlStXOtHR0c6JEyf8VML8ydv78vbbb5t//KQ1adIkp2LFirlazvxKshHsguF3PU2xOZCcnCzx8fHSqlWrdNt1fePGjR6P2bRpU6b9W7duLVu2bJHLly/nannzg5zcE0/PEdZn8ZUoUSKXSpn/5PS+zJ49W/bu3WvmhkLg78kXX3whcXFx8tZbb0mFChWkWrVq8sILL8jFixf9VGr75eS+NGrUSA4ePCgrVqwwzxL9888/ZfHixdKuXTs/lRrB+Ls+3z0r1heOHz8uqampUrZs2XTbdf3o0aMej9HtnvZPSUkx59O+EfDvPclo/Pjxcv78eencuXMulTL/ycl92bNnjwwbNsz0LdL+dQj8Pdm3b5+sX7/e9BlatmyZOcczzzwjJ0+epJ9dAO+LBjvtY9elSxe5dOmS+X3SoUMHef/99/1UagTj73pq7K6DdqRMS//FlHHbtfb3tB3+uycuCxYskNGjR8uiRYukTJkyuVjC/Cm790V/sXXt2lXGjBljaoUQHH9WtDZb39MQUa9ePWnbtq1MmDBB5syZQ61dAO/Lzp07ZcCAATJy5EhT2/f1119LQkKC9OvXz0+lRTD+ruefwzlQqlQpCQ0NzfSvqGPHjmVK6i7lypXzuL/WSJQsWTJXy5sf5OSeuGiY6927t3z22WfSokWLXC5p/uLtfdGmcG2y2LZtmzz77LPuUKF/MeqflW+++UaaN2/ut/LbKCd/VrSWQZtgo6Oj3duqV69u7os2Bd5yyy25Xm7b5eS+jBs3Tho3bixDhgwx67Vq1ZKoqChp0qSJvPrqq7QEBUAw/K6nxi4HIiIizFDm1atXp9uu61o17olOpZFxf/0lpf1WwsPDc7W8+UFO7omrpq5nz54yf/58+qUEwX3RaWd+/fVX2b59u3vR2odbb73VvK5fv74fS2+nnPxZ0fBw+PBh+fvvv93bdu/eLQUKFJCKFSvmepnzg5zclwsXLph7kJaGQ8XTQgMjKH7X+22YhqXD0mfOnGmGNA8cONAMS//jjz/M+zqKqVu3bpmGQA8aNMjsr8cx3Ulg78n8+fOdsLAwZ8qUKc6RI0fci06zgcDdl4wYFRv4e3Lu3Dkz0rJTp07Ob7/95qxZs8a55ZZbnD59+gTwU9jH2/sye/Zs83fY1KlTnb179zrr16934uLizOha+Ib+v79t2zazaGSaMGGCee2agiYYf9cT7K6DBoLY2FgnIiLCueuuu8xfdi49evRwmjZtmm7/H3/80alTp47Zv3Llys60adMCUGq7eXNP9LX+Qc246H4I7J+VtAh2wXFPdu3a5bRo0cIpXLiwCXmDBw92Lly4EICS283b+6LTm+g0TXpfypcv7zz22GPOwYMHA1ByO/3www9Z/p4Ixt/1Ifof/9QNAgAAIDfRxw4AAMASBDsAAABLEOwAAAAsQbADAACwBMEOAADAEgQ7AAAASxDsAAAALEGwAwAAsATBDkDQmDNnjhQvXlzyqsqVK8vEiROz3Gf06NFy5513+q1MAPIXgh0An+rZs6eEhIRkWv7zn/8ERXBMW6by5ctL586dJSEhwSfn//nnn+Wpp55yr+s1li9fnm6fF154Qb777jvx5+csW7astG/fXn777bd8FbSB/IhgB8Dn7rvvPjly5Ei6pUqVKhIMihUrZspz+PBhmT9/vmzfvl06dOggqamp133u0qVLS2RkZJb7FClSREqWLCn+/JxfffWVnD9/Xtq1ayfJycm5fm0AgUOwA+BzBQsWlHLlyqVbQkNDZcKECXLHHXdIVFSUVKpUSZ555hn5+++/r3qeX375RZo1ayZFixY1QaVu3bqyZcsW9/sbN26Uu+++WwoXLmzON2DAABNgsqI1WFoera3Tc48aNUr+53/+x12jOG3aNLnpppskIiJCbr31Vvnkk08yNaXeeOON5jPGxMSYa3pqitXXqmPHjuaarvW0TbGrVq2SQoUKyenTp9NdQ8/ZtGlTn33OuLg4GTRokCQmJsrvv//u3ier+/Hjjz9Kr1695MyZM+6aPy270nA4dOhQqVChgjm2fv36Zn8AgUewA+A3BQoUkEmTJpkg9fHHH8v3339vAsLVPPbYY1KxYkXTxBkfHy/Dhg2T8PBw896vv/4qrVu3lgcffFB27NghixYtkvXr18uzzz7rVZk0LKnLly/LsmXL5LnnnpPnn3/elLFv374m3Pzwww9mn8WLF8u7774rH3zwgezZs8c0s2ow8kTLrGbPnm1qzlzrabVo0cI0dS5ZssS9TWsOP/30U/PZffU5NThq7aRyfX/Xuh+NGjUyIdVV86eLNiMr/U42bNggCxcuNGV6+OGHTS2tficAAswBAB/q0aOHExoa6kRFRbmXTp06edz3008/dUqWLOlenz17thMdHe1eL1q0qDNnzhyPx3br1s156qmn0m1bt26dU6BAAefixYsej8l4/gMHDjgNGjRwKlas6CQlJTmNGjVynnzyyXTHPPzww07btm3N6/HjxzvVqlVzkpOTPZ4/NjbWeffdd93r+lfssmXL0u0zatQop3bt2u71AQMGOM2bN3evr1q1yomIiHBOnjx5XZ9Tr63ffWRkpHmtS4cOHZysXOt+qP/85z9OSEiIc+jQoXTb7733Xmf48OFZnh9A7gsLdLAEYB9t4tQmTRdtrlNa8/X666/Lzp075ezZs5KSkiKXLl0yzYqufdIaPHiw9OnTxzSHau2W1gxpM6nSGjxtPp03b557f81SV65cMYMhqlev7rFs2rSo/dx03wsXLshdd90lS5cuNU2vu3btSjf4QTVu3Fjee+8981qvr7VYVatWNTVUbdu2NYMSwsJy/lep1sw1bNjQ9IXTpl39PHreG2644bo+pzZfb9261XzHa9askbffflumT5+ebh9v74fSc+r1q1Wrlm57UlKSX/oOAsgawQ6Az2kouPnmm9Nt0/5dGlj69esnr7zyipQoUcI0Kfbu3ds0g3qifbq6du1qOv+vXLnS9IfT5j/tt6bBRptK0/Zxc9E+cFfjCjzaDKmjRTMGGO1LlpaGGNc27YemfdRWr14t3377remTpoFJg1PaJk5v1KtXz4RV/VxPP/20aQ7W5luXnH5O/Xyue3DbbbfJ0aNHpUuXLrJ27doc3w9XebS/pAZO/ZmWBmYAgUWwA+AXOuhBa4TGjx9vQofSvmTXojVDumjn/0cffdSEHg12WtOm03dkDJDXkjbwZKS1Xxpuunfvnm7gQtpaMe2Tp6Nodenfv78JTdoPTsuTkYa97Iy21fCqNXLan1DLp6NXXXL6OTPS708HS2hw1O8vO/dDazEzlr9OnTpm27Fjx6RJkybXVSYAvsfgCQB+obVSGiTef/992bdvn2lezdg0mNbFixfNAAEdbam1S9pZXwcguELWiy++KJs2bTLhSqcs0Y77X3zxhfzrX//KcRmHDBli5m7Tcun5NAhpM61r0IC+N3PmTDPYwPUZNOjFxsZ6PJ+OhNU567S27NSpU1k2x2ot4muvvSadOnUyI2VdfPU5dRCENmtrrafWQmbnfmj5dZSsfobjx4+bpmsN2VpeDb/63WhzsN6XN998U1asWOFVmQDkAj/04wOQzwZP3H///R7fmzBhglO+fHmncOHCTuvWrZ25c+eaTv2nTp3K1FlfBzM88sgjTqVKlcxggpiYGOfZZ59NN2Bg8+bNTsuWLZ0iRYqYgQK1atVyXnvttauWzdNggIymTp3qVK1a1QkPDzcDJbSMLjoQon79+k6xYsXM9XTgxbfffnvVwRNffPGFc/PNNzthYWHmPU+DJ1z+8Y9/mO/i+++/z/Serz5nYmKiKcuiRYuydT9Uv379zIAK3a5lVzp4ZOTIkU7lypXN91SuXDmnY8eOzo4dO7L8bgHkvhD9T24ERgAAAPgXTbEAAACWINgBAABYgmAHAABgCYIdAACAJQh2AAAAliDYAQAAWIJgBwAAYAmCHQAAgCUIdgAAAJYg2AEAAFiCYAcAAGAJgh0AAIDY4f8BsKhElPiQmSwAAAAASUVORK5CYII=", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["\n", "# --- Curva ROC\n", "fpr, tpr, _ = roc_curve(y_test, y_proba)\n", "plt.figure()\n", "plt.plot(fpr, tpr)\n", "plt.xlabel(\"False Positive Rate\")\n", "plt.ylabel(\"True Positive Rate\")\n", "plt.title(\"ROC Curve (Test)\")\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# --- <PERSON><PERSON><PERSON> Precision-Recall\n", "precision, recall, _ = precision_recall_curve(y_test, y_proba)\n", "plt.figure()\n", "plt.plot(recall, precision)\n", "plt.xlabel(\"Recall\")\n", "plt.ylabel(\"Precision\")\n", "plt.title(\"Precision-<PERSON><PERSON><PERSON> (Test)\")\n", "plt.tight_layout()\n", "plt.show()"]}], "metadata": {"kernelspec": {"display_name": "ia-bio", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}