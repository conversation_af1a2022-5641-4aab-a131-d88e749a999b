{"cells": [{"cell_type": "markdown", "id": "6340b527", "metadata": {}, "source": ["# Importancia por permutación y PDP\n", "Con el mejor modelo base, calcula la importancia por permutación en test con métrica AUPRC; muestra el Top-10. Genera un PDP para age_years (o la primera numérica disponible). Y guarda ambos gráficos en figures/."]}, {"cell_type": "code", "execution_count": 23, "id": "26e899b5", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import os\n", "\n", "from sklearn.compose import ColumnTransformer\n", "from sklearn.pipeline import Pipeline\n", "from sklearn.impute import SimpleImputer\n", "from sklearn.preprocessing import StandardScaler, OneHotEncoder\n", "from sklearn.linear_model import LogisticRegression\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.inspection import permutation_importance, PartialDependenceDisplay\n", "from sklearn.metrics import average_precision_score\n"]}, {"cell_type": "code", "execution_count": 24, "id": "0fb50337", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Data frame loaded (200, 19)\n"]}], "source": ["os.makedirs(\"figures\", exist_ok=True)\n", "\n", "df = pd.read_csv(\"../../hospital_general_sim_min.csv\")\n", "print(\"Data frame loaded\", df.shape)\n"]}, {"cell_type": "code", "execution_count": 25, "id": "cf32fe34", "metadata": {}, "outputs": [], "source": ["y = df[\"readmit_30d\"].astype(int)\n", "X = df.drop(columns=[\"readmit_30d\"])\n"]}, {"cell_type": "code", "execution_count": 26, "id": "01350cd4", "metadata": {}, "outputs": [], "source": ["\n", "num_cols = X.select_dtypes(include=\"number\").columns.tolist()\n", "cat_cols = X.columns.difference(num_cols).tolist()\n", "\n", "preprocessor = ColumnTransformer([\n", "    (\"num\", Pipeline([\n", "        (\"imputer\", SimpleImputer(strategy=\"median\")),\n", "        (\"scaler\", StandardScaler())\n", "    ]), num_cols),\n", "    (\"cat\", <PERSON><PERSON><PERSON>([\n", "        (\"imputer\", SimpleImputer(strategy=\"most_frequent\")),\n", "        (\"encoder\", OneHotEncoder(handle_unknown=\"ignore\", sparse_output=False))\n", "    ]), cat_cols)\n", "])\n"]}, {"cell_type": "code", "execution_count": 27, "id": "1bd174cb", "metadata": {}, "outputs": [{"data": {"text/html": ["<style>#sk-container-id-1 {\n", "  /* Definition of color scheme common for light and dark mode */\n", "  --sklearn-color-text: #000;\n", "  --sklearn-color-text-muted: #666;\n", "  --sklearn-color-line: gray;\n", "  /* Definition of color scheme for unfitted estimators */\n", "  --sklearn-color-unfitted-level-0: #fff5e6;\n", "  --sklearn-color-unfitted-level-1: #f6e4d2;\n", "  --sklearn-color-unfitted-level-2: #ffe0b3;\n", "  --sklearn-color-unfitted-level-3: chocolate;\n", "  /* Definition of color scheme for fitted estimators */\n", "  --sklearn-color-fitted-level-0: #f0f8ff;\n", "  --sklearn-color-fitted-level-1: #d4ebff;\n", "  --sklearn-color-fitted-level-2: #b3dbfd;\n", "  --sklearn-color-fitted-level-3: cornflowerblue;\n", "\n", "  /* Specific color for light theme */\n", "  --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, white)));\n", "  --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-icon: #696969;\n", "\n", "  @media (prefers-color-scheme: dark) {\n", "    /* Redefinition of color scheme for dark theme */\n", "    --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, #111)));\n", "    --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-icon: #878787;\n", "  }\n", "}\n", "\n", "#sk-container-id-1 {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "#sk-container-id-1 pre {\n", "  padding: 0;\n", "}\n", "\n", "#sk-container-id-1 input.sk-hidden--visually {\n", "  border: 0;\n", "  clip: rect(1px 1px 1px 1px);\n", "  clip: rect(1px, 1px, 1px, 1px);\n", "  height: 1px;\n", "  margin: -1px;\n", "  overflow: hidden;\n", "  padding: 0;\n", "  position: absolute;\n", "  width: 1px;\n", "}\n", "\n", "#sk-container-id-1 div.sk-dashed-wrapped {\n", "  border: 1px dashed var(--sklearn-color-line);\n", "  margin: 0 0.4em 0.5em 0.4em;\n", "  box-sizing: border-box;\n", "  padding-bottom: 0.4em;\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "#sk-container-id-1 div.sk-container {\n", "  /* jup<PERSON>r's `normalize.less` sets `[hidden] { display: none; }`\n", "     but bootstrap.min.css set `[hidden] { display: none !important; }`\n", "     so we also need the `!important` here to be able to override the\n", "     default hidden behavior on the sphinx rendered scikit-learn.org.\n", "     See: https://github.com/scikit-learn/scikit-learn/issues/21755 */\n", "  display: inline-block !important;\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-1 div.sk-text-repr-fallback {\n", "  display: none;\n", "}\n", "\n", "div.sk-parallel-item,\n", "div.sk-serial,\n", "div.sk-item {\n", "  /* draw centered vertical line to link estimators */\n", "  background-image: linear-gradient(var(--sklearn-color-text-on-default-background), var(--sklearn-color-text-on-default-background));\n", "  background-size: 2px 100%;\n", "  background-repeat: no-repeat;\n", "  background-position: center center;\n", "}\n", "\n", "/* Parallel-specific style estimator block */\n", "\n", "#sk-container-id-1 div.sk-parallel-item::after {\n", "  content: \"\";\n", "  width: 100%;\n", "  border-bottom: 2px solid var(--sklearn-color-text-on-default-background);\n", "  flex-grow: 1;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel {\n", "  display: flex;\n", "  align-items: stretch;\n", "  justify-content: center;\n", "  background-color: var(--sklearn-color-background);\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel-item {\n", "  display: flex;\n", "  flex-direction: column;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel-item:first-child::after {\n", "  align-self: flex-end;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel-item:last-child::after {\n", "  align-self: flex-start;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel-item:only-child::after {\n", "  width: 0;\n", "}\n", "\n", "/* Serial-specific style estimator block */\n", "\n", "#sk-container-id-1 div.sk-serial {\n", "  display: flex;\n", "  flex-direction: column;\n", "  align-items: center;\n", "  background-color: var(--sklearn-color-background);\n", "  padding-right: 1em;\n", "  padding-left: 1em;\n", "}\n", "\n", "\n", "/* Toggleable style: style used for estimator/Pipeline/ColumnTransformer box that is\n", "clickable and can be expanded/collapsed.\n", "- Pipeline and ColumnTransformer use this feature and define the default style\n", "- Estimators will overwrite some part of the style using the `sk-estimator` class\n", "*/\n", "\n", "/* Pipeline and ColumnTransformer style (default) */\n", "\n", "#sk-container-id-1 div.sk-toggleable {\n", "  /* Default theme specific background. It is overwritten whether we have a\n", "  specific estimator or a Pipeline/ColumnTransformer */\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "/* Toggleable label */\n", "#sk-container-id-1 label.sk-toggleable__label {\n", "  cursor: pointer;\n", "  display: flex;\n", "  width: 100%;\n", "  margin-bottom: 0;\n", "  padding: 0.5em;\n", "  box-sizing: border-box;\n", "  text-align: center;\n", "  align-items: start;\n", "  justify-content: space-between;\n", "  gap: 0.5em;\n", "}\n", "\n", "#sk-container-id-1 label.sk-toggleable__label .caption {\n", "  font-size: 0.6rem;\n", "  font-weight: lighter;\n", "  color: var(--sklearn-color-text-muted);\n", "}\n", "\n", "#sk-container-id-1 label.sk-toggleable__label-arrow:before {\n", "  /* <PERSON> on the left of the label */\n", "  content: \"▸\";\n", "  float: left;\n", "  margin-right: 0.25em;\n", "  color: var(--sklearn-color-icon);\n", "}\n", "\n", "#sk-container-id-1 label.sk-toggleable__label-arrow:hover:before {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "/* Toggleable content - dropdown */\n", "\n", "#sk-container-id-1 div.sk-toggleable__content {\n", "  display: none;\n", "  text-align: left;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 div.sk-toggleable__content.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 div.sk-toggleable__content pre {\n", "  margin: 0.2em;\n", "  border-radius: 0.25em;\n", "  color: var(--sklearn-color-text);\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 div.sk-toggleable__content.fitted pre {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 input.sk-toggleable__control:checked~div.sk-toggleable__content {\n", "  /* Expand drop-down */\n", "  display: block;\n", "  width: 100%;\n", "  overflow: visible;\n", "}\n", "\n", "#sk-container-id-1 input.sk-toggleable__control:checked~label.sk-toggleable__label-arrow:before {\n", "  content: \"▾\";\n", "}\n", "\n", "/* Pipeline/ColumnTransformer-specific style */\n", "\n", "#sk-container-id-1 div.sk-label input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-1 div.sk-label.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator-specific style */\n", "\n", "/* Colorize estimator box */\n", "#sk-container-id-1 div.sk-estimator input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-1 div.sk-estimator.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "#sk-container-id-1 div.sk-label label.sk-toggleable__label,\n", "#sk-container-id-1 div.sk-label label {\n", "  /* The background is the default theme color */\n", "  color: var(--sklearn-color-text-on-default-background);\n", "}\n", "\n", "/* On hover, darken the color of the background */\n", "#sk-container-id-1 div.sk-label:hover label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "/* Label box, darken color on hover, fitted */\n", "#sk-container-id-1 div.sk-label.fitted:hover label.sk-toggleable__label.fitted {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator label */\n", "\n", "#sk-container-id-1 div.sk-label label {\n", "  font-family: monospace;\n", "  font-weight: bold;\n", "  display: inline-block;\n", "  line-height: 1.2em;\n", "}\n", "\n", "#sk-container-id-1 div.sk-label-container {\n", "  text-align: center;\n", "}\n", "\n", "/* Estimator-specific */\n", "#sk-container-id-1 div.sk-estimator {\n", "  font-family: monospace;\n", "  border: 1px dotted var(--sklearn-color-border-box);\n", "  border-radius: 0.25em;\n", "  box-sizing: border-box;\n", "  margin-bottom: 0.5em;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 div.sk-estimator.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "/* on hover */\n", "#sk-container-id-1 div.sk-estimator:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-1 div.sk-estimator.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Specification for estimator info (e.g. \"i\" and \"?\") */\n", "\n", "/* Common style for \"i\" and \"?\" */\n", "\n", ".sk-estimator-doc-link,\n", "a:link.sk-estimator-doc-link,\n", "a:visited.sk-estimator-doc-link {\n", "  float: right;\n", "  font-size: smaller;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1em;\n", "  height: 1em;\n", "  width: 1em;\n", "  text-decoration: none !important;\n", "  margin-left: 0.5em;\n", "  text-align: center;\n", "  /* unfitted */\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted,\n", "a:link.sk-estimator-doc-link.fitted,\n", "a:visited.sk-estimator-doc-link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "div.sk-estimator:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "div.sk-estimator.fitted:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "/* Span, style for the box shown on hovering the info icon */\n", ".sk-estimator-doc-link span {\n", "  display: none;\n", "  z-index: 9999;\n", "  position: relative;\n", "  font-weight: normal;\n", "  right: .2ex;\n", "  padding: .5ex;\n", "  margin: .5ex;\n", "  width: min-content;\n", "  min-width: 20ex;\n", "  max-width: 50ex;\n", "  color: var(--sklearn-color-text);\n", "  box-shadow: 2pt 2pt 4pt #999;\n", "  /* unfitted */\n", "  background: var(--sklearn-color-unfitted-level-0);\n", "  border: .5pt solid var(--sklearn-color-unfitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted span {\n", "  /* fitted */\n", "  background: var(--sklearn-color-fitted-level-0);\n", "  border: var(--sklearn-color-fitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link:hover span {\n", "  display: block;\n", "}\n", "\n", "/* \"?\"-specific style due to the `<a>` HTML tag */\n", "\n", "#sk-container-id-1 a.estimator_doc_link {\n", "  float: right;\n", "  font-size: 1rem;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1rem;\n", "  height: 1rem;\n", "  width: 1rem;\n", "  text-decoration: none;\n", "  /* unfitted */\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "}\n", "\n", "#sk-container-id-1 a.estimator_doc_link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "#sk-container-id-1 a.estimator_doc_link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "#sk-container-id-1 a.estimator_doc_link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "}\n", "\n", ".estimator-table summary {\n", "    padding: .5rem;\n", "    font-family: monospace;\n", "    cursor: pointer;\n", "}\n", "\n", ".estimator-table details[open] {\n", "    padding-left: 0.1rem;\n", "    padding-right: 0.1rem;\n", "    padding-bottom: 0.3rem;\n", "}\n", "\n", ".estimator-table .parameters-table {\n", "    margin-left: auto !important;\n", "    margin-right: auto !important;\n", "}\n", "\n", ".estimator-table .parameters-table tr:nth-child(odd) {\n", "    background-color: #fff;\n", "}\n", "\n", ".estimator-table .parameters-table tr:nth-child(even) {\n", "    background-color: #f6f6f6;\n", "}\n", "\n", ".estimator-table .parameters-table tr:hover {\n", "    background-color: #e0e0e0;\n", "}\n", "\n", ".estimator-table table td {\n", "    border: 1px solid rgba(106, 105, 104, 0.232);\n", "}\n", "\n", ".user-set td {\n", "    color:rgb(255, 94, 0);\n", "    text-align: left;\n", "}\n", "\n", ".user-set td.value pre {\n", "    color:rgb(255, 94, 0) !important;\n", "    background-color: transparent !important;\n", "}\n", "\n", ".default td {\n", "    color: black;\n", "    text-align: left;\n", "}\n", "\n", ".user-set td i,\n", ".default td i {\n", "    color: black;\n", "}\n", "\n", ".copy-paste-icon {\n", "    background-image: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCA0NDggNTEyIj48IS0tIUZvbnQgQXdlc29tZSBGcmVlIDYuNy4yIGJ5IEBmb250YXdlc29tZSAtIGh0dHBzOi8vZm9udGF3ZXNvbWUuY29tIExpY2Vuc2UgLSBodHRwczovL2ZvbnRhd2Vzb21lLmNvbS9saWNlbnNlL2ZyZWUgQ29weXJpZ2h0IDIwMjUgRm9udGljb25zLCBJbmMuLS0+PHBhdGggZD0iTTIwOCAwTDMzMi4xIDBjMTIuNyAwIDI0LjkgNS4xIDMzLjkgMTQuMWw2Ny45IDY3LjljOSA5IDE0LjEgMjEuMiAxNC4xIDMzLjlMNDQ4IDMzNmMwIDI2LjUtMjEuNSA0OC00OCA0OGwtMTkyIDBjLTI2LjUgMC00OC0yMS41LTQ4LTQ4bDAtMjg4YzAtMjYuNSAyMS41LTQ4IDQ4LTQ4ek00OCAxMjhsODAgMCAwIDY0LTY0IDAgMCAyNTYgMTkyIDAgMC0zMiA2NCAwIDAgNDhjMCAyNi41LTIxLjUgNDgtNDggNDhMNDggNTEyYy0yNi41IDAtNDgtMjEuNS00OC00OEwwIDE3NmMwLTI2LjUgMjEuNS00OCA0OC00OHoiLz48L3N2Zz4=);\n", "    background-repeat: no-repeat;\n", "    background-size: 14px 14px;\n", "    background-position: 0;\n", "    display: inline-block;\n", "    width: 14px;\n", "    height: 14px;\n", "    cursor: pointer;\n", "}\n", "</style><body><div id=\"sk-container-id-1\" class=\"sk-top-container\"><div class=\"sk-text-repr-fallback\"><pre>Pipeline(steps=[(&#x27;preprocessor&#x27;,\n", "                 ColumnTransformer(transformers=[(&#x27;num&#x27;,\n", "                                                  Pipeline(steps=[(&#x27;imputer&#x27;,\n", "                                                                   SimpleImputer(strategy=&#x27;median&#x27;)),\n", "                                                                  (&#x27;scaler&#x27;,\n", "                                                                   StandardScaler())]),\n", "                                                  [&#x27;length_of_stay_days&#x27;,\n", "                                                   &#x27;age_years&#x27;, &#x27;bmi&#x27;, &#x27;hr_bpm&#x27;,\n", "                                                   &#x27;sbp_mmhg&#x27;, &#x27;spo2_pct&#x27;,\n", "                                                   &#x27;temp_c&#x27;, &#x27;hb_g_dl&#x27;,\n", "                                                   &#x27;creatinine_mg_dl&#x27;,\n", "                                                   &#x27;glucose_mg_dl&#x27;,\n", "                                                   &#x27;wbc_10e9_per_l&#x27;,\n", "                                                   &#x27;egfr_ml_min_1_73m2&#x27;]),\n", "                                                 (&#x27;cat&#x27;,\n", "                                                  Pipeline(steps=[(&#x27;imputer&#x27;,\n", "                                                                   SimpleImputer(strategy=&#x27;most_frequent&#x27;)),\n", "                                                                  (&#x27;encoder&#x27;,\n", "                                                                   OneHotEncoder(handle_unknown=&#x27;ignore&#x27;,\n", "                                                                                 sparse_output=False))]),\n", "                                                  [&#x27;admission_datetime&#x27;,\n", "                                                   &#x27;discharge_datetime&#x27;,\n", "                                                   &#x27;edad_band&#x27;, &#x27;episode_id&#x27;,\n", "                                                   &#x27;patient_id&#x27;, &#x27;sex&#x27;])])),\n", "                (&#x27;model&#x27;, LogisticRegression(max_iter=1000))])</pre><b>In a Jupyter environment, please rerun this cell to show the HTML representation or trust the notebook. <br />On GitHub, the HTML representation is unable to render, please try loading this page with nbviewer.org.</b></div><div class=\"sk-container\" hidden><div class=\"sk-item sk-dashed-wrapped\"><div class=\"sk-label-container\"><div class=\"sk-label fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-1\" type=\"checkbox\" ><label for=\"sk-estimator-id-1\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow\"><div><div>Pipeline</div></div><div><a class=\"sk-estimator-doc-link fitted\" rel=\"noreferrer\" target=\"_blank\" href=\"https://scikit-learn.org/1.7/modules/generated/sklearn.pipeline.Pipeline.html\">?<span>Documentation for Pipeline</span></a><span class=\"sk-estimator-doc-link fitted\">i<span>Fitted</span></span></div></label><div class=\"sk-toggleable__content fitted\" data-param-prefix=\"\">\n", "        <div class=\"estimator-table\">\n", "            <details>\n", "                <summary>Parameters</summary>\n", "                <table class=\"parameters-table\">\n", "                  <tbody>\n", "                    \n", "        <tr class=\"user-set\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('steps',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">steps&nbsp;</td>\n", "            <td class=\"value\">[(&#x27;preprocessor&#x27;, ...), (&#x27;model&#x27;, ...)]</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('transform_input',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">transform_input&nbsp;</td>\n", "            <td class=\"value\">None</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('memory',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">memory&nbsp;</td>\n", "            <td class=\"value\">None</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('verbose',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">verbose&nbsp;</td>\n", "            <td class=\"value\">False</td>\n", "        </tr>\n", "    \n", "                  </tbody>\n", "                </table>\n", "            </details>\n", "        </div>\n", "    </div></div></div><div class=\"sk-serial\"><div class=\"sk-item sk-dashed-wrapped\"><div class=\"sk-label-container\"><div class=\"sk-label fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-2\" type=\"checkbox\" ><label for=\"sk-estimator-id-2\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow\"><div><div>preprocessor: ColumnTransformer</div></div><div><a class=\"sk-estimator-doc-link fitted\" rel=\"noreferrer\" target=\"_blank\" href=\"https://scikit-learn.org/1.7/modules/generated/sklearn.compose.ColumnTransformer.html\">?<span>Documentation for preprocessor: ColumnTransformer</span></a></div></label><div class=\"sk-toggleable__content fitted\" data-param-prefix=\"preprocessor__\">\n", "        <div class=\"estimator-table\">\n", "            <details>\n", "                <summary>Parameters</summary>\n", "                <table class=\"parameters-table\">\n", "                  <tbody>\n", "                    \n", "        <tr class=\"user-set\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('transformers',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">transformers&nbsp;</td>\n", "            <td class=\"value\">[(&#x27;num&#x27;, ...), (&#x27;cat&#x27;, ...)]</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('remainder',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">remainder&nbsp;</td>\n", "            <td class=\"value\">&#x27;drop&#x27;</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('sparse_threshold',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">sparse_threshold&nbsp;</td>\n", "            <td class=\"value\">0.3</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('n_jobs',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">n_jobs&nbsp;</td>\n", "            <td class=\"value\">None</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('transformer_weights',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">transformer_weights&nbsp;</td>\n", "            <td class=\"value\">None</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('verbose',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">verbose&nbsp;</td>\n", "            <td class=\"value\">False</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('verbose_feature_names_out',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">verbose_feature_names_out&nbsp;</td>\n", "            <td class=\"value\">True</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('force_int_remainder_cols',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">force_int_remainder_cols&nbsp;</td>\n", "            <td class=\"value\">&#x27;deprecated&#x27;</td>\n", "        </tr>\n", "    \n", "                  </tbody>\n", "                </table>\n", "            </details>\n", "        </div>\n", "    </div></div></div><div class=\"sk-parallel\"><div class=\"sk-parallel-item\"><div class=\"sk-item\"><div class=\"sk-label-container\"><div class=\"sk-label fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-3\" type=\"checkbox\" ><label for=\"sk-estimator-id-3\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow\"><div><div>num</div></div></label><div class=\"sk-toggleable__content fitted\" data-param-prefix=\"preprocessor__num__\"><pre>[&#x27;length_of_stay_days&#x27;, &#x27;age_years&#x27;, &#x27;bmi&#x27;, &#x27;hr_bpm&#x27;, &#x27;sbp_mmhg&#x27;, &#x27;spo2_pct&#x27;, &#x27;temp_c&#x27;, &#x27;hb_g_dl&#x27;, &#x27;creatinine_mg_dl&#x27;, &#x27;glucose_mg_dl&#x27;, &#x27;wbc_10e9_per_l&#x27;, &#x27;egfr_ml_min_1_73m2&#x27;]</pre></div></div></div><div class=\"sk-serial\"><div class=\"sk-item\"><div class=\"sk-serial\"><div class=\"sk-item\"><div class=\"sk-estimator fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-4\" type=\"checkbox\" ><label for=\"sk-estimator-id-4\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow\"><div><div>SimpleImputer</div></div><div><a class=\"sk-estimator-doc-link fitted\" rel=\"noreferrer\" target=\"_blank\" href=\"https://scikit-learn.org/1.7/modules/generated/sklearn.impute.SimpleImputer.html\">?<span>Documentation for SimpleImputer</span></a></div></label><div class=\"sk-toggleable__content fitted\" data-param-prefix=\"preprocessor__num__imputer__\">\n", "        <div class=\"estimator-table\">\n", "            <details>\n", "                <summary>Parameters</summary>\n", "                <table class=\"parameters-table\">\n", "                  <tbody>\n", "                    \n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('missing_values',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">missing_values&nbsp;</td>\n", "            <td class=\"value\">nan</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"user-set\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('strategy',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">strategy&nbsp;</td>\n", "            <td class=\"value\">&#x27;median&#x27;</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('fill_value',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">fill_value&nbsp;</td>\n", "            <td class=\"value\">None</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('copy',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">copy&nbsp;</td>\n", "            <td class=\"value\">True</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('add_indicator',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">add_indicator&nbsp;</td>\n", "            <td class=\"value\">False</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('keep_empty_features',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">keep_empty_features&nbsp;</td>\n", "            <td class=\"value\">False</td>\n", "        </tr>\n", "    \n", "                  </tbody>\n", "                </table>\n", "            </details>\n", "        </div>\n", "    </div></div></div><div class=\"sk-item\"><div class=\"sk-estimator fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-5\" type=\"checkbox\" ><label for=\"sk-estimator-id-5\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow\"><div><div>StandardScaler</div></div><div><a class=\"sk-estimator-doc-link fitted\" rel=\"noreferrer\" target=\"_blank\" href=\"https://scikit-learn.org/1.7/modules/generated/sklearn.preprocessing.StandardScaler.html\">?<span>Documentation for StandardScaler</span></a></div></label><div class=\"sk-toggleable__content fitted\" data-param-prefix=\"preprocessor__num__scaler__\">\n", "        <div class=\"estimator-table\">\n", "            <details>\n", "                <summary>Parameters</summary>\n", "                <table class=\"parameters-table\">\n", "                  <tbody>\n", "                    \n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('copy',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">copy&nbsp;</td>\n", "            <td class=\"value\">True</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('with_mean',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">with_mean&nbsp;</td>\n", "            <td class=\"value\">True</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('with_std',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">with_std&nbsp;</td>\n", "            <td class=\"value\">True</td>\n", "        </tr>\n", "    \n", "                  </tbody>\n", "                </table>\n", "            </details>\n", "        </div>\n", "    </div></div></div></div></div></div></div></div><div class=\"sk-parallel-item\"><div class=\"sk-item\"><div class=\"sk-label-container\"><div class=\"sk-label fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-6\" type=\"checkbox\" ><label for=\"sk-estimator-id-6\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow\"><div><div>cat</div></div></label><div class=\"sk-toggleable__content fitted\" data-param-prefix=\"preprocessor__cat__\"><pre>[&#x27;admission_datetime&#x27;, &#x27;discharge_datetime&#x27;, &#x27;edad_band&#x27;, &#x27;episode_id&#x27;, &#x27;patient_id&#x27;, &#x27;sex&#x27;]</pre></div></div></div><div class=\"sk-serial\"><div class=\"sk-item\"><div class=\"sk-serial\"><div class=\"sk-item\"><div class=\"sk-estimator fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-7\" type=\"checkbox\" ><label for=\"sk-estimator-id-7\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow\"><div><div>SimpleImputer</div></div><div><a class=\"sk-estimator-doc-link fitted\" rel=\"noreferrer\" target=\"_blank\" href=\"https://scikit-learn.org/1.7/modules/generated/sklearn.impute.SimpleImputer.html\">?<span>Documentation for SimpleImputer</span></a></div></label><div class=\"sk-toggleable__content fitted\" data-param-prefix=\"preprocessor__cat__imputer__\">\n", "        <div class=\"estimator-table\">\n", "            <details>\n", "                <summary>Parameters</summary>\n", "                <table class=\"parameters-table\">\n", "                  <tbody>\n", "                    \n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('missing_values',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">missing_values&nbsp;</td>\n", "            <td class=\"value\">nan</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"user-set\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('strategy',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">strategy&nbsp;</td>\n", "            <td class=\"value\">&#x27;most_frequent&#x27;</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('fill_value',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">fill_value&nbsp;</td>\n", "            <td class=\"value\">None</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('copy',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">copy&nbsp;</td>\n", "            <td class=\"value\">True</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('add_indicator',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">add_indicator&nbsp;</td>\n", "            <td class=\"value\">False</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('keep_empty_features',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">keep_empty_features&nbsp;</td>\n", "            <td class=\"value\">False</td>\n", "        </tr>\n", "    \n", "                  </tbody>\n", "                </table>\n", "            </details>\n", "        </div>\n", "    </div></div></div><div class=\"sk-item\"><div class=\"sk-estimator fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-8\" type=\"checkbox\" ><label for=\"sk-estimator-id-8\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow\"><div><div>OneHotEncoder</div></div><div><a class=\"sk-estimator-doc-link fitted\" rel=\"noreferrer\" target=\"_blank\" href=\"https://scikit-learn.org/1.7/modules/generated/sklearn.preprocessing.OneHotEncoder.html\">?<span>Documentation for OneHotEncoder</span></a></div></label><div class=\"sk-toggleable__content fitted\" data-param-prefix=\"preprocessor__cat__encoder__\">\n", "        <div class=\"estimator-table\">\n", "            <details>\n", "                <summary>Parameters</summary>\n", "                <table class=\"parameters-table\">\n", "                  <tbody>\n", "                    \n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('categories',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">categories&nbsp;</td>\n", "            <td class=\"value\">&#x27;auto&#x27;</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('drop',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">drop&nbsp;</td>\n", "            <td class=\"value\">None</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"user-set\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('sparse_output',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">sparse_output&nbsp;</td>\n", "            <td class=\"value\">False</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('dtype',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">dtype&nbsp;</td>\n", "            <td class=\"value\">&lt;class &#x27;numpy.float64&#x27;&gt;</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"user-set\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('handle_unknown',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">handle_unknown&nbsp;</td>\n", "            <td class=\"value\">&#x27;ignore&#x27;</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('min_frequency',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">min_frequency&nbsp;</td>\n", "            <td class=\"value\">None</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('max_categories',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">max_categories&nbsp;</td>\n", "            <td class=\"value\">None</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('feature_name_combiner',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">feature_name_combiner&nbsp;</td>\n", "            <td class=\"value\">&#x27;concat&#x27;</td>\n", "        </tr>\n", "    \n", "                  </tbody>\n", "                </table>\n", "            </details>\n", "        </div>\n", "    </div></div></div></div></div></div></div></div></div></div><div class=\"sk-item\"><div class=\"sk-estimator fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-9\" type=\"checkbox\" ><label for=\"sk-estimator-id-9\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow\"><div><div>LogisticRegression</div></div><div><a class=\"sk-estimator-doc-link fitted\" rel=\"noreferrer\" target=\"_blank\" href=\"https://scikit-learn.org/1.7/modules/generated/sklearn.linear_model.LogisticRegression.html\">?<span>Documentation for LogisticRegression</span></a></div></label><div class=\"sk-toggleable__content fitted\" data-param-prefix=\"model__\">\n", "        <div class=\"estimator-table\">\n", "            <details>\n", "                <summary>Parameters</summary>\n", "                <table class=\"parameters-table\">\n", "                  <tbody>\n", "                    \n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('penalty',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">penalty&nbsp;</td>\n", "            <td class=\"value\">&#x27;l2&#x27;</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('dual',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">dual&nbsp;</td>\n", "            <td class=\"value\">False</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('tol',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">tol&nbsp;</td>\n", "            <td class=\"value\">0.0001</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('C',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">C&nbsp;</td>\n", "            <td class=\"value\">1.0</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('fit_intercept',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">fit_intercept&nbsp;</td>\n", "            <td class=\"value\">True</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('intercept_scaling',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">intercept_scaling&nbsp;</td>\n", "            <td class=\"value\">1</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('class_weight',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">class_weight&nbsp;</td>\n", "            <td class=\"value\">None</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('random_state',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">random_state&nbsp;</td>\n", "            <td class=\"value\">None</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('solver',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">solver&nbsp;</td>\n", "            <td class=\"value\">&#x27;lbfgs&#x27;</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"user-set\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('max_iter',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">max_iter&nbsp;</td>\n", "            <td class=\"value\">1000</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('multi_class',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">multi_class&nbsp;</td>\n", "            <td class=\"value\">&#x27;deprecated&#x27;</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('verbose',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">verbose&nbsp;</td>\n", "            <td class=\"value\">0</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('warm_start',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">warm_start&nbsp;</td>\n", "            <td class=\"value\">False</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('n_jobs',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">n_jobs&nbsp;</td>\n", "            <td class=\"value\">None</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('l1_ratio',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">l1_ratio&nbsp;</td>\n", "            <td class=\"value\">None</td>\n", "        </tr>\n", "    \n", "                  </tbody>\n", "                </table>\n", "            </details>\n", "        </div>\n", "    </div></div></div></div></div></div></div><script>function copyToClipboard(text, element) {\n", "    // Get the parameter prefix from the closest toggleable content\n", "    const toggleableContent = element.closest('.sk-toggleable__content');\n", "    const paramPrefix = toggleableContent ? toggleableContent.dataset.paramPrefix : '';\n", "    const fullParamName = paramPrefix ? `${paramPrefix}${text}` : text;\n", "\n", "    const originalStyle = element.style;\n", "    const computedStyle = window.getComputedStyle(element);\n", "    const originalWidth = computedStyle.width;\n", "    const originalHTML = element.innerHTML.replace('Copied!', '');\n", "\n", "    navigator.clipboard.writeText(fullParamName)\n", "        .then(() => {\n", "            element.style.width = originalWidth;\n", "            element.style.color = 'green';\n", "            element.innerHTML = \"Copied!\";\n", "\n", "            setTimeout(() => {\n", "                element.innerHTML = originalHTML;\n", "                element.style = originalStyle;\n", "            }, 2000);\n", "        })\n", "        .catch(err => {\n", "            console.error('Failed to copy:', err);\n", "            element.style.color = 'red';\n", "            element.innerHTML = \"Failed!\";\n", "            setTimeout(() => {\n", "                element.innerHTML = originalHTML;\n", "                element.style = originalStyle;\n", "            }, 2000);\n", "        });\n", "    return false;\n", "}\n", "\n", "document.querySelectorAll('.fa-regular.fa-copy').forEach(function(element) {\n", "    const toggleableContent = element.closest('.sk-toggleable__content');\n", "    const paramPrefix = toggleableContent ? toggleableContent.dataset.paramPrefix : '';\n", "    const paramName = element.parentElement.nextElementSibling.textContent.trim();\n", "    const fullParamName = paramPrefix ? `${paramPrefix}${paramName}` : paramName;\n", "\n", "    element.setAttribute('title', fullParamName);\n", "});\n", "</script></body>"], "text/plain": ["Pipeline(steps=[('preprocessor',\n", "                 ColumnTransformer(transformers=[('num',\n", "                                                  Pipeline(steps=[('imputer',\n", "                                                                   SimpleImputer(strategy='median')),\n", "                                                                  ('scaler',\n", "                                                                   StandardScaler())]),\n", "                                                  ['length_of_stay_days',\n", "                                                   'age_years', 'bmi', 'hr_bpm',\n", "                                                   'sbp_mmhg', 'spo2_pct',\n", "                                                   'temp_c', 'hb_g_dl',\n", "                                                   'creatinine_mg_dl',\n", "                                                   'glucose_mg_dl',\n", "                                                   'wbc_10e9_per_l',\n", "                                                   'egfr_ml_min_1_73m2']),\n", "                                                 ('cat',\n", "                                                  Pipeline(steps=[('imputer',\n", "                                                                   SimpleImputer(strategy='most_frequent')),\n", "                                                                  ('encoder',\n", "                                                                   OneHotEncoder(handle_unknown='ignore',\n", "                                                                                 sparse_output=False))]),\n", "                                                  ['admission_datetime',\n", "                                                   'discharge_datetime',\n", "                                                   'edad_band', 'episode_id',\n", "                                                   'patient_id', 'sex'])])),\n", "                ('model', LogisticRegression(max_iter=1000))])"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["\n", "# --- Model base\n", "clf = Pipeline([\n", "    (\"preprocessor\", preprocessor),\n", "    (\"model\", LogisticRegression(max_iter=1000, solver=\"lbfgs\"))\n", "])\n", "\n", "# --- Split train/test\n", "X_train, X_test, y_train, y_test = train_test_split(\n", "    X, y, test_size=0.2, stratify=y, random_state=42\n", ")\n", "\n", "# --- Entrenament del model\n", "clf.fit(X_train, y_train)\n"]}, {"cell_type": "code", "execution_count": 28, "id": "a3f68846", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Top-10 Importancia por permutación: ['num__creatinine_mg_dl', 'num__bmi', 'num__spo2_pct', 'cat__admission_datetime_2022-01-27', 'cat__admission_datetime_2022-01-30', 'num__egfr_ml_min_1_73m2', 'num__age_years', 'num__hr_bpm', 'num__length_of_stay_days', 'num__sbp_mmhg']\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["\n", "# ======================================================\n", "# --- Importancia por permutación\n", "# ======================================================\n", "perm = permutation_importance(\n", "    clf, X_test, y_test, n_repeats=20,\n", "    scoring=\"average_precision\", random_state=42, n_jobs=-1\n", ")\n", "\n", "feat_names = clf.named_steps[\"preprocessor\"].get_feature_names_out()\n", "imp_mean = perm.importances_mean\n", "top_idx = np.argsort(imp_mean)[::-1][:10]  # Top-10\n", "top_feats = [feat_names[i] for i in top_idx]\n", "print(\"Top-10 Importancia por permutación:\", top_feats)\n", "\n", "# Gràfic Top-10\n", "plt.figure()\n", "plt.bar(range(len(top_idx)), imp_mean[top_idx])\n", "plt.xticks(range(len(top_idx)), top_feats, rotation=45, ha=\"right\")\n", "plt.title(\"Importancia por permutación (Top-10)\")\n", "plt.tight_layout()\n", "plt.savefig(\"figures/perm_importances_top10.png\", dpi=120)\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 29, "id": "09c444cd", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["\n", "# ======================================================\n", "# --- Partial Dependence Plot (PDP)\n", "# ======================================================\n", "feat_pdp = \"age_years\" if \"age_years\" in X.columns else num_cols[0]\n", "PartialDependenceDisplay.from_estimator(clf, X_test, [feat_pdp])\n", "plt.tight_layout()\n", "plt.savefig(f\"figures/pdp_{feat_pdp}.png\", dpi=120)\n", "plt.show()\n"]}], "metadata": {"kernelspec": {"display_name": "ia-bio", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}