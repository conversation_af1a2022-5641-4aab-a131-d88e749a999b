{"cells": [{"cell_type": "markdown", "id": "3700b479-60b6-4314-84a2-42036158952b", "metadata": {}, "source": ["Ejercicio 3: Calibración isotónica + Brier + ECE\n", "Ajusta un CalibratedClassifierCV (isotonic) sobre el modelo. Compara Brier y ECE (10 bins por\n", "cuantiles) antes y después. Y dibuja la curva de calibración (observado vs predicho) y guarda el\n", "PNG."]}, {"cell_type": "code", "execution_count": 2, "id": "bcf86b90-4564-41de-99e5-d3d2ac973087", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["columnas y filas cargadas:  (200, 12)\n", "Numéricas:  8 | Categóricas:  3\n"]}, {"data": {"text/html": ["<style>#sk-container-id-1 {color: black;}#sk-container-id-1 pre{padding: 0;}#sk-container-id-1 div.sk-toggleable {background-color: white;}#sk-container-id-1 label.sk-toggleable__label {cursor: pointer;display: block;width: 100%;margin-bottom: 0;padding: 0.3em;box-sizing: border-box;text-align: center;}#sk-container-id-1 label.sk-toggleable__label-arrow:before {content: \"▸\";float: left;margin-right: 0.25em;color: #696969;}#sk-container-id-1 label.sk-toggleable__label-arrow:hover:before {color: black;}#sk-container-id-1 div.sk-estimator:hover label.sk-toggleable__label-arrow:before {color: black;}#sk-container-id-1 div.sk-toggleable__content {max-height: 0;max-width: 0;overflow: hidden;text-align: left;background-color: #f0f8ff;}#sk-container-id-1 div.sk-toggleable__content pre {margin: 0.2em;color: black;border-radius: 0.25em;background-color: #f0f8ff;}#sk-container-id-1 input.sk-toggleable__control:checked~div.sk-toggleable__content {max-height: 200px;max-width: 100%;overflow: auto;}#sk-container-id-1 input.sk-toggleable__control:checked~label.sk-toggleable__label-arrow:before {content: \"▾\";}#sk-container-id-1 div.sk-estimator input.sk-toggleable__control:checked~label.sk-toggleable__label {background-color: #d4ebff;}#sk-container-id-1 div.sk-label input.sk-toggleable__control:checked~label.sk-toggleable__label {background-color: #d4ebff;}#sk-container-id-1 input.sk-hidden--visually {border: 0;clip: rect(1px 1px 1px 1px);clip: rect(1px, 1px, 1px, 1px);height: 1px;margin: -1px;overflow: hidden;padding: 0;position: absolute;width: 1px;}#sk-container-id-1 div.sk-estimator {font-family: monospace;background-color: #f0f8ff;border: 1px dotted black;border-radius: 0.25em;box-sizing: border-box;margin-bottom: 0.5em;}#sk-container-id-1 div.sk-estimator:hover {background-color: #d4ebff;}#sk-container-id-1 div.sk-parallel-item::after {content: \"\";width: 100%;border-bottom: 1px solid gray;flex-grow: 1;}#sk-container-id-1 div.sk-label:hover label.sk-toggleable__label {background-color: #d4ebff;}#sk-container-id-1 div.sk-serial::before {content: \"\";position: absolute;border-left: 1px solid gray;box-sizing: border-box;top: 0;bottom: 0;left: 50%;z-index: 0;}#sk-container-id-1 div.sk-serial {display: flex;flex-direction: column;align-items: center;background-color: white;padding-right: 0.2em;padding-left: 0.2em;position: relative;}#sk-container-id-1 div.sk-item {position: relative;z-index: 1;}#sk-container-id-1 div.sk-parallel {display: flex;align-items: stretch;justify-content: center;background-color: white;position: relative;}#sk-container-id-1 div.sk-item::before, #sk-container-id-1 div.sk-parallel-item::before {content: \"\";position: absolute;border-left: 1px solid gray;box-sizing: border-box;top: 0;bottom: 0;left: 50%;z-index: -1;}#sk-container-id-1 div.sk-parallel-item {display: flex;flex-direction: column;z-index: 1;position: relative;background-color: white;}#sk-container-id-1 div.sk-parallel-item:first-child::after {align-self: flex-end;width: 50%;}#sk-container-id-1 div.sk-parallel-item:last-child::after {align-self: flex-start;width: 50%;}#sk-container-id-1 div.sk-parallel-item:only-child::after {width: 0;}#sk-container-id-1 div.sk-dashed-wrapped {border: 1px dashed gray;margin: 0 0.4em 0.5em 0.4em;box-sizing: border-box;padding-bottom: 0.4em;background-color: white;}#sk-container-id-1 div.sk-label label {font-family: monospace;font-weight: bold;display: inline-block;line-height: 1.2em;}#sk-container-id-1 div.sk-label-container {text-align: center;}#sk-container-id-1 div.sk-container {/* jupyter's `normalize.less` sets `[hidden] { display: none; }` but bootstrap.min.css set `[hidden] { display: none !important; }` so we also need the `!important` here to be able to override the default hidden behavior on the sphinx rendered scikit-learn.org. See: https://github.com/scikit-learn/scikit-learn/issues/21755 */display: inline-block !important;position: relative;}#sk-container-id-1 div.sk-text-repr-fallback {display: none;}</style><div id=\"sk-container-id-1\" class=\"sk-top-container\"><div class=\"sk-text-repr-fallback\"><pre>Pipeline(steps=[(&#x27;prep&#x27;,\n", "                 ColumnTransformer(transformers=[(&#x27;num&#x27;,\n", "                                                  Pipeline(steps=[(&#x27;imp&#x27;,\n", "                                                                   SimpleImputer(strategy=&#x27;median&#x27;)),\n", "                                                                  (&#x27;sc&#x27;,\n", "                                                                   StandardScaler())]),\n", "                                                  [&#x27;age_years&#x27;, &#x27;bmi&#x27;, &#x27;hr_bpm&#x27;,\n", "                                                   &#x27;sbp_mmhg&#x27;, &#x27;spo2_pct&#x27;,\n", "                                                   &#x27;hb_g_dl&#x27;,\n", "                                                   &#x27;creatinine_mg_dl&#x27;,\n", "                                                   &#x27;glucose_mg_dl&#x27;]),\n", "                                                 (&#x27;cat&#x27;,\n", "                                                  Pipeline(steps=[(&#x27;imp&#x27;,\n", "                                                                   SimpleImputer(strategy=&#x27;most_frequent&#x27;)),\n", "                                                                  (&#x27;oh&#x27;,\n", "                                                                   OneHotEncoder(handle_unknown=&#x27;ignore&#x27;,\n", "                                                                                 sparse_output=False))]),\n", "                                                  [&#x27;edad_band&#x27;, &#x27;sex&#x27;,\n", "                                                   &#x27;taquicardia&#x27;])])),\n", "                (&#x27;model&#x27;, LogisticRegression(max_iter=1000))])</pre><b>In a Jupyter environment, please rerun this cell to show the HTML representation or trust the notebook. <br />On GitHub, the HTML representation is unable to render, please try loading this page with nbviewer.org.</b></div><div class=\"sk-container\" hidden><div class=\"sk-item sk-dashed-wrapped\"><div class=\"sk-label-container\"><div class=\"sk-label sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-1\" type=\"checkbox\" ><label for=\"sk-estimator-id-1\" class=\"sk-toggleable__label sk-toggleable__label-arrow\">Pipeline</label><div class=\"sk-toggleable__content\"><pre>Pipeline(steps=[(&#x27;prep&#x27;,\n", "                 ColumnTransformer(transformers=[(&#x27;num&#x27;,\n", "                                                  Pipeline(steps=[(&#x27;imp&#x27;,\n", "                                                                   SimpleImputer(strategy=&#x27;median&#x27;)),\n", "                                                                  (&#x27;sc&#x27;,\n", "                                                                   StandardScaler())]),\n", "                                                  [&#x27;age_years&#x27;, &#x27;bmi&#x27;, &#x27;hr_bpm&#x27;,\n", "                                                   &#x27;sbp_mmhg&#x27;, &#x27;spo2_pct&#x27;,\n", "                                                   &#x27;hb_g_dl&#x27;,\n", "                                                   &#x27;creatinine_mg_dl&#x27;,\n", "                                                   &#x27;glucose_mg_dl&#x27;]),\n", "                                                 (&#x27;cat&#x27;,\n", "                                                  Pipeline(steps=[(&#x27;imp&#x27;,\n", "                                                                   SimpleImputer(strategy=&#x27;most_frequent&#x27;)),\n", "                                                                  (&#x27;oh&#x27;,\n", "                                                                   OneHotEncoder(handle_unknown=&#x27;ignore&#x27;,\n", "                                                                                 sparse_output=False))]),\n", "                                                  [&#x27;edad_band&#x27;, &#x27;sex&#x27;,\n", "                                                   &#x27;taquicardia&#x27;])])),\n", "                (&#x27;model&#x27;, LogisticRegression(max_iter=1000))])</pre></div></div></div><div class=\"sk-serial\"><div class=\"sk-item sk-dashed-wrapped\"><div class=\"sk-label-container\"><div class=\"sk-label sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-2\" type=\"checkbox\" ><label for=\"sk-estimator-id-2\" class=\"sk-toggleable__label sk-toggleable__label-arrow\">prep: ColumnTransformer</label><div class=\"sk-toggleable__content\"><pre>ColumnTransformer(transformers=[(&#x27;num&#x27;,\n", "                                 Pipeline(steps=[(&#x27;imp&#x27;,\n", "                                                  SimpleImputer(strategy=&#x27;median&#x27;)),\n", "                                                 (&#x27;sc&#x27;, StandardScaler())]),\n", "                                 [&#x27;age_years&#x27;, &#x27;bmi&#x27;, &#x27;hr_bpm&#x27;, &#x27;sbp_mmhg&#x27;,\n", "                                  &#x27;spo2_pct&#x27;, &#x27;hb_g_dl&#x27;, &#x27;creatinine_mg_dl&#x27;,\n", "                                  &#x27;glucose_mg_dl&#x27;]),\n", "                                (&#x27;cat&#x27;,\n", "                                 Pipeline(steps=[(&#x27;imp&#x27;,\n", "                                                  SimpleImputer(strategy=&#x27;most_frequent&#x27;)),\n", "                                                 (&#x27;oh&#x27;,\n", "                                                  OneHotEncoder(handle_unknown=&#x27;ignore&#x27;,\n", "                                                                sparse_output=False))]),\n", "                                 [&#x27;edad_band&#x27;, &#x27;sex&#x27;, &#x27;taquicardia&#x27;])])</pre></div></div></div><div class=\"sk-parallel\"><div class=\"sk-parallel-item\"><div class=\"sk-item\"><div class=\"sk-label-container\"><div class=\"sk-label sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-3\" type=\"checkbox\" ><label for=\"sk-estimator-id-3\" class=\"sk-toggleable__label sk-toggleable__label-arrow\">num</label><div class=\"sk-toggleable__content\"><pre>[&#x27;age_years&#x27;, &#x27;bmi&#x27;, &#x27;hr_bpm&#x27;, &#x27;sbp_mmhg&#x27;, &#x27;spo2_pct&#x27;, &#x27;hb_g_dl&#x27;, &#x27;creatinine_mg_dl&#x27;, &#x27;glucose_mg_dl&#x27;]</pre></div></div></div><div class=\"sk-serial\"><div class=\"sk-item\"><div class=\"sk-serial\"><div class=\"sk-item\"><div class=\"sk-estimator sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-4\" type=\"checkbox\" ><label for=\"sk-estimator-id-4\" class=\"sk-toggleable__label sk-toggleable__label-arrow\">SimpleImputer</label><div class=\"sk-toggleable__content\"><pre>SimpleImputer(strategy=&#x27;median&#x27;)</pre></div></div></div><div class=\"sk-item\"><div class=\"sk-estimator sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-5\" type=\"checkbox\" ><label for=\"sk-estimator-id-5\" class=\"sk-toggleable__label sk-toggleable__label-arrow\">StandardScaler</label><div class=\"sk-toggleable__content\"><pre>StandardScaler()</pre></div></div></div></div></div></div></div></div><div class=\"sk-parallel-item\"><div class=\"sk-item\"><div class=\"sk-label-container\"><div class=\"sk-label sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-6\" type=\"checkbox\" ><label for=\"sk-estimator-id-6\" class=\"sk-toggleable__label sk-toggleable__label-arrow\">cat</label><div class=\"sk-toggleable__content\"><pre>[&#x27;edad_band&#x27;, &#x27;sex&#x27;, &#x27;taquicardia&#x27;]</pre></div></div></div><div class=\"sk-serial\"><div class=\"sk-item\"><div class=\"sk-serial\"><div class=\"sk-item\"><div class=\"sk-estimator sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-7\" type=\"checkbox\" ><label for=\"sk-estimator-id-7\" class=\"sk-toggleable__label sk-toggleable__label-arrow\">SimpleImputer</label><div class=\"sk-toggleable__content\"><pre>SimpleImputer(strategy=&#x27;most_frequent&#x27;)</pre></div></div></div><div class=\"sk-item\"><div class=\"sk-estimator sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-8\" type=\"checkbox\" ><label for=\"sk-estimator-id-8\" class=\"sk-toggleable__label sk-toggleable__label-arrow\">OneHotEncoder</label><div class=\"sk-toggleable__content\"><pre>OneHotEncoder(handle_unknown=&#x27;ignore&#x27;, sparse_output=False)</pre></div></div></div></div></div></div></div></div></div></div><div class=\"sk-item\"><div class=\"sk-estimator sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-9\" type=\"checkbox\" ><label for=\"sk-estimator-id-9\" class=\"sk-toggleable__label sk-toggleable__label-arrow\">LogisticRegression</label><div class=\"sk-toggleable__content\"><pre>LogisticRegression(max_iter=1000)</pre></div></div></div></div></div></div></div>"], "text/plain": ["Pipeline(steps=[('prep',\n", "                 ColumnTransformer(transformers=[('num',\n", "                                                  Pipeline(steps=[('imp',\n", "                                                                   SimpleImputer(strategy='median')),\n", "                                                                  ('sc',\n", "                                                                   StandardScaler())]),\n", "                                                  ['age_years', 'bmi', 'hr_bpm',\n", "                                                   'sbp_mmhg', 'spo2_pct',\n", "                                                   'hb_g_dl',\n", "                                                   'creatinine_mg_dl',\n", "                                                   'glucose_mg_dl']),\n", "                                                 ('cat',\n", "                                                  Pipeline(steps=[('imp',\n", "                                                                   SimpleImputer(strategy='most_frequent')),\n", "                                                                  ('oh',\n", "                                                                   OneHotEncoder(handle_unknown='ignore',\n", "                                                                                 sparse_output=False))]),\n", "                                                  ['edad_band', 'sex',\n", "                                                   'taquicardia'])])),\n", "                ('model', LogisticRegression(max_iter=1000))])"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["import numpy as np, pandas as pd, matplotlib.pyplot as plt, os\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.compose import ColumnTransformer\n", "from sklearn.pipeline import Pipeline\n", "from sklearn.impute import SimpleImputer\n", "from sklearn.preprocessing import OneHotEncoder, StandardScaler\n", "from sklearn.linear_model import LogisticRegression\n", "from sklearn.metrics import brier_score_loss\n", "from sklearn.calibration import CalibratedClassifierCV, calibration_curve\n", "\n", "pd.set_option(\"display.max_columns\", 120)\n", "pd.set_option(\"display.width\", 160)\n", "#preguntar si existe la carpeta figueres\n", "os.makedirs(\"figures\", exist_ok=True)\n", "\n", "df=pd.read_csv(\"baseS03.csv\")\n", "y= df['readmit_30d'].astype(int)\n", "X = df.drop(columns=['readmit_30d'])\n", "num_cols = X.select_dtypes(include='number').columns.tolist()\n", "cat_cols = X.columns.difference(num_cols).tolist()\n", "print(\"columnas y filas cargadas: \",df.shape)\n", "print(\"Numéricas: \", len(num_cols), \"| Categóricas: \", len(cat_cols))\n", "\n", "# preprocesado de los datos para numericas y para categóricas\n", "\n", "prep = ColumnTransformer([\n", "    (\"num\", Pipeline([(\"imp\", SimpleImputer(strategy=\"median\")), \n", "                       (\"sc\", StandardScaler())]), num_cols),\n", "    (\"cat\", Pipeline([(\"imp\", SimpleImputer(strategy=\"most_frequent\")),\n", "                       (\"oh\", OneHotEncoder(handle_unknown=\"ignore\", sparse_output=False))]), cat_cols)])\n", "\n", "\n", "pipe = Pipeline([(\"prep\", prep), (\"model\", LogisticRegression(max_iter=1000, solver=\"lbfgs\"))])\n", "#Separamos los datos de entrenamiento (80/20) y estratificando por y, \n", "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.20, stratify=y, random_state=42)\n", "\n", "pipe.fit(X_train, y_train)"]}, {"cell_type": "code", "execution_count": 3, "id": "84f37cf9-e2e9-4247-ae8c-7da83dba3a3a", "metadata": {}, "outputs": [], "source": ["# calcular las probabilidades sin calibrar\n", "p_raw = pipe.predict_proba(X_test)[:,1]"]}, {"cell_type": "code", "execution_count": 4, "id": "8f96a45d-64c0-4996-a59a-6257a2f79a42", "metadata": {}, "outputs": [], "source": ["# crear un calibrador \n", "cal = CalibratedClassifierCV(pipe, method=\"isotonic\",cv=3)"]}, {"cell_type": "code", "execution_count": 5, "id": "9b033ae7-c1fc-4d23-bbe0-f71d22508ef2", "metadata": {}, "outputs": [{"data": {"text/html": ["<style>#sk-container-id-2 {color: black;}#sk-container-id-2 pre{padding: 0;}#sk-container-id-2 div.sk-toggleable {background-color: white;}#sk-container-id-2 label.sk-toggleable__label {cursor: pointer;display: block;width: 100%;margin-bottom: 0;padding: 0.3em;box-sizing: border-box;text-align: center;}#sk-container-id-2 label.sk-toggleable__label-arrow:before {content: \"▸\";float: left;margin-right: 0.25em;color: #696969;}#sk-container-id-2 label.sk-toggleable__label-arrow:hover:before {color: black;}#sk-container-id-2 div.sk-estimator:hover label.sk-toggleable__label-arrow:before {color: black;}#sk-container-id-2 div.sk-toggleable__content {max-height: 0;max-width: 0;overflow: hidden;text-align: left;background-color: #f0f8ff;}#sk-container-id-2 div.sk-toggleable__content pre {margin: 0.2em;color: black;border-radius: 0.25em;background-color: #f0f8ff;}#sk-container-id-2 input.sk-toggleable__control:checked~div.sk-toggleable__content {max-height: 200px;max-width: 100%;overflow: auto;}#sk-container-id-2 input.sk-toggleable__control:checked~label.sk-toggleable__label-arrow:before {content: \"▾\";}#sk-container-id-2 div.sk-estimator input.sk-toggleable__control:checked~label.sk-toggleable__label {background-color: #d4ebff;}#sk-container-id-2 div.sk-label input.sk-toggleable__control:checked~label.sk-toggleable__label {background-color: #d4ebff;}#sk-container-id-2 input.sk-hidden--visually {border: 0;clip: rect(1px 1px 1px 1px);clip: rect(1px, 1px, 1px, 1px);height: 1px;margin: -1px;overflow: hidden;padding: 0;position: absolute;width: 1px;}#sk-container-id-2 div.sk-estimator {font-family: monospace;background-color: #f0f8ff;border: 1px dotted black;border-radius: 0.25em;box-sizing: border-box;margin-bottom: 0.5em;}#sk-container-id-2 div.sk-estimator:hover {background-color: #d4ebff;}#sk-container-id-2 div.sk-parallel-item::after {content: \"\";width: 100%;border-bottom: 1px solid gray;flex-grow: 1;}#sk-container-id-2 div.sk-label:hover label.sk-toggleable__label {background-color: #d4ebff;}#sk-container-id-2 div.sk-serial::before {content: \"\";position: absolute;border-left: 1px solid gray;box-sizing: border-box;top: 0;bottom: 0;left: 50%;z-index: 0;}#sk-container-id-2 div.sk-serial {display: flex;flex-direction: column;align-items: center;background-color: white;padding-right: 0.2em;padding-left: 0.2em;position: relative;}#sk-container-id-2 div.sk-item {position: relative;z-index: 1;}#sk-container-id-2 div.sk-parallel {display: flex;align-items: stretch;justify-content: center;background-color: white;position: relative;}#sk-container-id-2 div.sk-item::before, #sk-container-id-2 div.sk-parallel-item::before {content: \"\";position: absolute;border-left: 1px solid gray;box-sizing: border-box;top: 0;bottom: 0;left: 50%;z-index: -1;}#sk-container-id-2 div.sk-parallel-item {display: flex;flex-direction: column;z-index: 1;position: relative;background-color: white;}#sk-container-id-2 div.sk-parallel-item:first-child::after {align-self: flex-end;width: 50%;}#sk-container-id-2 div.sk-parallel-item:last-child::after {align-self: flex-start;width: 50%;}#sk-container-id-2 div.sk-parallel-item:only-child::after {width: 0;}#sk-container-id-2 div.sk-dashed-wrapped {border: 1px dashed gray;margin: 0 0.4em 0.5em 0.4em;box-sizing: border-box;padding-bottom: 0.4em;background-color: white;}#sk-container-id-2 div.sk-label label {font-family: monospace;font-weight: bold;display: inline-block;line-height: 1.2em;}#sk-container-id-2 div.sk-label-container {text-align: center;}#sk-container-id-2 div.sk-container {/* jupyter's `normalize.less` sets `[hidden] { display: none; }` but bootstrap.min.css set `[hidden] { display: none !important; }` so we also need the `!important` here to be able to override the default hidden behavior on the sphinx rendered scikit-learn.org. See: https://github.com/scikit-learn/scikit-learn/issues/21755 */display: inline-block !important;position: relative;}#sk-container-id-2 div.sk-text-repr-fallback {display: none;}</style><div id=\"sk-container-id-2\" class=\"sk-top-container\"><div class=\"sk-text-repr-fallback\"><pre>CalibratedClassifierCV(cv=3,\n", "                       estimator=Pipeline(steps=[(&#x27;prep&#x27;,\n", "                                                  ColumnTransformer(transformers=[(&#x27;num&#x27;,\n", "                                                                                   Pipeline(steps=[(&#x27;imp&#x27;,\n", "                                                                                                    SimpleImputer(strategy=&#x27;median&#x27;)),\n", "                                                                                                   (&#x27;sc&#x27;,\n", "                                                                                                    StandardScaler())]),\n", "                                                                                   [&#x27;age_years&#x27;,\n", "                                                                                    &#x27;bmi&#x27;,\n", "                                                                                    &#x27;hr_bpm&#x27;,\n", "                                                                                    &#x27;sbp_mmhg&#x27;,\n", "                                                                                    &#x27;spo2_pct&#x27;,\n", "                                                                                    &#x27;hb_g_dl&#x27;,\n", "                                                                                    &#x27;creatinine_mg_dl&#x27;,\n", "                                                                                    &#x27;glucose_mg_dl&#x27;]),\n", "                                                                                  (&#x27;cat&#x27;,\n", "                                                                                   Pipeline(steps=[(&#x27;imp&#x27;,\n", "                                                                                                    SimpleImputer(strategy=&#x27;most_frequent&#x27;)),\n", "                                                                                                   (&#x27;oh&#x27;,\n", "                                                                                                    OneHotEncoder(handle_unknown=&#x27;ignore&#x27;,\n", "                                                                                                                  sparse_output=False))]),\n", "                                                                                   [&#x27;edad_band&#x27;,\n", "                                                                                    &#x27;sex&#x27;,\n", "                                                                                    &#x27;taquicardia&#x27;])])),\n", "                                                 (&#x27;model&#x27;,\n", "                                                  LogisticRegression(max_iter=1000))]),\n", "                       method=&#x27;isotonic&#x27;)</pre><b>In a Jupyter environment, please rerun this cell to show the HTML representation or trust the notebook. <br />On GitHub, the HTML representation is unable to render, please try loading this page with nbviewer.org.</b></div><div class=\"sk-container\" hidden><div class=\"sk-item sk-dashed-wrapped\"><div class=\"sk-label-container\"><div class=\"sk-label sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-10\" type=\"checkbox\" ><label for=\"sk-estimator-id-10\" class=\"sk-toggleable__label sk-toggleable__label-arrow\">CalibratedClassifierCV</label><div class=\"sk-toggleable__content\"><pre>CalibratedClassifierCV(cv=3,\n", "                       estimator=Pipeline(steps=[(&#x27;prep&#x27;,\n", "                                                  ColumnTransformer(transformers=[(&#x27;num&#x27;,\n", "                                                                                   Pipeline(steps=[(&#x27;imp&#x27;,\n", "                                                                                                    SimpleImputer(strategy=&#x27;median&#x27;)),\n", "                                                                                                   (&#x27;sc&#x27;,\n", "                                                                                                    StandardScaler())]),\n", "                                                                                   [&#x27;age_years&#x27;,\n", "                                                                                    &#x27;bmi&#x27;,\n", "                                                                                    &#x27;hr_bpm&#x27;,\n", "                                                                                    &#x27;sbp_mmhg&#x27;,\n", "                                                                                    &#x27;spo2_pct&#x27;,\n", "                                                                                    &#x27;hb_g_dl&#x27;,\n", "                                                                                    &#x27;creatinine_mg_dl&#x27;,\n", "                                                                                    &#x27;glucose_mg_dl&#x27;]),\n", "                                                                                  (&#x27;cat&#x27;,\n", "                                                                                   Pipeline(steps=[(&#x27;imp&#x27;,\n", "                                                                                                    SimpleImputer(strategy=&#x27;most_frequent&#x27;)),\n", "                                                                                                   (&#x27;oh&#x27;,\n", "                                                                                                    OneHotEncoder(handle_unknown=&#x27;ignore&#x27;,\n", "                                                                                                                  sparse_output=False))]),\n", "                                                                                   [&#x27;edad_band&#x27;,\n", "                                                                                    &#x27;sex&#x27;,\n", "                                                                                    &#x27;taquicardia&#x27;])])),\n", "                                                 (&#x27;model&#x27;,\n", "                                                  LogisticRegression(max_iter=1000))]),\n", "                       method=&#x27;isotonic&#x27;)</pre></div></div></div><div class=\"sk-parallel\"><div class=\"sk-parallel-item\"><div class=\"sk-item\"><div class=\"sk-label-container\"><div class=\"sk-label sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-11\" type=\"checkbox\" ><label for=\"sk-estimator-id-11\" class=\"sk-toggleable__label sk-toggleable__label-arrow\">estimator: Pipeline</label><div class=\"sk-toggleable__content\"><pre>Pipeline(steps=[(&#x27;prep&#x27;,\n", "                 ColumnTransformer(transformers=[(&#x27;num&#x27;,\n", "                                                  Pipeline(steps=[(&#x27;imp&#x27;,\n", "                                                                   SimpleImputer(strategy=&#x27;median&#x27;)),\n", "                                                                  (&#x27;sc&#x27;,\n", "                                                                   StandardScaler())]),\n", "                                                  [&#x27;age_years&#x27;, &#x27;bmi&#x27;, &#x27;hr_bpm&#x27;,\n", "                                                   &#x27;sbp_mmhg&#x27;, &#x27;spo2_pct&#x27;,\n", "                                                   &#x27;hb_g_dl&#x27;,\n", "                                                   &#x27;creatinine_mg_dl&#x27;,\n", "                                                   &#x27;glucose_mg_dl&#x27;]),\n", "                                                 (&#x27;cat&#x27;,\n", "                                                  Pipeline(steps=[(&#x27;imp&#x27;,\n", "                                                                   SimpleImputer(strategy=&#x27;most_frequent&#x27;)),\n", "                                                                  (&#x27;oh&#x27;,\n", "                                                                   OneHotEncoder(handle_unknown=&#x27;ignore&#x27;,\n", "                                                                                 sparse_output=False))]),\n", "                                                  [&#x27;edad_band&#x27;, &#x27;sex&#x27;,\n", "                                                   &#x27;taquicardia&#x27;])])),\n", "                (&#x27;model&#x27;, LogisticRegression(max_iter=1000))])</pre></div></div></div><div class=\"sk-serial\"><div class=\"sk-item\"><div class=\"sk-serial\"><div class=\"sk-item sk-dashed-wrapped\"><div class=\"sk-label-container\"><div class=\"sk-label sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-12\" type=\"checkbox\" ><label for=\"sk-estimator-id-12\" class=\"sk-toggleable__label sk-toggleable__label-arrow\">prep: ColumnTransformer</label><div class=\"sk-toggleable__content\"><pre>ColumnTransformer(transformers=[(&#x27;num&#x27;,\n", "                                 Pipeline(steps=[(&#x27;imp&#x27;,\n", "                                                  SimpleImputer(strategy=&#x27;median&#x27;)),\n", "                                                 (&#x27;sc&#x27;, StandardScaler())]),\n", "                                 [&#x27;age_years&#x27;, &#x27;bmi&#x27;, &#x27;hr_bpm&#x27;, &#x27;sbp_mmhg&#x27;,\n", "                                  &#x27;spo2_pct&#x27;, &#x27;hb_g_dl&#x27;, &#x27;creatinine_mg_dl&#x27;,\n", "                                  &#x27;glucose_mg_dl&#x27;]),\n", "                                (&#x27;cat&#x27;,\n", "                                 Pipeline(steps=[(&#x27;imp&#x27;,\n", "                                                  SimpleImputer(strategy=&#x27;most_frequent&#x27;)),\n", "                                                 (&#x27;oh&#x27;,\n", "                                                  OneHotEncoder(handle_unknown=&#x27;ignore&#x27;,\n", "                                                                sparse_output=False))]),\n", "                                 [&#x27;edad_band&#x27;, &#x27;sex&#x27;, &#x27;taquicardia&#x27;])])</pre></div></div></div><div class=\"sk-parallel\"><div class=\"sk-parallel-item\"><div class=\"sk-item\"><div class=\"sk-label-container\"><div class=\"sk-label sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-13\" type=\"checkbox\" ><label for=\"sk-estimator-id-13\" class=\"sk-toggleable__label sk-toggleable__label-arrow\">num</label><div class=\"sk-toggleable__content\"><pre>[&#x27;age_years&#x27;, &#x27;bmi&#x27;, &#x27;hr_bpm&#x27;, &#x27;sbp_mmhg&#x27;, &#x27;spo2_pct&#x27;, &#x27;hb_g_dl&#x27;, &#x27;creatinine_mg_dl&#x27;, &#x27;glucose_mg_dl&#x27;]</pre></div></div></div><div class=\"sk-serial\"><div class=\"sk-item\"><div class=\"sk-serial\"><div class=\"sk-item\"><div class=\"sk-estimator sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-14\" type=\"checkbox\" ><label for=\"sk-estimator-id-14\" class=\"sk-toggleable__label sk-toggleable__label-arrow\">SimpleImputer</label><div class=\"sk-toggleable__content\"><pre>SimpleImputer(strategy=&#x27;median&#x27;)</pre></div></div></div><div class=\"sk-item\"><div class=\"sk-estimator sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-15\" type=\"checkbox\" ><label for=\"sk-estimator-id-15\" class=\"sk-toggleable__label sk-toggleable__label-arrow\">StandardScaler</label><div class=\"sk-toggleable__content\"><pre>StandardScaler()</pre></div></div></div></div></div></div></div></div><div class=\"sk-parallel-item\"><div class=\"sk-item\"><div class=\"sk-label-container\"><div class=\"sk-label sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-16\" type=\"checkbox\" ><label for=\"sk-estimator-id-16\" class=\"sk-toggleable__label sk-toggleable__label-arrow\">cat</label><div class=\"sk-toggleable__content\"><pre>[&#x27;edad_band&#x27;, &#x27;sex&#x27;, &#x27;taquicardia&#x27;]</pre></div></div></div><div class=\"sk-serial\"><div class=\"sk-item\"><div class=\"sk-serial\"><div class=\"sk-item\"><div class=\"sk-estimator sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-17\" type=\"checkbox\" ><label for=\"sk-estimator-id-17\" class=\"sk-toggleable__label sk-toggleable__label-arrow\">SimpleImputer</label><div class=\"sk-toggleable__content\"><pre>SimpleImputer(strategy=&#x27;most_frequent&#x27;)</pre></div></div></div><div class=\"sk-item\"><div class=\"sk-estimator sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-18\" type=\"checkbox\" ><label for=\"sk-estimator-id-18\" class=\"sk-toggleable__label sk-toggleable__label-arrow\">OneHotEncoder</label><div class=\"sk-toggleable__content\"><pre>OneHotEncoder(handle_unknown=&#x27;ignore&#x27;, sparse_output=False)</pre></div></div></div></div></div></div></div></div></div></div><div class=\"sk-item\"><div class=\"sk-estimator sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-19\" type=\"checkbox\" ><label for=\"sk-estimator-id-19\" class=\"sk-toggleable__label sk-toggleable__label-arrow\">LogisticRegression</label><div class=\"sk-toggleable__content\"><pre>LogisticRegression(max_iter=1000)</pre></div></div></div></div></div></div></div></div></div></div></div></div>"], "text/plain": ["CalibratedClassifierCV(cv=3,\n", "                       estimator=Pipeline(steps=[('prep',\n", "                                                  ColumnTransformer(transformers=[('num',\n", "                                                                                   Pipeline(steps=[('imp',\n", "                                                                                                    SimpleImputer(strategy='median')),\n", "                                                                                                   ('sc',\n", "                                                                                                    StandardScaler())]),\n", "                                                                                   ['age_years',\n", "                                                                                    'bmi',\n", "                                                                                    'hr_bpm',\n", "                                                                                    'sbp_mmhg',\n", "                                                                                    'spo2_pct',\n", "                                                                                    'hb_g_dl',\n", "                                                                                    'creatinine_mg_dl',\n", "                                                                                    'glucose_mg_dl']),\n", "                                                                                  ('cat',\n", "                                                                                   Pipeline(steps=[('imp',\n", "                                                                                                    SimpleImputer(strategy='most_frequent')),\n", "                                                                                                   ('oh',\n", "                                                                                                    OneHotEncoder(handle_unknown='ignore',\n", "                                                                                                                  sparse_output=False))]),\n", "                                                                                   ['edad_band',\n", "                                                                                    'sex',\n", "                                                                                    'taquicardia'])])),\n", "                                                 ('model',\n", "                                                  LogisticRegression(max_iter=1000))]),\n", "                       method='isotonic')"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["#ajustamos el calibrador (reentrena y aprende)\n", "cal.fit(X_train, y_train)"]}, {"cell_type": "code", "execution_count": 6, "id": "779dc2d5-79de-400e-958f-562bf637100e", "metadata": {}, "outputs": [], "source": ["#Probabilidades de calibrado con el test\n", "p_cal =cal.predict_proba(X_test)[:,1]\n"]}, {"cell_type": "code", "execution_count": 7, "id": "4ef4bcd3-1972-4a83-847c-866277a17148", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Brier sin calibrado:  0.164\n", "Brier con calibrado:  0.160\n"]}], "source": ["#calculamos el Brier sin y con calibrado\n", "print(\"Brier sin calibrado: \", f\"{brier_score_loss(y_test, p_raw):.3f}\")\n", "print(\"Brier con calibrado: \", f\"{brier_score_loss(y_test, p_cal):.3f}\")"]}, {"cell_type": "code", "execution_count": 8, "id": "76700dde-d3e0-4246-bc19-24e5eaa3ea89", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ECE sin calibrado:  0.262\n", "ECE con calibrado:  0.153\n"]}], "source": ["# ECE sencillo\n", "def ece(y_true, p_hat, bins=10):\n", "    pr_true, pr_pred = calibration_curve(y_true, p_hat, n_bins=bins, strategy=\"quantile\")\n", "    return float(np.mean(np.abs(pr_pred - pr_true)))\n", "\n", "# calculamos el ECE sin y con calibrado\n", "print(\"ECE sin calibrado: \", f\"{ece(y_test, p_raw):.3f}\")\n", "print(\"ECE con calibrado: \", f\"{ece(y_test, p_cal):.3f}\")\n"]}, {"cell_type": "code", "execution_count": 9, "id": "80011479-10f0-4918-b1e7-68dded2b7ad9", "metadata": {}, "outputs": [], "source": ["# calculamos los puntos de la curva de calibracion\n", "# sin calibrar\n", "t1, p1 = calibration_curve(y_test, p_raw, n_bins=10, strategy=\"quantile\")\n", "# calibrada\n", "t2, p2 = calibration_curve(y_test, p_cal, n_bins=10, strategy=\"quantile\")"]}, {"cell_type": "code", "execution_count": 11, "id": "2327bb84-5d7a-4d38-9cf7-b96214b477b2", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# mostramos la grafica de ambas curvas\n", "plt.figure()\n", "plt.plot(p1, t1, label=\"Sin calibrar\")\n", "plt.plot(p2, t2, label=\"Calibrado Isotónico\")\n", "plt.plot([0,1],[0,1], linestyle=\"--\")\n", "plt.xlabel(\"Prob. Predicha\")\n", "plt.ylabel(\"Freq. Observada\")\n", "plt.title(\"Calibración (test)\")\n", "plt.legend()\n", "plt.tight_layout()\n", "plt.savefig(\"figures/S07_E3_curvas_calibracion.png\", dpi=120)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "813792d8-fb26-4226-a990-542b147aa955", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "anaconda-2024.02-py310", "language": "python", "name": "conda-env-anaconda-2024.02-py310-py"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 5}