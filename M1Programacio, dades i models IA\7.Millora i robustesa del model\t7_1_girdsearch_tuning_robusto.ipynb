{"cells": [{"cell_type": "markdown", "id": "8bf8e012-c0be-417f-a382-c759eafeba7c", "metadata": {}, "source": ["# GridSearch con class_weight. Tuning robusto CV multi-métrica con refit\n", "# por AUPRC + evaluación en test"]}, {"cell_type": "code", "execution_count": 3, "id": "555e3a6f-e12c-4052-9d42-d6362574cc69", "metadata": {}, "outputs": [], "source": ["import numpy as np, pandas as pd, matplotlib.pyplot as plt\n", "from sklearn.model_selection import train_test_split, StratifiedKFold,GridSearchCV\n", "from sklearn.compose import ColumnTransformer\n", "from sklearn.pipeline import Pipeline\n", "from sklearn.impute import SimpleImputer\n", "from sklearn.preprocessing import OneHotEncoder, StandardScaler\n", "from sklearn.linear_model import LogisticRegression\n", "from sklearn.metrics import (roc_auc_score, average_precision_score, roc_curve, precision_recall_curve, confusion_matrix)"]}, {"cell_type": "code", "execution_count": null, "id": "044330f8-edc9-49aa-bed6-3c843b6ee4bd", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Cargado baseS03.csv (200, 12)\n"]}], "source": ["pd.set_option(\"display.max_columns\", 120)\n", "pd.set_option(\"display.width\", 160)\n", "df= pd.read_csv(\"../baseS03.csv\")\n", "print(\"Cargado baseS03.csv\", df.shape)"]}, {"cell_type": "code", "execution_count": 5, "id": "a0f95d00-c256-4c9a-baca-86220a8c84c1", "metadata": {}, "outputs": [], "source": ["# target y predictores \n", "y= df[\"readmit_30d\"].astype(int)\n", "X = df.drop(columns=[\"readmit_30d\"])"]}, {"cell_type": "code", "execution_count": null, "id": "2f3fc016-c16f-4f8d-a31a-bc53d9f1c3dd", "metadata": {}, "outputs": [], "source": ["num_cols = X.select_dtypes(include=\"number\").columns.tolist()\n", "cat_cols = X.columns.difference(num_cols).tolist()\n", "\n", "prep = ColumnTransformer([(\"num\", Pipeline([(\"imp\", SimpleImputer(strategy=\"median\")),\n", "                                            (\"sc\", StandardScaler())]), num_cols),\n", "                          (\"cat\", Pipeline([(\"imp\", SimpleImputer(strategy=\"most_frequent\")),\n", "                                            (\"oh\", OneHotEncoder(handle_unknown=\"ignore\", sparse_output=False))\n", "]), cat_cols)\n", "])"]}, {"cell_type": "code", "execution_count": null, "id": "bcd84518-cc6d-4b1f-bdcb-abcaec07822b", "metadata": {}, "outputs": [], "source": ["# modelo base (logistica)\n", "pipe = Pipeline([\n", "(\"prep\", prep),\n", "(\"model\", LogisticRegression(max_iter=1000, solver=\"lbfgs\"))\n", "])"]}, {"cell_type": "code", "execution_count": null, "id": "98e5f035-ee79-438b-8976-f80ab4aa6cd4", "metadata": {}, "outputs": [], "source": ["X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.20, stratify=y, random_state=42)"]}, {"cell_type": "code", "execution_count": 9, "id": "5ab8cf46-7cdb-4931-9dc7-34e96340e74b", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "===Tuning(GridSearchCV, refit=AUPRC)===\n", "Mejor params:  {'model__C': np.float64(1.0), 'model__class_weight': 'balanced', 'model__penalty': 'l2'}\n", "CV ROC AUC:  0.623\n", "CV AUPRC:  0.340\n"]}], "source": ["# Grid logarítmico + scoring multi_metrica\n", "cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)\n", "#diccionario de metricas \n", "scoring={\"roc_auc\":\"roc_auc\",\"auprc\":\"average_precision\"}\n", "param_grid = {\n", "    \"model__C\": np.logspace(-3, 3, 7),\n", "    \"model__penalty\":[\"l2\"],\n", "    \"model__class_weight\": [None,\"balanced\"]}\n", "gcv = GridSearchCV(\n", "    estimator= pipe, param_grid=param_grid, cv=cv,\n", "    scoring=scoring, refit=\"auprc\", n_jobs=-1, return_train_score=False)\n", "print(\"\\n===Tuning(GridSearchCV, refit=AUPRC)===\")\n", "gcv.fit(X_train, y_train)\n", "print(\"Mejor params: \", gcv.best_params_)\n", "print(\"CV ROC AUC: \", f\"{gcv.cv_results_['mean_test_roc_auc'][gcv.best_index_]:.3f}\")\n", "print(\"CV AUPRC: \", f\"{gcv.cv_results_['mean_test_auprc'][gcv.best_index_]:.3f}\")"]}, {"cell_type": "code", "execution_count": 10, "id": "5f0325c0-87f1-4ec4-abd4-90320484c335", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== Evaluación en test ===\n", "ROC AUC:  0.515\n", "AUPRC:  0.211\n"]}], "source": ["# Evaluacion en test\n", "best_clf= gcv.best_estimator_\n", "proba = best_clf.predict_proba(X_test)[:,1]\n", "print(\"\\n=== Evaluación en test ===\")\n", "print(\"ROC AUC: \", f\"{roc_auc_score(y_test, proba):.3f}\")\n", "print(\"AUPRC: \", f\"{average_precision_score(y_test, proba):.3f}\")"]}, {"cell_type": "code", "execution_count": 11, "id": "9b4f0cf1-63ec-4322-a5cc-6f674d2dfbd6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["confusion (thr=0.5): TN 21 FP 12 FN 3 TP 4\n"]}], "source": ["# matriz de confusión a un umbral de 0.5\n", "pred05 = (proba >= 0.5).astype(int)\n", "tn, fp, fn, tp = confusion_matrix(y_test, pred05).ravel()\n", "print(\"confusion (thr=0.5): TN\", tn, \"FP\", fp, \"FN\", fn, \"TP\", tp)"]}, {"cell_type": "code", "execution_count": 12, "id": "9c1c8254-bf97-4a7f-b452-466007afbb06", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Curvas ROC y PR (sin estilos)\n", "plt.figure()\n", "fpr, tpr, _ = roc_curve(y_test, proba)\n", "plt.plot(fpr,tpr)\n", "plt.xlabel(\"FPR\")\n", "plt.ylabel(\"TPR\")\n", "plt.title(\"ROC (test)\")\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "plt.figure()\n", "prec, rec, _ = precision_recall_curve(y_test, proba)\n", "plt.plot(rec,prec)\n", "plt.xlabel(\"Recall\")\n", "plt.ylabel(\"Precission\")\n", "plt.title(\"PR (test)\")\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "fe1a9f84-cf26-4292-b1ff-725decaf218d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "d50b2572-c033-4d85-8b6f-9f063990e7cc", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "ia-bio", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}