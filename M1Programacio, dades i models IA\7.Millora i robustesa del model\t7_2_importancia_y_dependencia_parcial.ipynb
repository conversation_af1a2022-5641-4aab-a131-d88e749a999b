{"cells": [{"cell_type": "markdown", "id": "3b0f93a1-c394-4443-aba9-bd3a63162d5d", "metadata": {}, "source": ["# Importancia y dependencia parcial. \n", "# Explicabilidad + Calibración + Umbral operativo (OOF) + subgrupos"]}, {"cell_type": "code", "execution_count": 16, "id": "39c76102-836b-4469-a722-3315d411944d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Cargado baseS03.csv: (200, 12)\n"]}], "source": ["import numpy as np, pandas as pd, matplotlib.pyplot as plt, os\n", "from sklearn.model_selection import train_test_split, StratifiedKFold,cross_val_predict\n", "from sklearn.compose import ColumnTransformer\n", "from sklearn.pipeline import Pipeline\n", "from sklearn.impute import SimpleImputer\n", "from sklearn.preprocessing import OneHotEncoder, StandardScaler\n", "from sklearn.linear_model import LogisticRegression\n", "from sklearn.metrics import (roc_auc_score, average_precision_score, confusion_matrix,brier_score_loss)\n", "from sklearn.calibration import CalibratedClassifierCV, calibration_curve\n", "from sklearn.inspection import permutation_importance, PartialDependenceDisplay\n", "# --- Opciones de impresión\n", "pd.set_option(\"display.max_columns\", 120)\n", "pd.set_option(\"display.width\", 160)\n", "# --- Carga directa del dataset\n", "df = pd.read_csv(\"../baseS03.csv\")\n", "print(\"Cargado baseS03.csv:\", df.shape)"]}, {"cell_type": "code", "execution_count": 17, "id": "18bbfa83-93a0-4c58-a86e-7a5b1af63a38", "metadata": {}, "outputs": [], "source": ["y = df[\"readmit_30d\"].astype(int)\n", "X = df.drop(columns=[\"readmit_30d\"])"]}, {"cell_type": "code", "execution_count": 18, "id": "d2fa17db-189a-4ff0-adde-2491c7d340f6", "metadata": {}, "outputs": [], "source": ["num_cols = X.select_dtypes(include=\"number\").columns.tolist()\n", "cat_cols = X.columns.difference(num_cols).tolist()\n", "prep = ColumnTransformer([(\"num\", Pipeline([(\"imp\", SimpleImputer(strategy=\"median\")),\n", "                                            (\"sc\", StandardScaler())]), num_cols),\n", "                          (\"cat\", Pipeline([(\"imp\", SimpleImputer(strategy=\"most_frequent\")),\n", "                                            (\"oh\", OneHotEncoder(handle_unknown=\"ignore\", sparse_output=False))\n", "]), cat_cols)\n", "])"]}, {"cell_type": "code", "execution_count": 19, "id": "2e737124-44ca-433c-938b-0d1d3a34514a", "metadata": {}, "outputs": [{"data": {"text/html": ["<style>#sk-container-id-2 {\n", "  /* Definition of color scheme common for light and dark mode */\n", "  --sklearn-color-text: #000;\n", "  --sklearn-color-text-muted: #666;\n", "  --sklearn-color-line: gray;\n", "  /* Definition of color scheme for unfitted estimators */\n", "  --sklearn-color-unfitted-level-0: #fff5e6;\n", "  --sklearn-color-unfitted-level-1: #f6e4d2;\n", "  --sklearn-color-unfitted-level-2: #ffe0b3;\n", "  --sklearn-color-unfitted-level-3: chocolate;\n", "  /* Definition of color scheme for fitted estimators */\n", "  --sklearn-color-fitted-level-0: #f0f8ff;\n", "  --sklearn-color-fitted-level-1: #d4ebff;\n", "  --sklearn-color-fitted-level-2: #b3dbfd;\n", "  --sklearn-color-fitted-level-3: cornflowerblue;\n", "\n", "  /* Specific color for light theme */\n", "  --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, white)));\n", "  --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-icon: #696969;\n", "\n", "  @media (prefers-color-scheme: dark) {\n", "    /* Redefinition of color scheme for dark theme */\n", "    --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, #111)));\n", "    --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-icon: #878787;\n", "  }\n", "}\n", "\n", "#sk-container-id-2 {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "#sk-container-id-2 pre {\n", "  padding: 0;\n", "}\n", "\n", "#sk-container-id-2 input.sk-hidden--visually {\n", "  border: 0;\n", "  clip: rect(1px 1px 1px 1px);\n", "  clip: rect(1px, 1px, 1px, 1px);\n", "  height: 1px;\n", "  margin: -1px;\n", "  overflow: hidden;\n", "  padding: 0;\n", "  position: absolute;\n", "  width: 1px;\n", "}\n", "\n", "#sk-container-id-2 div.sk-dashed-wrapped {\n", "  border: 1px dashed var(--sklearn-color-line);\n", "  margin: 0 0.4em 0.5em 0.4em;\n", "  box-sizing: border-box;\n", "  padding-bottom: 0.4em;\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "#sk-container-id-2 div.sk-container {\n", "  /* jup<PERSON>r's `normalize.less` sets `[hidden] { display: none; }`\n", "     but bootstrap.min.css set `[hidden] { display: none !important; }`\n", "     so we also need the `!important` here to be able to override the\n", "     default hidden behavior on the sphinx rendered scikit-learn.org.\n", "     See: https://github.com/scikit-learn/scikit-learn/issues/21755 */\n", "  display: inline-block !important;\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-2 div.sk-text-repr-fallback {\n", "  display: none;\n", "}\n", "\n", "div.sk-parallel-item,\n", "div.sk-serial,\n", "div.sk-item {\n", "  /* draw centered vertical line to link estimators */\n", "  background-image: linear-gradient(var(--sklearn-color-text-on-default-background), var(--sklearn-color-text-on-default-background));\n", "  background-size: 2px 100%;\n", "  background-repeat: no-repeat;\n", "  background-position: center center;\n", "}\n", "\n", "/* Parallel-specific style estimator block */\n", "\n", "#sk-container-id-2 div.sk-parallel-item::after {\n", "  content: \"\";\n", "  width: 100%;\n", "  border-bottom: 2px solid var(--sklearn-color-text-on-default-background);\n", "  flex-grow: 1;\n", "}\n", "\n", "#sk-container-id-2 div.sk-parallel {\n", "  display: flex;\n", "  align-items: stretch;\n", "  justify-content: center;\n", "  background-color: var(--sklearn-color-background);\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-2 div.sk-parallel-item {\n", "  display: flex;\n", "  flex-direction: column;\n", "}\n", "\n", "#sk-container-id-2 div.sk-parallel-item:first-child::after {\n", "  align-self: flex-end;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-2 div.sk-parallel-item:last-child::after {\n", "  align-self: flex-start;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-2 div.sk-parallel-item:only-child::after {\n", "  width: 0;\n", "}\n", "\n", "/* Serial-specific style estimator block */\n", "\n", "#sk-container-id-2 div.sk-serial {\n", "  display: flex;\n", "  flex-direction: column;\n", "  align-items: center;\n", "  background-color: var(--sklearn-color-background);\n", "  padding-right: 1em;\n", "  padding-left: 1em;\n", "}\n", "\n", "\n", "/* Toggleable style: style used for estimator/Pipeline/ColumnTransformer box that is\n", "clickable and can be expanded/collapsed.\n", "- Pipeline and ColumnTransformer use this feature and define the default style\n", "- Estimators will overwrite some part of the style using the `sk-estimator` class\n", "*/\n", "\n", "/* Pipeline and ColumnTransformer style (default) */\n", "\n", "#sk-container-id-2 div.sk-toggleable {\n", "  /* Default theme specific background. It is overwritten whether we have a\n", "  specific estimator or a Pipeline/ColumnTransformer */\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "/* Toggleable label */\n", "#sk-container-id-2 label.sk-toggleable__label {\n", "  cursor: pointer;\n", "  display: flex;\n", "  width: 100%;\n", "  margin-bottom: 0;\n", "  padding: 0.5em;\n", "  box-sizing: border-box;\n", "  text-align: center;\n", "  align-items: start;\n", "  justify-content: space-between;\n", "  gap: 0.5em;\n", "}\n", "\n", "#sk-container-id-2 label.sk-toggleable__label .caption {\n", "  font-size: 0.6rem;\n", "  font-weight: lighter;\n", "  color: var(--sklearn-color-text-muted);\n", "}\n", "\n", "#sk-container-id-2 label.sk-toggleable__label-arrow:before {\n", "  /* <PERSON> on the left of the label */\n", "  content: \"▸\";\n", "  float: left;\n", "  margin-right: 0.25em;\n", "  color: var(--sklearn-color-icon);\n", "}\n", "\n", "#sk-container-id-2 label.sk-toggleable__label-arrow:hover:before {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "/* Toggleable content - dropdown */\n", "\n", "#sk-container-id-2 div.sk-toggleable__content {\n", "  display: none;\n", "  text-align: left;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-2 div.sk-toggleable__content.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-2 div.sk-toggleable__content pre {\n", "  margin: 0.2em;\n", "  border-radius: 0.25em;\n", "  color: var(--sklearn-color-text);\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-2 div.sk-toggleable__content.fitted pre {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-2 input.sk-toggleable__control:checked~div.sk-toggleable__content {\n", "  /* Expand drop-down */\n", "  display: block;\n", "  width: 100%;\n", "  overflow: visible;\n", "}\n", "\n", "#sk-container-id-2 input.sk-toggleable__control:checked~label.sk-toggleable__label-arrow:before {\n", "  content: \"▾\";\n", "}\n", "\n", "/* Pipeline/ColumnTransformer-specific style */\n", "\n", "#sk-container-id-2 div.sk-label input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-2 div.sk-label.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator-specific style */\n", "\n", "/* Colorize estimator box */\n", "#sk-container-id-2 div.sk-estimator input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-2 div.sk-estimator.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "#sk-container-id-2 div.sk-label label.sk-toggleable__label,\n", "#sk-container-id-2 div.sk-label label {\n", "  /* The background is the default theme color */\n", "  color: var(--sklearn-color-text-on-default-background);\n", "}\n", "\n", "/* On hover, darken the color of the background */\n", "#sk-container-id-2 div.sk-label:hover label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "/* Label box, darken color on hover, fitted */\n", "#sk-container-id-2 div.sk-label.fitted:hover label.sk-toggleable__label.fitted {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator label */\n", "\n", "#sk-container-id-2 div.sk-label label {\n", "  font-family: monospace;\n", "  font-weight: bold;\n", "  display: inline-block;\n", "  line-height: 1.2em;\n", "}\n", "\n", "#sk-container-id-2 div.sk-label-container {\n", "  text-align: center;\n", "}\n", "\n", "/* Estimator-specific */\n", "#sk-container-id-2 div.sk-estimator {\n", "  font-family: monospace;\n", "  border: 1px dotted var(--sklearn-color-border-box);\n", "  border-radius: 0.25em;\n", "  box-sizing: border-box;\n", "  margin-bottom: 0.5em;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-2 div.sk-estimator.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "/* on hover */\n", "#sk-container-id-2 div.sk-estimator:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-2 div.sk-estimator.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Specification for estimator info (e.g. \"i\" and \"?\") */\n", "\n", "/* Common style for \"i\" and \"?\" */\n", "\n", ".sk-estimator-doc-link,\n", "a:link.sk-estimator-doc-link,\n", "a:visited.sk-estimator-doc-link {\n", "  float: right;\n", "  font-size: smaller;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1em;\n", "  height: 1em;\n", "  width: 1em;\n", "  text-decoration: none !important;\n", "  margin-left: 0.5em;\n", "  text-align: center;\n", "  /* unfitted */\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted,\n", "a:link.sk-estimator-doc-link.fitted,\n", "a:visited.sk-estimator-doc-link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "div.sk-estimator:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "div.sk-estimator.fitted:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "/* Span, style for the box shown on hovering the info icon */\n", ".sk-estimator-doc-link span {\n", "  display: none;\n", "  z-index: 9999;\n", "  position: relative;\n", "  font-weight: normal;\n", "  right: .2ex;\n", "  padding: .5ex;\n", "  margin: .5ex;\n", "  width: min-content;\n", "  min-width: 20ex;\n", "  max-width: 50ex;\n", "  color: var(--sklearn-color-text);\n", "  box-shadow: 2pt 2pt 4pt #999;\n", "  /* unfitted */\n", "  background: var(--sklearn-color-unfitted-level-0);\n", "  border: .5pt solid var(--sklearn-color-unfitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted span {\n", "  /* fitted */\n", "  background: var(--sklearn-color-fitted-level-0);\n", "  border: var(--sklearn-color-fitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link:hover span {\n", "  display: block;\n", "}\n", "\n", "/* \"?\"-specific style due to the `<a>` HTML tag */\n", "\n", "#sk-container-id-2 a.estimator_doc_link {\n", "  float: right;\n", "  font-size: 1rem;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1rem;\n", "  height: 1rem;\n", "  width: 1rem;\n", "  text-decoration: none;\n", "  /* unfitted */\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "}\n", "\n", "#sk-container-id-2 a.estimator_doc_link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "#sk-container-id-2 a.estimator_doc_link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "#sk-container-id-2 a.estimator_doc_link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "}\n", "\n", ".estimator-table summary {\n", "    padding: .5rem;\n", "    font-family: monospace;\n", "    cursor: pointer;\n", "}\n", "\n", ".estimator-table details[open] {\n", "    padding-left: 0.1rem;\n", "    padding-right: 0.1rem;\n", "    padding-bottom: 0.3rem;\n", "}\n", "\n", ".estimator-table .parameters-table {\n", "    margin-left: auto !important;\n", "    margin-right: auto !important;\n", "}\n", "\n", ".estimator-table .parameters-table tr:nth-child(odd) {\n", "    background-color: #fff;\n", "}\n", "\n", ".estimator-table .parameters-table tr:nth-child(even) {\n", "    background-color: #f6f6f6;\n", "}\n", "\n", ".estimator-table .parameters-table tr:hover {\n", "    background-color: #e0e0e0;\n", "}\n", "\n", ".estimator-table table td {\n", "    border: 1px solid rgba(106, 105, 104, 0.232);\n", "}\n", "\n", ".user-set td {\n", "    color:rgb(255, 94, 0);\n", "    text-align: left;\n", "}\n", "\n", ".user-set td.value pre {\n", "    color:rgb(255, 94, 0) !important;\n", "    background-color: transparent !important;\n", "}\n", "\n", ".default td {\n", "    color: black;\n", "    text-align: left;\n", "}\n", "\n", ".user-set td i,\n", ".default td i {\n", "    color: black;\n", "}\n", "\n", ".copy-paste-icon {\n", "    background-image: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCA0NDggNTEyIj48IS0tIUZvbnQgQXdlc29tZSBGcmVlIDYuNy4yIGJ5IEBmb250YXdlc29tZSAtIGh0dHBzOi8vZm9udGF3ZXNvbWUuY29tIExpY2Vuc2UgLSBodHRwczovL2ZvbnRhd2Vzb21lLmNvbS9saWNlbnNlL2ZyZWUgQ29weXJpZ2h0IDIwMjUgRm9udGljb25zLCBJbmMuLS0+PHBhdGggZD0iTTIwOCAwTDMzMi4xIDBjMTIuNyAwIDI0LjkgNS4xIDMzLjkgMTQuMWw2Ny45IDY3LjljOSA5IDE0LjEgMjEuMiAxNC4xIDMzLjlMNDQ4IDMzNmMwIDI2LjUtMjEuNSA0OC00OCA0OGwtMTkyIDBjLTI2LjUgMC00OC0yMS41LTQ4LTQ4bDAtMjg4YzAtMjYuNSAyMS41LTQ4IDQ4LTQ4ek00OCAxMjhsODAgMCAwIDY0LTY0IDAgMCAyNTYgMTkyIDAgMC0zMiA2NCAwIDAgNDhjMCAyNi41LTIxLjUgNDgtNDggNDhMNDggNTEyYy0yNi41IDAtNDgtMjEuNS00OC00OEwwIDE3NmMwLTI2LjUgMjEuNS00OCA0OC00OHoiLz48L3N2Zz4=);\n", "    background-repeat: no-repeat;\n", "    background-size: 14px 14px;\n", "    background-position: 0;\n", "    display: inline-block;\n", "    width: 14px;\n", "    height: 14px;\n", "    cursor: pointer;\n", "}\n", "</style><body><div id=\"sk-container-id-2\" class=\"sk-top-container\"><div class=\"sk-text-repr-fallback\"><pre>Pipeline(steps=[(&#x27;prep&#x27;,\n", "                 ColumnTransformer(transformers=[(&#x27;num&#x27;,\n", "                                                  Pipeline(steps=[(&#x27;imp&#x27;,\n", "                                                                   SimpleImputer(strategy=&#x27;median&#x27;)),\n", "                                                                  (&#x27;sc&#x27;,\n", "                                                                   StandardScaler())]),\n", "                                                  [&#x27;age_years&#x27;, &#x27;bmi&#x27;, &#x27;hr_bpm&#x27;,\n", "                                                   &#x27;sbp_mmhg&#x27;, &#x27;spo2_pct&#x27;,\n", "                                                   &#x27;hb_g_dl&#x27;,\n", "                                                   &#x27;creatinine_mg_dl&#x27;,\n", "                                                   &#x27;glucose_mg_dl&#x27;]),\n", "                                                 (&#x27;cat&#x27;,\n", "                                                  Pipeline(steps=[(&#x27;imp&#x27;,\n", "                                                                   SimpleImputer(strategy=&#x27;most_frequent&#x27;)),\n", "                                                                  (&#x27;oh&#x27;,\n", "                                                                   OneHotEncoder(handle_unknown=&#x27;ignore&#x27;,\n", "                                                                                 sparse_output=False))]),\n", "                                                  [&#x27;edad_band&#x27;, &#x27;sex&#x27;,\n", "                                                   &#x27;taquicardia&#x27;])])),\n", "                (&#x27;model&#x27;, LogisticRegression(max_iter=1000))])</pre><b>In a Jupyter environment, please rerun this cell to show the HTML representation or trust the notebook. <br />On GitHub, the HTML representation is unable to render, please try loading this page with nbviewer.org.</b></div><div class=\"sk-container\" hidden><div class=\"sk-item sk-dashed-wrapped\"><div class=\"sk-label-container\"><div class=\"sk-label fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-10\" type=\"checkbox\" ><label for=\"sk-estimator-id-10\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow\"><div><div>Pipeline</div></div><div><a class=\"sk-estimator-doc-link fitted\" rel=\"noreferrer\" target=\"_blank\" href=\"https://scikit-learn.org/1.7/modules/generated/sklearn.pipeline.Pipeline.html\">?<span>Documentation for Pipeline</span></a><span class=\"sk-estimator-doc-link fitted\">i<span>Fitted</span></span></div></label><div class=\"sk-toggleable__content fitted\" data-param-prefix=\"\">\n", "        <div class=\"estimator-table\">\n", "            <details>\n", "                <summary>Parameters</summary>\n", "                <table class=\"parameters-table\">\n", "                  <tbody>\n", "                    \n", "        <tr class=\"user-set\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('steps',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">steps&nbsp;</td>\n", "            <td class=\"value\">[(&#x27;prep&#x27;, ...), (&#x27;model&#x27;, ...)]</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('transform_input',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">transform_input&nbsp;</td>\n", "            <td class=\"value\">None</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('memory',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">memory&nbsp;</td>\n", "            <td class=\"value\">None</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('verbose',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">verbose&nbsp;</td>\n", "            <td class=\"value\">False</td>\n", "        </tr>\n", "    \n", "                  </tbody>\n", "                </table>\n", "            </details>\n", "        </div>\n", "    </div></div></div><div class=\"sk-serial\"><div class=\"sk-item sk-dashed-wrapped\"><div class=\"sk-label-container\"><div class=\"sk-label fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-11\" type=\"checkbox\" ><label for=\"sk-estimator-id-11\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow\"><div><div>prep: ColumnTransformer</div></div><div><a class=\"sk-estimator-doc-link fitted\" rel=\"noreferrer\" target=\"_blank\" href=\"https://scikit-learn.org/1.7/modules/generated/sklearn.compose.ColumnTransformer.html\">?<span>Documentation for prep: ColumnTransformer</span></a></div></label><div class=\"sk-toggleable__content fitted\" data-param-prefix=\"prep__\">\n", "        <div class=\"estimator-table\">\n", "            <details>\n", "                <summary>Parameters</summary>\n", "                <table class=\"parameters-table\">\n", "                  <tbody>\n", "                    \n", "        <tr class=\"user-set\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('transformers',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">transformers&nbsp;</td>\n", "            <td class=\"value\">[(&#x27;num&#x27;, ...), (&#x27;cat&#x27;, ...)]</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('remainder',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">remainder&nbsp;</td>\n", "            <td class=\"value\">&#x27;drop&#x27;</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('sparse_threshold',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">sparse_threshold&nbsp;</td>\n", "            <td class=\"value\">0.3</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('n_jobs',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">n_jobs&nbsp;</td>\n", "            <td class=\"value\">None</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('transformer_weights',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">transformer_weights&nbsp;</td>\n", "            <td class=\"value\">None</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('verbose',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">verbose&nbsp;</td>\n", "            <td class=\"value\">False</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('verbose_feature_names_out',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">verbose_feature_names_out&nbsp;</td>\n", "            <td class=\"value\">True</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('force_int_remainder_cols',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">force_int_remainder_cols&nbsp;</td>\n", "            <td class=\"value\">&#x27;deprecated&#x27;</td>\n", "        </tr>\n", "    \n", "                  </tbody>\n", "                </table>\n", "            </details>\n", "        </div>\n", "    </div></div></div><div class=\"sk-parallel\"><div class=\"sk-parallel-item\"><div class=\"sk-item\"><div class=\"sk-label-container\"><div class=\"sk-label fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-12\" type=\"checkbox\" ><label for=\"sk-estimator-id-12\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow\"><div><div>num</div></div></label><div class=\"sk-toggleable__content fitted\" data-param-prefix=\"prep__num__\"><pre>[&#x27;age_years&#x27;, &#x27;bmi&#x27;, &#x27;hr_bpm&#x27;, &#x27;sbp_mmhg&#x27;, &#x27;spo2_pct&#x27;, &#x27;hb_g_dl&#x27;, &#x27;creatinine_mg_dl&#x27;, &#x27;glucose_mg_dl&#x27;]</pre></div></div></div><div class=\"sk-serial\"><div class=\"sk-item\"><div class=\"sk-serial\"><div class=\"sk-item\"><div class=\"sk-estimator fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-13\" type=\"checkbox\" ><label for=\"sk-estimator-id-13\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow\"><div><div>SimpleImputer</div></div><div><a class=\"sk-estimator-doc-link fitted\" rel=\"noreferrer\" target=\"_blank\" href=\"https://scikit-learn.org/1.7/modules/generated/sklearn.impute.SimpleImputer.html\">?<span>Documentation for SimpleImputer</span></a></div></label><div class=\"sk-toggleable__content fitted\" data-param-prefix=\"prep__num__imp__\">\n", "        <div class=\"estimator-table\">\n", "            <details>\n", "                <summary>Parameters</summary>\n", "                <table class=\"parameters-table\">\n", "                  <tbody>\n", "                    \n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('missing_values',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">missing_values&nbsp;</td>\n", "            <td class=\"value\">nan</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"user-set\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('strategy',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">strategy&nbsp;</td>\n", "            <td class=\"value\">&#x27;median&#x27;</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('fill_value',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">fill_value&nbsp;</td>\n", "            <td class=\"value\">None</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('copy',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">copy&nbsp;</td>\n", "            <td class=\"value\">True</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('add_indicator',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">add_indicator&nbsp;</td>\n", "            <td class=\"value\">False</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('keep_empty_features',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">keep_empty_features&nbsp;</td>\n", "            <td class=\"value\">False</td>\n", "        </tr>\n", "    \n", "                  </tbody>\n", "                </table>\n", "            </details>\n", "        </div>\n", "    </div></div></div><div class=\"sk-item\"><div class=\"sk-estimator fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-14\" type=\"checkbox\" ><label for=\"sk-estimator-id-14\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow\"><div><div>StandardScaler</div></div><div><a class=\"sk-estimator-doc-link fitted\" rel=\"noreferrer\" target=\"_blank\" href=\"https://scikit-learn.org/1.7/modules/generated/sklearn.preprocessing.StandardScaler.html\">?<span>Documentation for StandardScaler</span></a></div></label><div class=\"sk-toggleable__content fitted\" data-param-prefix=\"prep__num__sc__\">\n", "        <div class=\"estimator-table\">\n", "            <details>\n", "                <summary>Parameters</summary>\n", "                <table class=\"parameters-table\">\n", "                  <tbody>\n", "                    \n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('copy',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">copy&nbsp;</td>\n", "            <td class=\"value\">True</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('with_mean',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">with_mean&nbsp;</td>\n", "            <td class=\"value\">True</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('with_std',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">with_std&nbsp;</td>\n", "            <td class=\"value\">True</td>\n", "        </tr>\n", "    \n", "                  </tbody>\n", "                </table>\n", "            </details>\n", "        </div>\n", "    </div></div></div></div></div></div></div></div><div class=\"sk-parallel-item\"><div class=\"sk-item\"><div class=\"sk-label-container\"><div class=\"sk-label fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-15\" type=\"checkbox\" ><label for=\"sk-estimator-id-15\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow\"><div><div>cat</div></div></label><div class=\"sk-toggleable__content fitted\" data-param-prefix=\"prep__cat__\"><pre>[&#x27;edad_band&#x27;, &#x27;sex&#x27;, &#x27;taquicardia&#x27;]</pre></div></div></div><div class=\"sk-serial\"><div class=\"sk-item\"><div class=\"sk-serial\"><div class=\"sk-item\"><div class=\"sk-estimator fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-16\" type=\"checkbox\" ><label for=\"sk-estimator-id-16\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow\"><div><div>SimpleImputer</div></div><div><a class=\"sk-estimator-doc-link fitted\" rel=\"noreferrer\" target=\"_blank\" href=\"https://scikit-learn.org/1.7/modules/generated/sklearn.impute.SimpleImputer.html\">?<span>Documentation for SimpleImputer</span></a></div></label><div class=\"sk-toggleable__content fitted\" data-param-prefix=\"prep__cat__imp__\">\n", "        <div class=\"estimator-table\">\n", "            <details>\n", "                <summary>Parameters</summary>\n", "                <table class=\"parameters-table\">\n", "                  <tbody>\n", "                    \n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('missing_values',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">missing_values&nbsp;</td>\n", "            <td class=\"value\">nan</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"user-set\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('strategy',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">strategy&nbsp;</td>\n", "            <td class=\"value\">&#x27;most_frequent&#x27;</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('fill_value',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">fill_value&nbsp;</td>\n", "            <td class=\"value\">None</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('copy',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">copy&nbsp;</td>\n", "            <td class=\"value\">True</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('add_indicator',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">add_indicator&nbsp;</td>\n", "            <td class=\"value\">False</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('keep_empty_features',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">keep_empty_features&nbsp;</td>\n", "            <td class=\"value\">False</td>\n", "        </tr>\n", "    \n", "                  </tbody>\n", "                </table>\n", "            </details>\n", "        </div>\n", "    </div></div></div><div class=\"sk-item\"><div class=\"sk-estimator fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-17\" type=\"checkbox\" ><label for=\"sk-estimator-id-17\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow\"><div><div>OneHotEncoder</div></div><div><a class=\"sk-estimator-doc-link fitted\" rel=\"noreferrer\" target=\"_blank\" href=\"https://scikit-learn.org/1.7/modules/generated/sklearn.preprocessing.OneHotEncoder.html\">?<span>Documentation for OneHotEncoder</span></a></div></label><div class=\"sk-toggleable__content fitted\" data-param-prefix=\"prep__cat__oh__\">\n", "        <div class=\"estimator-table\">\n", "            <details>\n", "                <summary>Parameters</summary>\n", "                <table class=\"parameters-table\">\n", "                  <tbody>\n", "                    \n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('categories',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">categories&nbsp;</td>\n", "            <td class=\"value\">&#x27;auto&#x27;</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('drop',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">drop&nbsp;</td>\n", "            <td class=\"value\">None</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"user-set\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('sparse_output',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">sparse_output&nbsp;</td>\n", "            <td class=\"value\">False</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('dtype',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">dtype&nbsp;</td>\n", "            <td class=\"value\">&lt;class &#x27;numpy.float64&#x27;&gt;</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"user-set\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('handle_unknown',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">handle_unknown&nbsp;</td>\n", "            <td class=\"value\">&#x27;ignore&#x27;</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('min_frequency',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">min_frequency&nbsp;</td>\n", "            <td class=\"value\">None</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('max_categories',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">max_categories&nbsp;</td>\n", "            <td class=\"value\">None</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('feature_name_combiner',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">feature_name_combiner&nbsp;</td>\n", "            <td class=\"value\">&#x27;concat&#x27;</td>\n", "        </tr>\n", "    \n", "                  </tbody>\n", "                </table>\n", "            </details>\n", "        </div>\n", "    </div></div></div></div></div></div></div></div></div></div><div class=\"sk-item\"><div class=\"sk-estimator fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-18\" type=\"checkbox\" ><label for=\"sk-estimator-id-18\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow\"><div><div>LogisticRegression</div></div><div><a class=\"sk-estimator-doc-link fitted\" rel=\"noreferrer\" target=\"_blank\" href=\"https://scikit-learn.org/1.7/modules/generated/sklearn.linear_model.LogisticRegression.html\">?<span>Documentation for LogisticRegression</span></a></div></label><div class=\"sk-toggleable__content fitted\" data-param-prefix=\"model__\">\n", "        <div class=\"estimator-table\">\n", "            <details>\n", "                <summary>Parameters</summary>\n", "                <table class=\"parameters-table\">\n", "                  <tbody>\n", "                    \n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('penalty',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">penalty&nbsp;</td>\n", "            <td class=\"value\">&#x27;l2&#x27;</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('dual',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">dual&nbsp;</td>\n", "            <td class=\"value\">False</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('tol',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">tol&nbsp;</td>\n", "            <td class=\"value\">0.0001</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('C',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">C&nbsp;</td>\n", "            <td class=\"value\">1.0</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('fit_intercept',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">fit_intercept&nbsp;</td>\n", "            <td class=\"value\">True</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('intercept_scaling',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">intercept_scaling&nbsp;</td>\n", "            <td class=\"value\">1</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('class_weight',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">class_weight&nbsp;</td>\n", "            <td class=\"value\">None</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('random_state',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">random_state&nbsp;</td>\n", "            <td class=\"value\">None</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('solver',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">solver&nbsp;</td>\n", "            <td class=\"value\">&#x27;lbfgs&#x27;</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"user-set\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('max_iter',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">max_iter&nbsp;</td>\n", "            <td class=\"value\">1000</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('multi_class',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">multi_class&nbsp;</td>\n", "            <td class=\"value\">&#x27;deprecated&#x27;</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('verbose',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">verbose&nbsp;</td>\n", "            <td class=\"value\">0</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('warm_start',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">warm_start&nbsp;</td>\n", "            <td class=\"value\">False</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('n_jobs',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">n_jobs&nbsp;</td>\n", "            <td class=\"value\">None</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('l1_ratio',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">l1_ratio&nbsp;</td>\n", "            <td class=\"value\">None</td>\n", "        </tr>\n", "    \n", "                  </tbody>\n", "                </table>\n", "            </details>\n", "        </div>\n", "    </div></div></div></div></div></div></div><script>function copyToClipboard(text, element) {\n", "    // Get the parameter prefix from the closest toggleable content\n", "    const toggleableContent = element.closest('.sk-toggleable__content');\n", "    const paramPrefix = toggleableContent ? toggleableContent.dataset.paramPrefix : '';\n", "    const fullParamName = paramPrefix ? `${paramPrefix}${text}` : text;\n", "\n", "    const originalStyle = element.style;\n", "    const computedStyle = window.getComputedStyle(element);\n", "    const originalWidth = computedStyle.width;\n", "    const originalHTML = element.innerHTML.replace('Copied!', '');\n", "\n", "    navigator.clipboard.writeText(fullParamName)\n", "        .then(() => {\n", "            element.style.width = originalWidth;\n", "            element.style.color = 'green';\n", "            element.innerHTML = \"Copied!\";\n", "\n", "            setTimeout(() => {\n", "                element.innerHTML = originalHTML;\n", "                element.style = originalStyle;\n", "            }, 2000);\n", "        })\n", "        .catch(err => {\n", "            console.error('Failed to copy:', err);\n", "            element.style.color = 'red';\n", "            element.innerHTML = \"Failed!\";\n", "            setTimeout(() => {\n", "                element.innerHTML = originalHTML;\n", "                element.style = originalStyle;\n", "            }, 2000);\n", "        });\n", "    return false;\n", "}\n", "\n", "document.querySelectorAll('.fa-regular.fa-copy').forEach(function(element) {\n", "    const toggleableContent = element.closest('.sk-toggleable__content');\n", "    const paramPrefix = toggleableContent ? toggleableContent.dataset.paramPrefix : '';\n", "    const paramName = element.parentElement.nextElementSibling.textContent.trim();\n", "    const fullParamName = paramPrefix ? `${paramPrefix}${paramName}` : paramName;\n", "\n", "    element.setAttribute('title', fullParamName);\n", "});\n", "</script></body>"], "text/plain": ["Pipeline(steps=[('prep',\n", "                 ColumnTransformer(transformers=[('num',\n", "                                                  Pipeline(steps=[('imp',\n", "                                                                   SimpleImputer(strategy='median')),\n", "                                                                  ('sc',\n", "                                                                   StandardScaler())]),\n", "                                                  ['age_years', 'bmi', 'hr_bpm',\n", "                                                   'sbp_mmhg', 'spo2_pct',\n", "                                                   'hb_g_dl',\n", "                                                   'creatinine_mg_dl',\n", "                                                   'glucose_mg_dl']),\n", "                                                 ('cat',\n", "                                                  Pipeline(steps=[('imp',\n", "                                                                   SimpleImputer(strategy='most_frequent')),\n", "                                                                  ('oh',\n", "                                                                   OneHotEncoder(handle_unknown='ignore',\n", "                                                                                 sparse_output=False))]),\n", "                                                  ['edad_band', 'sex',\n", "                                                   'taquicardia'])])),\n", "                ('model', LogisticRegression(max_iter=1000))])"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["clf = Pipeline([\n", "(\"prep\", prep),\n", "(\"model\", LogisticRegression(max_iter=1000, solver=\"lbfgs\"))\n", "])\n", "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.20, stratify=y, random_state=42)\n", "clf.fit(X_train, y_train)"]}, {"cell_type": "code", "execution_count": 20, "id": "611d74ef-1ec9-4d8b-8c97-38ae67aeaaaf", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "===Métricas sin calibrar (test)===\n", "ROC AUC:  0.524\n", "AUPRC:  0.212\n"]}], "source": ["# Probabilidades de la clase positiva en test (sin calibrar)\n", "p_test_raw = clf.predict_proba(X_test)[:,1]\n", "print(\"\\n===Métricas sin calibrar (test)===\")\n", "print(\"ROC AUC: \", f\"{roc_auc_score(y_test, p_test_raw):.3f}\")\n", "print(\"AUPRC: \", f\"{average_precision_score(y_test, p_test_raw):.3f}\")\n", "\n"]}, {"cell_type": "code", "execution_count": 21, "id": "1e889dbc-8bd6-40f1-b9e1-c32b90898858", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", " === Importancia por permutacion en test ===\n", "Top(10):  ['num__sbp_mmhg', 'cat__edad_band_40-59', 'num__hr_bpm', 'num__creatinine_mg_dl', 'cat__edad_band_<40', 'num__hb_g_dl', 'num__age_years', 'cat__edad_band_60-74', 'num__spo2_pct', 'num__bmi']\n"]}], "source": ["# Importancia por permutación en test\n", "print(\"\\n === Importancia por permutacion en test ===\")\n", "perm = permutation_importance(\n", "    clf, X_test, y_test,\n", "    n_repeats=20, random_state= 42,\n", "    scoring=\"average_precision\", n_jobs=-1)\n", "imp_mean = perm.importances_mean\n", "feat_names = clf.named_steps[\"prep\"].get_feature_names_out()\n", "topk_idx = np.argsort(imp_mean)[::-1][:10]\n", "topk_names = [feat_names[i] for i in topk_idx]\n", "print(\"Top(10): \", topk_names)"]}, {"cell_type": "code", "execution_count": 22, "id": "afb44884-e31c-4ca0-b9f9-9daa64271c2d", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# grafico de importancias (Top-10)\n", "plt.figure()\n", "plt.bar(range(len(topk_idx)), imp_mean[topk_idx])\n", "plt.xticks(range(len(topk_idx)),topk_names, rotation=45, ha=\"right\")\n", "plt.title(\"Importancia por permutación (Top 10)\")\n", "plt.tight_layout()\n", "plt.savefig(\"T7_2_Importancia_por_permutación_top10.png\", dpi=120)\n", "plt.show()\n", "\n", "    "]}, {"cell_type": "code", "execution_count": 23, "id": "93e30608-c52f-48c5-985b-0aaf4eee1502", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", " === Partial Dependence Plot (PDP) ==\n"]}, {"data": {"image/png": "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******************************/R01+t0b6oWPl5e+rth8L0QJ2SdncLboRgBwBANpi/JUL9p61XbGKKSubPo4ndwlWz5IWNfcDNQrADAOAGpKY6NHbRLo1btMu0G1YoqI+61FOhfNRHxc1HsAMAIIti4pPUb9oG/brtuGk/1qSc/tM6VD6sp4NNCHYAAGTBnhPn9PTk1dpzIla+3p56o30tPRReyu5uwc0R7AAAyKRF246r79T1iklIVvFgf03oGq7apfPb3S2AYAcAQGbW0437bZfG/nphPd2t5Qrqo0frqXAg6+ngHAh2AABksD6dtet10fZI0+7WsKyGtq1upmEBZ0GwAwDgH+w8HqN//bc+Hevp4MwIdgAAXMcvG4/ppe8vnPdq1aez1tPVKkV9Ojgngh0AAFeRnJKqd+bv0MQ/95p2k0qF9EHneirIea9wYgQ7AAAucyo2Uf/+dq2W7T5p2v9qXkEvtazKea9wegQ7AAAusenwWfX6eo2OnDmvvL5eeueh2moTVtzubgEZQrADAOC/vl9zWP+ZtUmJyakqHxJgznutUjTQ7m4BGUawAwC4PSvIjfh5q75accC076pWRGM61VFwHh+7uwZkCsEOAODWjkfH69lv1mrNgdOm3bdFZT1/Z2V5enrY3TUg0wh2AAC3tXr/KT3zzVqdiElQoL+**************************/r00081aNAgnTp1ylxbu3atjhw5kvWeAABwkzgcDk3+a78embTChLoqRfNp9nNNCXVwvxG7jRs3qkWLFgoODtb+/fv11FNPqWDBgpo1a5YOHDigyZMn50xPAQDIBvFJKRo8a7NmrD1s2taO17c7hCnAj0ksuOGIXf/+/dWzZ0/t2rVL/v7+addbtWqlP//8M7v7BwBAtjl0Kk4PTVhuQp21hO4/ravpw851CXVwGZn+m7xq1SpNnDjxiuslS5ZUREREdvULAIBs9cfOE+ozdZ3OxCWZ0yM+6FxXTSqF2N0twN5gZ43SRUdHX3F9x44dKly4cHb1CwCAbJGa6tDHv+/W6IU75XBItUsF6+Ou4ebcV0DuPhX7wAMP6LXXXlNSUpJpe3h46ODBgxo4cKA6dOiQE30EACBLouOT9PRXa/TugguhrvOtpTXtX40IdXBZHg5ra1AmWKN1rVu31pYtWxQTE6MSJUqYKdhGjRppzpw5CggIUG5nfY/W5pCzZ88qKCjI7u4AALJgR0SMORpsX1SsfL09NeKBGup0Sxm7uwXkaC7J9FSs9YZLly7Vb7/9ZkqcpKamql69emanLAAAzuCnDUf18vcbdT4pxYzOje9aT2Gl8tvdLcD5RuzcASN2AJA7JaWk6s252/XZ0n2m3bRSiMZ1rms2SwDukEsyvcbu+eef17hx4664/uGHH6pv376ZfTsAALKFVWj40U//Tgt1z9xeUV8+fiuhDm4l08FuxowZatKkyRXXGzdurO+//z67+gUAQIatOXBKbT9YopX7Timfn7cmdA3XgHuryYvzXuFmMr3G7uTJk2Y48HLW0GBUVFR29QsAgAweDXZAI37equRUhyoVyWdCnfUn4I4yPWJXqVIlzZs374rrc+fOVYUKFbKrXwAAXFdcYrL6TVuvYbO3mFBnHQ32Y+8mhDq4Ne+sHCn23HPP6cSJE7rzzjvNtUWLFmn06NEaO3ZsTvQRAIB09kfFmlIm2yNizHTroFbV9ETT8qa2KuDOMh3sHn/8cSUkJGjkyJEaMWKEuVauXDmNHz9e3bt3z4k+AgCQZuHW4+r/3XrFxCcrJJ+fPupSVw0qFLK7W0DuL3dijdrlyZNH+fK51rA35U4AwPmkpDr03sKd+nDxbtMOL1tAHz9aT0WD/O3uGpB7CxRfirNhAQA3w6nYRPWZuk5Ldl3YpNezcTn9p3WoOVECwP9k+v8Rx48fV7du3cxRYt7e3vLy8kr3AAAgO204dEb3fbDUhLo8Pl56/5E6Gn5/DUIdkB0jdj179tTBgwc1dOhQFS9enIWqAIAcYa0UmrLyoF6dvVWJKakqHxJgjgarVowlMkC2BTvrnNglS5aoTp06mf1UAAAy5Hxiiob8sFkz1h427ZbVi+rdjrUV5O9jd9cA1wp2pUuXNr9FAQCQEw6ctEqZrNW2Y9GyDo54+d5q+tdtFZghAjIg0wsUrFp1AwcO1P79+zP7qQAAXNevW4+r7QdLTagLyeerr59soF7NKxLqgJwasevUqZPi4uJUsWJF5c2bVz4+6YfFT506ldm3BAC4OauUyZiFO/TR4j2mXa9Mfn38aLiKBVPKBMjRYMfpEgCA7HTyXIL6TF2vpbspZQLc9GDXo0ePG/6iAABY1h48rd7frNWxs/GmlMmbHWrpgTol7e4WkGtl6dehPXv2aMiQIercubMiIyPNtXnz5mnLli3Z3T8AgAuyNuF9uXy/Ok38y4S6CoUD9ONzTQh1wM0Odn/88Ydq1aqlv//+WzNnztS5c+fM9Y0bN2rYsGE32h8AgIuLTUg2U6/DZm9RUopDrWsV0+znmqpK0UC7uwa4X7CzdsS+/vrrWrhwoXx9fdOu33HHHfrrr7+yu38AABeyO/Kc2n20TLM3HJW3p4eGtq2uj7rUUz6/GzrhEsB/Zfr/SZs2bdKUKVOuem7syZMnM/t2AAA38cvGY3r5+w2KTUxRkUA/ffRoPd1SrqDd3QLcO9jlz59fx44dU/ny5dNdX7dunUqWZG0EACC9xORUvTl3u/5v2T7TblihoD7oXE+FA/3s7hrgcjI9FdulSxcNGDBAERERpmBkamqqli1bphdffFHdu3fPmV4CAHKlY2fP65FJf6WFun81r6Cvn2hAqANyiIcjk+eDJSUlqWfPnpo6darZ1eTt7a2UlBQT+L744gt5eXkpt4uOjlZwcLDOnj2roCAOmwaArPhz5wn1nbZep2ITFejvrTEd6+ju6kXt7hbg0rkk08Hu0pIn1vSrNWJXt25dVa5cWa6CYAcAN3aKxAe/7dL7i3bJ+hemZskgfdwlXGUK5bW7a4DL55Isb0OyjhSzHgAAXHqKhDVKt2TXhVMkujQoo1faVpe/T+6fzQFygwwFu/79+2f4DceMGXMj/QEA5FJrDpzWc1MunCLh7+OpN9rX0oP1StndLcCtZCjYWVOul1qzZo1ZV1e1alXT3rlzp1lbFx4enjO9BAA4LWtFz+fL9uuNOduUnOowp0iMfzRcVYtRcBhwymC3ePHidCNygYGB+vLLL1WgQAFz7fTp03rsscfUrFmznOspAMDpxMQnacCMjZqzKcK024QV11sdwig4DNgk05snrFp1CxYsUI0aNdJd37x5s1q2bKmjR48qt2PzBAD8s23HovXsN2u1LypWPl4eGtKmuro3KmtKYQHIJZsnrDc/fvz4FcEuMjJSMTExme8tACDXmb76kIb8sFkJyakqEexvTpGoW+bCLA4A+2Q62LVv395Mu44ePVoNGzY011asWKGXXnpJDz74YE70EQDgJOKTUjR89hZNXXXItJtXKayxneqoQMD/zg4HkIuC3YQJE8wpE127djXFis2beHvriSee0DvvvJMTfQQAOIEDJ2P1zNdrtfVYtKzZ1v4tqqj3HZXk6cnUK+AsslygODY21hQptj69UqVKCggIkKtgjR0ApDdvc4Remr5BMQnJKhTgq/cfqaumlUPs7hbgFqJvRoFiK8iFhYVl9dMBALlAUkqq3p63XZ8suXDWa/2yBfRhl3oqFuxvd9cAZEews0bq3nzzTS1atMhsmLCOFLvU3r17M/uWAAAnFHE23hQcXn3gtGk/1ay8Xr63mny8PO3uGoDsCnZPPvmk/vjjD3Xr1k3FixdnWzsAuKBlu6P0/LfrdDI2UYF+3nrn4TDdW7O43d0CkN3Bbu7cufrll1/UpEmTzH4qAMDJpaY69NHi3Rrz605ZK7BDiwdp/KP1VC7EddZRA64s08HOOm2iYMGCOdMbAIBtTscmqt936/X7jhOm3al+ab36QA35+3jZ3TUAGZTphRIjRozQK6+8ori4uMx+KgDASa07eFptxi0xoc7P21NvPxSmtx4KI9QBrj5iZxUmtsqcFC1aVOXKlZOPj0+659euXZud/QMA5CCrZNWXy/dr5JxtSkpxqFyhvPr40XBVL0GpJ8Atgl27du1ypicAgJsqOj5JA2ds1JxNEabdqmYxM0oX5J/+F3YAblCg2JVRoBiAq9ty9Kx6f7NW+0/GydvTQ/9pHarHmpSj0gHgjgWKz5w5o++//95MyVpnxFqbKawpWGt6tmTJklntNwAgh1m/y1vnvA6bvUWJyakqmT+PPuhSV/XKFLC7awCyQaaD3caNG9WiRQuTHPfv36+nnnrKBLtZs2bpwIEDmjx5cnb0CwCQzWITkjXkh82ate6Iad9ZrYhGP1xbBQJ87e4aALt2xfbv3189e/bUrl275O//vyNlWrVqpT///DO7+gUAyEY7j8fogY+WmVDn5emhga2q6dPu9Ql1gLuP2K1atUoTJ0684ro1BRsRcWEBLgDAecxYc9iM1J1PSlHRID990Lmebi1PPVLAFWU62FmjdNYivsvt2LFDhQsXzq5+AQBuUHxSiob9uEXTVh8y7aaVQjT2kToKyednd9cAOMtU7AMPPKDXXntNSUlJpm3toDp48KAGDhyoDh06ZLoDH3/8scqXL28CY3h4uJYsWXLN186cOVN33323CZDWrpBGjRpp/vz5V7xuxowZql69uvz8/Myf1vo/AHAne06cU7uPlplQZ2107deiir58/FZCHeDiMh3s3n33XZ04cUJFihTR+fPn1bx5c1WqVEmBgYEaOXJkpt5r2rRp6tu3rwYPHqx169apWbNmZq2eFRSvxlrDZwW7OXPmaM2aNbrjjjt03333mc+96K+//lKnTp3UrVs3bdiwwfzZsWNH/f3335n9VgEgV/px/RHd98FSbY+IUUg+X339RAP1aVHZrK0D4NqyXMfut99+MyVOUlNTVa9ePbNTNrMaNGhgPnf8+PFp10JDQ00R5FGjRmXoPWrUqGGCnHXMmcX62Joqnjt3btpr7r33XnPG7bfffpuh96SOHYDcOvX66k9b9e3KC78cN6xQUOMeqasiQf/b6AYg98nxOnaWO++80zyyKjEx0Yy6WVO4l2rZsqWWL1+eofewQmVMTIwpt3LpiF2/fv3Sve6ee+7R2LFjs9xXAHB2+6Ji9ew3a7XtWLSZev33HZXUp0UVRukAN5OlYLdo0SK999572rZtm1ljV61aNTOlmplRu6ioKKWkpJiixpey2hndXWudWxsbG2umWi+yPjez75mQkGAeF11tcwgAOKufNx7VwBmbdC4hWQUDfDW2Ux3dVoXNbIA7yvQauw8//NBMbVpr6vr06aPnn3/eDAu2bt3aPJdZlx9fY80MZ+RIG2tadfjw4WadnrXe70be05r2tYY4Lz5Kly6d6e8DAOyYeh36w2Y9N2WdCXW3liuoOc83I9QBbizTI3ZWCLJG65577rm0a1a4a9Kkidk8cen16wkJCZGXl9cVI2mRkZFXjLhdzgpzTzzxhKZPn37FKGGxYsUy/Z6DBg0yhZcvHbEj3AFwZvujYvXct2u1+ciFGYZnbq+oF+6uIm+vTP++DsCFZPongBV6rBG7y1lr4zIzhenr62vKmyxcuDDddavduHHj647UWSdfTJkyRW3atLnieasEyuXvuWDBguu+p1UWxRp1vPQBAM7ql43H1PaDpSbUFcjro88fu0UD7q1GqAOQ+RG7+++/39SFe+mll9Jd//HHH03pkcywRsmsciT169c3gWzSpEmm1EmvXr3SRtKOHDmSdv6sFeq6d++u999/Xw0bNkwbmcuTJ4+ZQrVY08O33Xab3nrrLVNzz+rXr7/+qqVLl2b2WwUAp5t6HfnLNn214oBp1y9bQB90qaviwXns7hqA3BrsrHIk1pTr77//bsKYZcWKFVq2bJleeOEFjRs3Lt0U7fVYpUlOnjxpCh4fO3ZMNWvWNDXqypYta563rl1a0846yiw5OVm9e/c2j4t69OihL774wnxsjcxNnTpVQ4YM0dChQ1WxYkUzdWuVVgGA3Dz12nvKWm05emFm5NnbK6o/U68AbrSOnXVKREZYmxX27t2r3Ig6dgCcyU8bjmrQzP/teh3TsbZur5p+0xgA15Wjdez27dt3I30DAGRi6vX1X7bq6xUXZi6sXa/jOtdVsWAKDgPI5gLFVoFhK+RZU53e3ll+GwDANQoOP3fJ1GvvOyqa816ZegVwPZn+CREXF2dKjeTNm9cc53VxDZy1nu7NN9/M7NsBAC7zw7ojajtuiQl11tTrl4/fqpfuYdcrgH+W6Z8S1k7VDRs2mM0T/v7/mw6w6slZmxQAAFkTl5isl6ZvUN9p6xWbmGLOerUKDjen4DCADMr0HOoPP/xgApxVbuTS0xyqV6+uPXv2ZPbtAACSdkTEmF2vuyPPyTre9fm7Kuvfd1bmrFcAORvsTpw4ccURXhbrzNaMHAUGAPgfqzDB1FWHNHz2FiUkp6pIoJ/ef6SuGlUsZHfXALjDVOwtt9yiX375Ja19Mcx98sknaXXtAAD/LCY+Sf/+dp0pZWKFuturFtbcPs0IdQBu7lmx1pFiW7duNcWCrVMgtmzZor/++kt//PFH1nsCAG5k4+Ezem7KOh08FSdvTw+9dE9VPdWsgjyZegVwM0fsrJMdrFMmrN2xVqkT6xzWokWLmmBnnf0KALj+1OunS/aqw/jlJtSVzJ9H3/VqpH81r0ioA3DzT55wB5w8ASAnnI5N1Evfb9Cv2yJN+94axfRWhzAF5/Wxu2sA3OnkCesNM4ogBABXWrX/lJ7/dp2OnY2Xr5enhrYNVdeGZdl0BiBbZSjY5c+fP8M/fFJSUm60TwDgMlJSHRr/+2699+su83GFkAB90KWuapQItrtrANw12C1evDjt4/3792vgwIHq2bNn2i5Ya33dl19+aTZWAAAuiIyJV/9pG7R0d5Rpt69bUiPa1VQ+P45hBOAka+zuuusuPfnkk+rcuXO661OmTNGkSZPMiRS5HWvsANyoJbtOqN+09Yo6l6g8Pl567YEaeii8FFOvAHI0l2R6V6w1Ole/fv0rrlvXVq5cmdm3AwCXkpySqrfnbVf3/1tpQl21YoH66d9N9HD90oQ6ADku08GudOnSmjBhwhXXJ06caJ4DAHd15Mx5dZq0Qh//vkfWXMijDcroh95NVKlIoN1dA+AmMr3Q47333lOHDh00f/58c16sZcWKFeac2BkzZuREHwHA6S3celwvTt+gs+eTFOjnrTc7hKlNWHG7uwXAzWSpjt3hw4c1fvx4bdu2zRTbrF69unr16uUyI3assQOQUQnJKXpz7nZ9vmy/aYeVCtaHneupTKG8dncNgIvITC6hQPFVEOwAZMT+qFg99+1abT5yodbnk03L6+V7q8nXO9OrXADg5hUoBgCkN3vDUf1n5iadS0hW/rw+Gv1wbd0VWtTubgFwcwQ7AMiE84kpevWnLZq66pBp31quoN7vXEfFg/PY3TUAINgBQEbtOh6j3lPWaufxc7Iqlzx3RyX1uauyvL2YegXgHAh2APAPrKXI360+pGGztyg+KVUh+fz0/iN11KRSiN1dA4B0CHYAcB0x8UkaPGuzWVNnaVY5RGM61lHhQD+7uwYAWQt2devWzXDF9LVr12bodQDg7DYfOavnpqzV/pNx8vL00Astq6jXbRXl6ckJEgBycbBr165dzvcEAJxo6vWL5fs1as52JaakqmT+PBrXuY7Cyxa0u2sAcF3UsbsK6tgB7utUbKJemr5Bi7ZHmvbd1YvqnYfClD+vr91dA+CmoqljBwCZt3xPlPpNW6/j0QmmyPDg1qHq3qhshpeiAIDdMh3sUlJSzHmx3333nQ4ePKjExMR0z586dSo7+wcAOS45JVXvL9qlDxfvljWHUaFwgDkWrHoJRuwB5C6ZLr706quvasyYMerYsaMZEuzfv78efPBBeXp6avjw4TnTSwDIIUfOnNcjk1bog98uhLqO9Uvp5383JdQBcI81dhUrVtS4cePUpk0bBQYGav369WnXVqxYoSlTpii3Y40d4B7mbjqmATM2Kjo+Wfn8vPXGg7V0f+0SdncLAG7eGruIiAjVqlXLfJwvXz7zRSxt27bV0KFDM/t2AGDLsWAjftmqKX8fNO3apfPrg0fqqkyhvHZ3DQBu7lRsqVKldOzYMfNxpUqVtGDBAvPxqlWr5OdHwU4Azm1HRIwe+GhpWqjr1byivu/ViFAHwCVkesSuffv2WrRokRo0aKA+ffqoc+fO+uyzz8xGin79+uVMLwHgBlmrTr7++6Be/3mrEpJTzckRYzrWVrPKhe3uGgA4Tx07a13d8uXLzejd/fffL1fAGjvAtZyOTTRr6RZsPW7azasU1uiOtc2ZrwDg7G5qHbuGDRuaBwA4oxV7T6rv1PWKiI6Xj5eHBtxbTY83Kc+xYABcUoaC3ezZs9WqVSv5+PiYj6/HVUbtAOT+2nTjFu3SBxdr04UEaFznuqpZMtjurgGAvVOxVo06azdskSJFzMfXfDMPD1PAOLdjKhbI3Q6dilPfaeu15sBp0344vJSG319DAX4ctgMg98n2qdjU1NSrfgwAzubnjUc1aOYmxcQnK/C/tenuozYdADeR6XInkydPVkJCwhXXraPFrOcAwA5xicka8P1GPTdlnQl1dcvk15w+zQh1ANxKpnfFenl5mTp21rTspU6ePGmuMRUL4GbbfOSsnp+6TntPxMrDQ+p9eyX1aVFZPl6Z/t0VANxrV6yVA621dJc7fPiw+aIAcLOkpjr0f8v26e15O5SYkqqiQX56r1MdNa4YYnfXAMAWGQ52devWNYHOetx1113y9v7fp1qjdPv27dO9996bU/0EgHQiY+L14vSN+nPnCdNuWb2o3uoQpgIBvnZ3DQCcP9i1a9fO/Ll+/Xrdc8895pzYi3x9fVWuXDl16NAhZ3oJAJdYvCNSL03foKhzifLz9tTQttX1aIMyV51NAAB3kuFgN2zYMDMyV7ZsWRPsihcvnrM9A4DLJCSn6K25O8z0q6VasUB90LmuKhcNtLtrAOAUvDO7caJXr17atm1bzvUIAK5i1/EYPT91vbYdizbtno3LaWCravL38bK7awDgNDK9eaJWrVrau3evypcvnzM9AoDLNmx98/dBjfh5qxKSU1UowFdvPxSmu0KL2t01AMj9wW7kyJF68cUXNWLECIWHhysgICDd85QHAZBdTsUmasCMjVq49bhp31alsN59OExFAv3t7hoAuEYdu0uPFLt0ofLFMijUsQOQHZbtjlK/aesVGZMgXy9PDWhVTY81LidPTzZIAHAv0TlZx27x4sU30jcAuK7E5FSNXrhDk/7cK+vXzoqFAzSuc13VKEGdTAD4J5kOds2bN8/spwBAhuw9cU59pq7XpiNnTbtLgzIa2qa68viyQQIAciTYXRQXF6eDBw+aM2IvFRYWltW3BOCmrKUc360+pOGzt+p8Uory5/UxxYbvqVHM7q4BgGsHuxMnTuixxx7T3Llzr/q8K6yxA3DznIlL1KCZmzR3c4RpN65YSGM61lGxYDZIAEBmZfqE7L59++r06dNasWKF8uTJo3nz5unLL79U5cqVNXv27Ex3AID7Wr47SveOXWJCnbenhwa1qqavn2hAqAOAmzVi99tvv+nHH3/ULbfcYnbIWidR3H333WaXxqhRo9SmTZus9gWAm26QqBASoPcfqatapdggAQA3NdjFxsaqSJEi5uOCBQuaqdkqVaqYwsVr1669oc4AcH17zAaJddp85MIJEp1vLaOhbUOV1zfLS34BAP+V6Z+kVatW1Y4dO1SuXDnVqVNHEydONB9PmDCB82MBXHeDxLcrD+m1n7coPinVbJB488Ew3VuTDRIAYFuws9bYHT161Hw8bNgw3XPPPfrmm2/k6+urL774Its6BsC1TpAYOGOjFvz3BImmlUI0umNtFQ1iLR0A2HryxNXKnmzfvl1lypRRSEiIXAEnTwDZZ8muE3rhuw3mBAkfLw+9fE81PdG0PCdIAEAO5BLPzAS43r17q2TJkmaNXZcuXRQVFaW8efOqXr16LhPqAGSPhOQUvf7zVnX7bKUJdZWK5NMPvZvoqdsqEOoAwO6pWGva1ZpqffTRR+Xv769vv/1WzzzzjKZPn55TfQOQS+08HqPnv12n7RExpt21YRkNbs0JEgDgNMFu5syZ+uyzz/TII4+YdteuXdWkSRNTkNjLix/WAC5skPhy+X69MXe7KWlSKMBXbz8UprtCi9rdNQBwCxkOdocOHVKzZs3S2rfeequ8vb3NRorSpUvnVP8A5BKRMfF6afpG/bHzhGnfXrWw3nmotgoH+tndNQBwGxkOdtbInLXzNd0ne3srOTk5J/oFIBdZuPW4BszYaHa/+nl76j+tQ9W9UVl5eLCWDgCcMthZUyw9e/aUn9//fvuOj49Xr169FBAQkG7KFoB7iEtM1uu/bNOUvw+adrVigRrXua6qFA20u2sA4JYyHOx69OhxxTVrnR0A97Tx8Bn1nbpee6NiTfupZuX14j1V5efNmlsAcPpg9/nnn+dsTwDkCimpDo3/fbfG/rpLyakOFQvy17sP11bTypQ8AgC7cTgjgAw7dCpO/aat1+oDp027Ta3iGtm+pvLnTb/+FgBgD4IdgAytsZ259oiGzd6icwnJyufnrVfvr6EH65VkgwQAOBGCHYDrOhOXqMGzNuuXTcdMu37ZAnqvUx2VLpjX7q4BAC5DsANwTct2R5lzXiOi4+Xt6aF+d1dRr+YV5cWRYADglAh2AK4Qn5Sid+fv0KdL95l2hZAAjX2kjsJK5be7awCA6yDYAUhne0S0KWNy8ZzXRxuU0eA2ocrry48LAHB2/KQGYKSmOvTZ0n16Z/4OJaZwzisA5Eaednfg448/Vvny5eXv76/w8HAtWbLkmq89duyYunTpoqpVq8rT01N9+/a96uvGjh1rXpMnTx5zjm2/fv3MKRkAru7w6Th1+XSFRs7ZZkJdi9Aimt/vNkIdAOQytga7adOmmXA2ePBgrVu3Ts2aNVOrVq108OCF44kul5CQoMKFC5vX165d+6qv+eabbzRw4EANGzZM27Zt02effWa+zqBBg3L4uwFyZxmTWesOq9XYJVqx95Ty+nrpzQdr6ZPu9RWS73/HBwIAcgcPh/WT3SYNGjRQvXr1NH78+LRroaGhateunUaNGnXdz7399ttVp04dMzp3qeeee84EukWLFqVde+GFF7Ry5crrjgZeKjo6WsHBwTp79qyCgoIy/X0BubGMSd0y+fVexzoqF/K/s58BAPbLTC6xbcQuMTFRa9asUcuWLdNdt9rLly/P8vs2bdrUvK8V5Cx79+7VnDlz1KZNmxvuM+Aqluw6oXvG/mlCnVXG5IW7q2j6vxoR6gAgl7Nt80RUVJRSUlJUtGj6NTxWOyIiIsvv+8gjj+jEiRMm4FmDkcnJyXrmmWfM9Oy1WFO81uPSZAy4ahmTN+du1xfL95t2hcIBGtuJMiYA4Cps3zxx+XFEVhi7kSOKfv/9d40cOdJsyli7dq1mzpypn3/+WSNGjLjm51jTvtYQ58WHteECcDWbj5xVm3FL0kJdj0Zl9cu/mxHqAMCF2DZiFxISIi8vrytG5yIjI68YxcuMoUOHqlu3bnryySdNu1atWoqNjdXTTz9tNl1Yu2kvZ22s6N+/f7oRO8IdXEVKqkMT/tij9xbuVHKqQ0UC/fTOw7XVvEphu7sGAHCVYOfr62vKmyxcuFDt27dPu261H3jggSy/b1xc3BXhzQqQ1kjgtfaJ+Pn5mQfgag6ejFO/79ZrzYHTpt26VjGNbFdLBQJ87e4aAMDVChRbo2TW6Fr9+vXVqFEjTZo0yZQ66dWrV9pI2pEjRzR58uS0z1m/fr3589y5c2YtndW2QmL16tXN9fvuu09jxoxR3bp1za7b3bt3m1G8+++/3wQ8wB1Yv8RMX31Yr/60RbGJKQr089arD9RQ+7olb2ipAwDAudka7Dp16qSTJ0/qtddeM8WHa9asaXawli1b1jxvXbu8pp0V2C6ydr9OmTLFvH7//gvrhoYMGWL+4bL+tEKhVffOCnvWujvAHUSdS9CgmZu0cOtx0761fEGN6VhbpQrktbtrAABXrmPnrKhjh9xq0bbjGjBjo6LOJcrHy0MvtqyqJ5tVkJcno3QA4A65hLNiARdwLiFZr/+8VVNXHTLtqkUD9V6nOqpegl9MAMCdEOyAXG71/lPq/90GHTwVJ2v53JNNy+uFllXl78OaUgBwNwQ7IJdKTE7Ve7/u1MQ/9ijVIZXMn0ejO9ZWwwqF7O4aAMAmBDsgF9oeEa1+0zZo27ELp6Q8FF5Kw+6rrkB/H7u7BgCwEcEOyGXFhj9dslejF+xUYkqqCgb46o32tXRvzWJ2dw0A4AQIdkAuKjb8wvT1WrX/QrHhFqFFNOrBMBUOpLg2AOACgh3g5KyKRNNWHdKIn7eaYsMBvl4adl8NPVy/FMWGAQDpEOwAJxYZE69BMzZp0fZI0761XEGzQaJ0QYoNAwCuRLADnNTPG49qyA+bdSYuSb5ennrxnip6oinFhgEA10awA5zMmbhEDf1xi37acNS0qxcP0phOtVWtGMWGAQDXR7ADnMji7ZHmSLDImAQzMvfs7RX17zsry9fb0+6uAQByAYId4CRHgo38Zau+XXnhSLAKhQM0pmMd1Smd3+6uAQByEYIdYLO/957Ui99v0KFT50378Sbl9fK9HAkGAMg8gh1gk/ikFL0zf4f+b9k+Of57JNi7D9dWo4ocCQYAyBqCHWCDjYfPqP93G7Q78pxpd6pfWkPahnIkGADghhDsgJsoKSVVH/y2Wx8t3m2OB7NOjXirQy3dWa2o3V0DALgAgh1wk+yIiFH/79Zry9Fo024bVlwjHqipAgG+dncNAOAiCHZADrNG5j5dslejF+xUYkqq8uf1MYHuvtol7O4aAMDFEOyAHHTgZKxenL5Bq/afNu07qhbWWx3CVCTI3+6uAQBcEMEOyAEOh0Pf/H1Qb8zZprjEFAX4emlo2+rqdEtpeXhwJBgAIGcQ7IBsFnE2Xi/P2Kg/d54w7QblC5oyJqUL5rW7awAAF0ewA7JxlO6H9Uc07Mctio5Plp+3p16+t5oea1xOnp6M0gEAch7BDsgGUecSNGTWZs3bEmHatUsFa3THOqpUJJ/dXQMAuBGCHXCD5mw6piE/bNap2ER5e3qoz12V9cztFeXt5Wl31wAAboZgB2TR6dhEvTJ7i37acNS0qxUL1OiOtVWjRLDdXQMAuCmCHZAFC7ce16CZm8wUrJenh55pXlHP31VZvt6M0gEA7EOwAzLh7PkkvfrTFs1ce8S0rTV0ox+urdql89vdNQAACHZARv2+I1IDZ2xSRHS8rFJ0TzeroH53V5G/j5fdXQMAwCDYAf8gJj5JI3/ZpqmrDpl2+ZAAvftwmMLLFrS7awAApEOwA65j+e4ovfT9Rh05c960H2tSTi/fU015fBmlAwA4H4IdcBWxCcl6a952Tf7rgGmXLphH7zxUWw0rFLK7awAAXBPBDrjMyn2n9OL0DTp4Ks60uzYso0GtQhXgx/9dAADOjX+pgP86n5iid+bv0OfL98nhkEoE++vth2qraeUQu7sGAECGEOwASav3nzJr6fZFxZp2p/qlNbhtqIL8fezuGgAAGUawg1uLT0rRu/N36LNlF0bpigX5680OtXR71SJ2dw0AgEwj2MFtrTlwSi9N36i9/x2leyi8lIa2ra7gPIzSAQByJ4Id5O6jdEWD/DTqwVq6s1pRu7sGAMANIdjBrUfpHqxXUsPa1lBwXkbpAAC5H8EOboFROgCAOyDYwe1G6TrUK6VXrLV0jNIBAFwMwQ4ui1E6AIC7IdjBfXa8tmGUDgDg2gh2cCmM0gEA3BnBDi5jxd6TGjRzU9rpEYzSAQDcDcEOuV50fJJGzdmub1ceNG1G6QAA7opgh1xtwZYIDf1xs45HJ5h251vLaFDrapzxCgBwSwQ75EonYhI0fPYW/bLpmGmXDwkwo3QNKxSyu2sAANiGYIdcxeFwaPqawxr5yzadPZ8kL08PPX1bBfW5q7L8fbzs7h4AALYi2CHXOHgyTv+ZtUlLd0eZds2SQXrzwTDVLBlsd9cAAHAKBDs4vZRUhz5ftk+jF+zU+aQU+Xl7qt/dVfRk0/Ly9vK0u3sAADgNgh2c2rZj0Ro4Y6M2HD5r2o0qFDJr6cqFBNjdNQAAnA7BDk5baPjD33Zrwh97lJzqUKC/twa3DlWnW0rLw8PD7u4BAOCUCHZwOqv2nzKjdHtOXCg0fE+NonrtgZoqGuRvd9cAAHBqBDs4jZj4JL09b4e+WnHAtAsH+um1+2uoVa3idncNAIBcgWAHp/Db9uMaPGuzjp2NN+1O9UvrP61DOQ4MAIBMINjBVlHnEvTqT1v104ajpl2mYF69+WAtNa4UYnfXAADIdQh2sK3Q8Kx1R/Taz1t1Ji5Jnh7Sk80qqF+LKsrjS6FhAACygmCHm+7waavQ8Gb9ufOEaYcWD9LbHcJUqxSFhgEAuBEEO9zUQsNfLt+vdxfsUFxiiny9Pc1RYNaRYD4UGgYA4IYR7HBT7Dweo5e/36j1h86Y9q3lCmpUh1qqWDif3V0DAMBlEOyQoxKSU/Tx4j36+PfdSkpxKNDPWwNbV1PnW8rI01pYBwAAsg3BDjlmzYHTptDwrshzpt0itKheb1dTxYIpNAwAQE4g2CHbxSYk6535O/TlX/vlcEgh+Xw1/P4aalOrOMeBAQCQgwh2yFa/74g0hYaPnDlv2g+FlzJnvBYI8LW7awAAuDyCHbLFqdhEvfbTFv2w/kKh4VIF8mjUg7XUrHJhu7sGAIDbINjhhgsNz95w1JweYYU7az/EY03K64WWVZTXl79eAADcTPzLiyyzpluHzNqkxTsuFBquWjRQbz0Upjql89vdNQAA3BLBDpmWmurQVysO6O152xVrFRr28tRzd1ZSr+YVTdFhAABgD4IdMmV3ZIwGzNhkSplY6pctoDc71FKlIoF2dw0AALdHsEOGJCanasIfe/Thb7uVmJKqAF8vDWhVTV0blKXQMAAAToJgh39kHQM24PuN2nE8xrTvqFpYr7evpZL589jdNQAAcAmCHa4pLjFZoxfs1OfL9inVIRUM8NWw+6rr/tolKDQMAIATItjhqv7ceUL/mbVJh09fKDTcvm5JDW1b3YQ7AADgnAh2SOdMXKJG/LxNM9YeNm1ruvX19jV1R9UidncNAAD8A4Id0goN/7LpmIbP3qKoc4myZlp7NCqnF++pqnx+/DUBACA3sL3o2Mcff6zy5cvL399f4eHhWrJkyTVfe+zYMXXp0kVVq1aVp6en+vbte9XXnTlzRr1791bx4sXN+4aGhmrOnDk5+F3kbsfOntdTk1fruSnrTKirVCSfvu/VWMPvr0GoAwAgF7H1X+1p06aZcGaFuyZNmmjixIlq1aqVtm7dqjJlylzx+oSEBBUuXFiDBw/We++9d9X3TExM1N13360iRYro+++/V6lSpXTo0CEFBlJn7WqFhqesPKg3527XuYRk+Xh56NnbK+nZOyrKz9vL7u4BAIBM8nBYc3A2adCggerVq6fx48enXbNG19q1a6dRo0Zd93Nvv/121alTR2PHjk13fcKECXrnnXe0fft2+fj4ZKlf0dHRCg4O1tmzZxUUFCRXtOfEOQ2auUkr950ybesYsLcfClOVogRgAACcSWZyiW1TsdbI2po1a9SyZct016328uXLs/y+s2fPVqNGjcxUbNGiRVWzZk298cYbSklJuebnWCOB1k279OGqklJS9dHi3Wr1/hIT6vL6epkSJjOeaUyoAwAgl7NtKjYqKsqELSt8XcpqR0REZPl99+7dq99++02PPvqoWVe3a9cuE/KSk5P1yiuvXPVzrNHBV199Va5u4+Ez5jiwbccuBNfbqhTWyHY1VbpgXru7BgAAsoHtK+MvL3RrzQzfSPHb1NRUs75u0qRJ8vLyMhsyjh49aqZnrxXsBg0apP79+6e1rRG70qVLy1WcT0zRe7/u1KdL9ppCw/nz+uiVttVNbToKDQMA4DpsC3YhISEmeF0+OhcZGXnFKF5mWDthrbV11ntfum7P+jrW9K+v75UFdv38/MzDFS3bHWXW0h08FWfa1qkRr9xXXSH5XPP7BQDAndm2xs4KWNZo2sKFC9Ndt9qNGzfO8vtau2t3795tRu4u2rlzpwl8Vwt1rupsXJJe/n6DHv30bxPqigf767Me9TWuc11CHQAALsrWqVhr+rNbt26qX7++2fBgTZ8ePHhQvXr1SpsiPXLkiCZPnpz2OevXrzd/njt3TidOnDBtK7BVr17dXH/mmWf0wQcfqE+fPvr3v/9t1thZmyeef/55uYu5m47pldlbdCImwbS7NSyrl++tqkD/rO0SBgAAuYOtwa5Tp046efKkXnvtNVN82NrBam14KFu2rHneumYFvUvVrVs37WNrV+2UKVPM6/fv32+uWWvjFixYoH79+iksLEwlS5Y0IW/AgAFydcej4/XKj5s1f8tx065QOEBvdQjTLeUK2t01AADg6nXsnFVuq2Nn/SecuuqQ3pizTTHxyfL29FCv5hX13J2V5O9DoWEAANwll9i+KxY3Zn9UrAbO3KgVey8UGq5dKlhvdghTaHHnD6QAACB7EexyqeSUVH26dJ/eW7hTCcmp8vfx1Istq+qxJuXl5UkJEwAA3BHBLhfafOSsBszYqC1HLxQablopRG+0r6UyhSg0DACAOyPY5SLxSSka++sufbJkr1JSHQrO46MhbUL1UHgpCg0DAACCXW6xYu9JU2h4X1SsabepVVzD7q+uIoH+dncNAAA4CYKdkzt7Pklvzt2ub1deKPtSNMhPIx6oqZY1itndNQAA4GQIdk5s/pYIDf1hsyL/W2i4861lNKh1NQVRaBgAAFwFwc4JRcbEa/jsLZqz6cI5uuVDAjTqwVpqWKGQ3V0DAABOjGDnZIWGp685rJG/bDNTsFbZkqdvq6A+d1Wm0DAAAPhHBDsncfBknAbN2qhlu0+ads2SQXrzwTDVLBlsd9cAAEAuQbBzgkLDny/br9ELdyg+KVV+3p7qd3cVPdm0vLy9PO3uHgAAyEVIDjbadixaD45frpFztplQ16hCIc3ve5s555VQl/NiY2NN/T/rYX3szOzu6834+jfze7T7frrC9+EM99AZ+uBuuOfOjxE7GyQmp+r9RTs18Y+9Sk51KNDfW4Nbh6rTLaUpNAwAALKMYGcD6yjXP3dGmVB3T42ieu2BmioaRKFhAABwYwh2NrCmWd9+KEz7o2LVqlZxu7sDAABcBMHOJqHFg8wDAAAgu7BCHwAAwEUQ7AAAAFwEwQ4AAMBFEOwAAABcBMEOAADARRDsAAAAXATBDgAAwEUQ7AAAAFwEwQ4AAMBFEOwAAABcBMEOAADARRDsAAAAXATBDgAAwEUQ7AAAAFwEwQ4AAMBFeNvdAWfkcDjMn9HR0XZ3BTkoNjY27WPrv3VKSoqcld19vRlf/2Z+j3bfT1f4PpzhHjpDH9wN99weF/PIxXxyPR6OjLzKzRw+fFilS5e2uxsAAABpDh06pFKlSul6CHZXkZqaqqNHjyowMFAeHh43lLCtgGj9hwgKCsrWPuJK3O+bi/t983HPby7u983F/b42K6rFxMSoRIkS8vS8/io6pmKvwrpp/5SIM8P6C8pf0puH+31zcb9vPu75zcX9vrm431cXHBysjGDzBAAAgIsg2AEAALgIgl0O8vPz07Bhw8yfyHnc75uL+33zcc9vLu73zcX9zh5sngAAAHARjNgBAAC4CIIdAACAiyDYAQAAuAiCXTYbNWqUKWrct2/ftGvWMsbhw4ebwoJ58uTR7bffri1bttjaz9zKuo/W/b30UaxYsbTnudfZ78iRI+ratasKFSqkvHnzqk6dOlqzZk3a89zz7FWuXLkr/o5bj969e5vnud/ZKzk5WUOGDFH58uXN/axQoYJee+01U6j+Iu559rIK7Vr/RpYtW9bcz8aNG2vVqlVpz3O/b5C1eQLZY+XKlY5y5co5wsLCHH369Em7/uabbzoCAwMdM2bMcGzatMnRqVMnR/HixR3R0dG29jc3GjZsmKNGjRqOY8eOpT0iIyPTnudeZ69Tp045ypYt6+jZs6fj77//duzbt8/x66+/Onbv3p32Gu559rL+Pl/693vhwoXWBjfH4sWLzfPc7+z1+uuvOwoVKuT4+eefzd/v6dOnO/Lly+cYO3Zs2mu459mrY8eOjurVqzv++OMPx65du8zP9aCgIMfhw4fN89zvG0OwyyYxMTGOypUrmx/CzZs3Twt2qampjmLFipm/qBfFx8c7goODHRMmTLCxx7mT9QOgdu3aV32Oe539BgwY4GjatOk1n+ee5zzrZ0nFihXNveZ+Z782bdo4Hn/88XTXHnzwQUfXrl3Nx9zz7BUXF+fw8vIyQfpS1s/1wYMHc7+zAVOx2cSaJmnTpo1atGiR7vq+ffsUERGhli1bpl2zavQ0b95cy5cvt6Gnud+uXbvMEL01dfLII49o79695jr3OvvNnj1b9evX18MPP6wiRYqobt26+uSTT9Ke557nrMTERH399dd6/PHHzXQs9zv7NW3aVIsWLdLOnTtNe8OGDVq6dKlat25t2tzz7J/6TklJkb+/f7rr1pSrdd+53zeOYJcNpk6dqrVr15r1dZez/oJaihYtmu661b74HDKuQYMGmjx5subPn28ChnUPrfUZJ0+e5F7nACs0jx8/XpUrVzb3vFevXnr++efNfwML9zxn/fDDDzpz5ox69uxp2tzv7DdgwAB17txZ1apVk4+Pj/nlxVr/ZV2zcM+zV2BgoBo1aqQRI0bo6NGjJuRZv7z8/fffOnbsGPc7G3hnx5u4s0OHDqlPnz5asGDBFb+BXMr6bftS1jT45dfwz1q1apX2ca1atcwPiIoVK+rLL79Uw4YNzXXudfaxFpBbI3ZvvPGGaVv/6FmLmK2w171797TXcc9zxmeffWb+zlsj1JfifmefadOmmWAxZcoU1ahRQ+vXrzfBzrrnPXr0SHsd9zz7fPXVV2YUumTJkvLy8lK9evXUpUsXM0ByEfc76xixu0HW7sDIyEiFh4fL29vbPP744w+NGzfOfHzxt47Lf9OwPufy30iQeQEBASbgWdOzF3fHcq+zT/HixVW9evV010JDQ3Xw4EHzMfc85xw4cEC//vqrnnzyybRr3O/s99JLL2ngwIFmWYf1s6Rbt27q169f2gwM9zz7Wb+MW/9Onjt3zgyOrFy5UklJSWZ5Dff7xhHsbtBdd92lTZs2md/yLj6sEY5HH33UfGxtnbf+oi5cuDDduhnrL7U1hYgbk5CQoG3btpkAcvGHAvc6+zRp0kQ7duxId81ai2SVKbBwz3PO559/btY1Wmt3L+J+Z7+4uDh5eqb/p9AaRbpY7oR7nrO/mFs/u0+fPm2WejzwwAPc7+yQHTswkN6lu2It1u4ea0fPzJkzzdbtzp07s3U7i1544QXH77//7ti7d69jxYoVjrZt25pt8fv37zfPc6+zv4SPt7e3Y+TIkaYswTfffOPImzev4+uvv057Dfc8+6WkpDjKlCljdiVfjvudvXr06OEoWbJkWrkT676GhIQ4Xn755bTXcM+z17x58xxz5841P8cXLFhgdsTeeuutjsTERPM89/vGEOxuQrCztm9bZTqsLdx+fn6O2267zfxlReZdrGfk4+PjKFGihClLsGXLlrTnudfZ76effnLUrFnT3M9q1ao5Jk2alO557nn2mz9/vqldt2PHjiue435nLyssWD+vrSDt7+/vqFChgim7kZCQkPYa7nn2mjZtmrnPvr6+5p727t3bcebMmbTnud83xsP6n2wZ+gMAAICtWGMHAADgIgh2AAAALoJgBwAA4CIIdgAAAC6CYAcAAOAiCHYAAAAugmAHAADgIgh2AAAALoJgBwAA4CIIdgAAAC6CYAcALiIlJUWpqal2dwOAjQh2AFzSvHnz1LRpU+XPn1+FChVS27ZttWfPnrTnly9frjp16sjf31/169fXDz/8IA8PD61fvz7tNVu3blXr1q2VL18+FS1aVN26dVNUVNQ/fu3Jkyebr5mQkJDueocOHdS9e/e09k8//aTw8HDThwoVKujVV19VcnJy2vNjxoxRrVq1FBAQoNKlS+vZZ5/VuXPn0p7/4osvzPf3888/q3r16vLz89OBAwf0+++/69ZbbzWfZz3fpEkTcx2A6yPYAXBJsbGx6t+/v1atWqVFixbJ09NT7du3NyNaMTExuu+++0xoWrt2rUaMGKEBAwak+/xjx46pefPmJvytXr3aBMXjx4+rY8eO//i1H374YTN6Nnv27LRrViC0Athjjz1m2vPnz1fXrl31/PPPmwA5ceJEE9RGjhyZ9jlWn8eNG6fNmzfryy+/1G+//aaXX3453deKi4vTqFGj9Omnn2rLli0qWLCg2rVrZ/q+ceNG/fXXX3r66adNaAXgBhwA4AYiIyMd1o+8TZs2OcaPH+8oVKiQ4/z582nPf/LJJ+b5devWmfbQoUMdLVu2TPcehw4dMq/ZsWPHP369Z555xtGqVau09tixYx0VKlRwpKammnazZs0cb7zxRrrP+eqrrxzFixe/5nt+9913pt8Xff7556Y/69evT7t28uRJc+3333//xz4CcD3edgdLAMgJ1rTr0KFDtWLFCjNadnHt2cGDB7Vjxw6FhYWZKdCLrKnLS61Zs0aLFy8207BXe+8qVapc9+s/9dRTuuWWW3TkyBGVLFlSn3/+uXr27Jk2cma9vzWaeOkInTXKFx8fb0bh8ubNa77+G2+8YUb0oqOjzTSt9bw1GmlNs1p8fX3N93KRNWJnfZ177rlHd999t1q0aGFGGYsXL57lewkg92AqFoBLsqZaT548qU8++UR///23eVgSExOtmYorpiata5eygqD1Htaau0sfu3bt0m233faPX79u3bqqXbu2WW9nTfdu2rTJBK5L399aU3fpe1uvsd7fCpzWmjhrfV/NmjU1Y8YMEwQ/+ugj87lJSUlp75MnT54rvhcrRFpTsI0bN9a0adNMCLUCLgDXx4gdAJdjBbpt27aZdWvNmjUz15YuXZr2fLVq1fTNN9+YzQ3WhgOLtY7uUvXq1TOBqly5cvL2ztqPyieffFLvvfeeGbWzRs6sDRCXvr81clipUqWrfq7VH2uEbvTo0WatneW7777L8Ne2gqX1GDRokBo1aqQpU6aoYcOGWfo+AOQejNgBcDkFChQwu1InTZqk3bt3m00H1kaKi7p06WJGzKxNBVYAtDYyvPvuu+a5i6NfvXv31qlTp9S5c2etXLlSe/fu1YIFC/T444+bKdOMePTRR02os0YNrc+71CuvvGJG84YPH242PVj9sEbXhgwZYp6vWLGiCXYffPCB+dpfffWVJkyY8I9fc9++fSbMWSN21qif1eedO3cqNDQ0U/cQQC5l9yI/AMgJCxcudISGhjr8/PwcYWFhZjOB9SNv1qxZ5vlly5aZ676+vo7w8HDHlClTzPPbt29Pe4+dO3c62rdv78ifP78jT548jmrVqjn69u2btgEiI7p16+YoWLCgIz4+/orn5s2b52jcuLF576CgIMett97qmDRpUtrzY8aMMZsprOfvuecex+TJk00fT58+nbZ5Ijg4ON17RkREONq1a2c+z/reypYt63jllVccKSkpWbqPAHIXD+t/7A6XAGA3a2rWKkVy9uxZs24tu1gbGKzRMqtsCQDkNNbYAXBL1jSoVRTY2rG6YcMGU8fO2j2aXaHOmsa1pkGtaeAPP/wwW94TAP4JwQ6AW4qIiDDr3Kw/rVIgVlHhS0uPXI9VMsU66eFarPIk1s7Z06dP66233lLVqlWzsecAcG1MxQJAJlmbGvbv33/N529kJy0A3AiCHQAAgIug3AkAAICLINgBAAC4CIIdAACAiyDYAQAAuAiCHQAAgIsg2AEAALgIgh0AAICLINgBAADINfw/lXzcBVvGh2AAAAAASUVORK5CYII=", "text/plain": ["<Figure size 640x480 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Dependencia parcial de una variable\n", "print (\"\\n === Partial Dependence Plot (PDP) ==\")\n", "if \"age_years\" in X.columns:\n", "    feat_for_pdp = \"age_years\"\n", "elif len(num_cols) > 0:\n", "    feat_for_pdp = num_cols[0]\n", "else: \n", "    feat_for_pdp = clf.named_steps[\"prep\"].get_feature_names_out()[0]\n", "\n", "PartialDependenceDisplay.from_estimator(clf, X_test, [feat_for_pdp])\n", "\n", "plt.tight_layout()\n", "plt.savefig(\"T7_2_PDP_age_years.png\", dpi=120)\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 24, "id": "65eb57d2-4892-47fa-8966-2448ae78719b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== CAlibración isotónica ===\n", "Brier sin/cal:  0.164 / 0.160\n"]}], "source": ["# Calibración (isotónica) + Brier + ECE\n", "\n", "print(\"\\n=== CAlibración isotónica ===\")\n", "cal = CalibratedClassifierCV(clf, method=\"isotonic\", cv=3)\n", "cal.fit(X_train, y_train)\n", "p_test_cal = cal.predict_proba(X_test)[:, 1]\n", "\n", "# Brier\n", "brier_raw = brier_score_loss(y_test, p_test_raw)\n", "brier_cal = brier_score_loss(y_test, p_test_cal)\n", "print(\"Brier sin/cal: \", f\"{brier_raw:.3f}\", \"/\", f\"{brier_cal:.3f}\")"]}, {"cell_type": "code", "execution_count": 25, "id": "b9d3adaf-d058-41e6-99c4-352f978aeb97", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ECE sin/cal: 0.262 / 0.153\n"]}], "source": ["# ECE sencillo (10 bins por cuantiles)\n", "def ece(y_true, p_hat, bins=10):\n", "    pr_true, pr_pred = calibration_curve(y_true, p_hat, n_bins=bins, strategy=\"quantile\")\n", "    return float(np.mean(np.abs(pr_pred - pr_true)))\n", "\n", "print(\"ECE sin/cal:\", f\"{ece(y_test, p_test_raw):.3f}\", \"/\", f\"{ece(y_test, p_test_cal):.3f}\")"]}, {"cell_type": "code", "execution_count": 26, "id": "95ec5bca-2c45-4013-9142-159dbcb3da5c", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# curbas de calibración\n", "t1, p1 = calibration_curve(y_test, p_test_raw, n_bins=10, strategy=\"quantile\")\n", "t2, p2 = calibration_curve(y_test, p_test_cal, n_bins=10, strategy=\"quantile\")\n", "\n", "plt.figure()\n", "plt.plot(p1, t1, label=\"Sin Calibrar\")\n", "plt.plot(p2, t2, label=\"Isotónica\")\n", "plt.plot([0,1],[0,1], linestyle=\"--\")\n", "plt.xlabel(\"Prob. predicha\")\n", "plt.ylabel(\"freq. observada\")\n", "plt.title(\"Calibración (Test)\")\n", "plt.legend()\n", "plt.tight_layout()\n", "plt.savefig(\"T07_Curvas_calibracion.png\", dpi=120)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 27, "id": "31ecc205-dd15-4e9a-91a1-45840da2e4ef", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== Umbral operativo por coste con OOF ===\n", "Umbral óptimo (OOF, coste 5:1): 0.12\n"]}], "source": ["# Umbral operativo (coste FN:FP = 5:1) con OOF (entreno)\n", "print(\"\\n=== Umbral operativo por coste con OOF ===\")\n", "cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)\n", "proba_oof = cross_val_predict(clf, X_train, y_train, cv=cv, method=\"predict_proba\")[:, 1]\n", "grid= np.linspace(0.01, 0.99, 99)\n", "RATIO = 5 \n", "def cost_overall(th, y_true, p_hat):\n", "    pred = (p_hat >= th).astype(int)\n", "    tn, fp, fn, tp = confusion_matrix(y_true, pred).ravel()\n", "    return fp*1 + fn*RATIO\n", "    # nos devuelve el COSTE \n", "thr_oof = grid[np.argmin([cost_overall(t, y_train, proba_oof) for t in grid])]\n", "print(\"Umbral óptimo (OOF, coste 5:1):\", round(thr_oof, 3))"]}, {"cell_type": "code", "execution_count": 28, "id": "385df350-ef3b-4d92-8509-638d65359bea", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Test (calibrado+umbral OOF) → Sens: 0.857 | Esp: 0.152 | FP: 28 | FN: 1\n"]}], "source": ["# Evaluación en test con probabilidades calibradas y umbral OFF\n", "pred_test = (p_test_cal >= thr_oof).astype(int)\n", "tn, fp, fn, tp = confusion_matrix(y_test, pred_test).ravel()\n", "sens = tp/(tp+fn) if (tp+fn)>0 else np.nan\n", "esp = tn/(tn+fp) if (tn+fp)>0 else np.nan\n", "\n", "print(\"Test (calibrado+umbral OOF) → Sens:\", f\"{sens:.3f}\", \"| Esp:\", f\"{esp:.3f}\", \"| FP:\", fp, \"| FN:\", fn)"]}, {"cell_type": "code", "execution_count": 29, "id": "a52f44e9-6928-417e-8f28-c820d09c4d8f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", " === Subgrupos: sex / edad_band ===\n"]}], "source": ["# Evaluación por subgrupos\n", "print (\"\\n === Subgrupos: sex / edad_band ===\")\n", "\n", "def eval_subgrupo(nombre_col, p_test, y_true, thr, test_index):\n", "    if nombre_col not in df.columns:\n", "        print(f\"- {nombre_col}: columna no disponible.\")\n", "        return\n", "\n", "    sub = df.loc[test_index, nombre_col].astype(\"category\")\n", "    valores = list(sub.cat.categories) if hasattr(sub, \"cat\") else sorted(sub.unique())\n", "\n", "    for val in valores:\n", "        mask =(sub.values == val)\n", "        n = int(mask.sum())\n", "        if n < 30:\n", "            print(f\"- {nombre_col}={val}: n={n} (demasiado pequeño)\")\n", "            continue\n", "\n", "        auc = roc_auc_score(y_true[mask], p_test[mask])\n", "        ap = average_precision_score(y_true[mask], p_test[mask])\n", "        pred = (p_test[mask] > thr).astype(int)\n", "        tn, fp, fn, tp = confusion_matrix(y_true[mask], pred).ravel()\n", "        sens = tp/(tp+fn) if (tp+fn) >0 else np.nan\n", "        esp = tn/(tn+fp) if (tn+fp)>0 else np.nan\n", "        flag = \" **ATENCIÓN**\" if (not np.isnan(sens) and sens < 0.60) else \"\"\n", "\n", "        print(f\"- {nombre_col}={val} → AUC:{auc:.3f} | AUPRC:{ap:.3f} | Sens:{sens:.3f} | Esp:{esp:.3f} | n={n}{flag}\")\n"]}, {"cell_type": "code", "execution_count": 30, "id": "18efd809-1dac-48ac-b70e-2e4954509c65", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["- sex=F: n=21 (demasiado pequeño)\n", "- sex=M: n=19 (demasiado pequeño)\n", "- edad_band=40-59: n=12 (demasiado pequeño)\n", "- edad_band=60-74: n=16 (demasiado pequeño)\n", "- edad_band=>=75: n=12 (demasiado pequeño)\n"]}], "source": ["eval_subgrupo(\"sex\", p_test_cal, y_test.values, thr_oof, X_test.index)\n", "eval_subgrupo(\"edad_band\", p_test_cal, y_test.values, thr_oof, X_test.index)"]}, {"cell_type": "code", "execution_count": null, "id": "86bbd91b-3010-42f8-b751-1afa2a56ead8", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "ia-bio", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}