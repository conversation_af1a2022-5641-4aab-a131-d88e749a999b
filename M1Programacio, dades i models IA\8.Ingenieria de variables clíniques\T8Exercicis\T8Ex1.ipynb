{"cells": [{"cell_type": "markdown", "id": "42fc05c3", "metadata": {}, "source": ["Ejercicio 1: Transformaciones log1p seguras y escaladoIdentifica variables con fuerte asimetría, aplica log1p de forma segura a las no negativas con sesgo marcado y estandariza. Compara skewness antes/después y muestra histogramas de al menos dos variables (por ejemplo glucosa y creatinina)."]}, {"cell_type": "code", "execution_count": 10, "id": "876f8079", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "from sklearn.compose import ColumnTransformer\n", "from sklearn.preprocessing import OneHotEncoder, StandardScaler, FunctionTransformer\n", "from sklearn.impute import SimpleImputer\n", "from sklearn.pipeline import Pipeline\n", "import matplotlib.pyplot as plt\n", "import os\n", "df = pd.read_csv(\"../../baseS03.csv\")"]}, {"cell_type": "code", "execution_count": 11, "id": "dd8eceb4", "metadata": {}, "outputs": [], "source": ["# ---------- Columnes ----------\n", "num_cols = [c for c in ['bmi','hr_bpm','sbp_mmhg','spo2_pct','hb_g_dl','creatinine_mg_dl','glucose_mg_dl','egfr_ml_min_1_73m2','age_years'] if c in df.columns]\n", "cat_cols = [c for c in ['sex','edad_band','taquicardia'] if c in df.columns]"]}, {"cell_type": "code", "execution_count": null, "id": "25406192", "metadata": {}, "outputs": [], "source": ["# ---------- Transformació log1p segura ----------\n", "def safe_log1p(X):\n", "    X = pd.DataFrame(X, columns=num_cols).copy()\n", "    for c in num_cols:\n", "        s = X[c].dropna()\n", "        if not s.empty and (s >= 0).all() and s.skew() > 1:\n", "            X[c] = np.log1p(X[c])\n", "    return X.values\n"]}, {"cell_type": "code", "execution_count": 13, "id": "3e27d03f", "metadata": {}, "outputs": [], "source": ["# ---------- Pi<PERSON>ines ----------\n", "num_pipe = Pipeline([\n", "    ('imputer', SimpleImputer(strategy='median')),\n", "    ('log1p', FunctionTransformer(safe_log1p, validate=False)),\n", "    ('scaler', StandardScaler())\n", "])\n", "\n", "cat_pipe = Pipeline([\n", "    ('imputer', SimpleImputer(strategy='most_frequent')),\n", "    ('onehot', OneHotEncoder(handle_unknown='ignore'))\n", "])\n", "\n", "prep = ColumnTransformer([\n", "    ('num', num_pipe, num_cols),\n", "    ('cat', cat_pipe, cat_cols)\n", "])\n"]}, {"cell_type": "code", "execution_count": 14, "id": "72da0f80", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Shape transformado: (200, 16)\n"]}], "source": ["# ---------- Transformació ----------\n", "X_prep = prep.fit_transform(df[num_cols + cat_cols])\n", "print(\"Shape transformado:\", X_prep.shape)\n"]}, {"cell_type": "code", "execution_count": 15, "id": "3cfa7b5d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Skewness abans:\n", "bmi                 0.182281\n", "hr_bpm             -0.169763\n", "sbp_mmhg            0.191115\n", "spo2_pct           -0.216845\n", "hb_g_dl             0.095356\n", "creatinine_mg_dl    0.398773\n", "glucose_mg_dl       0.146856\n", "age_years           0.149734\n", "dtype: float64\n"]}], "source": ["# ---------- Skewness abans ----------\n", "print(\"\\nSkewness abans:\")\n", "print(df[num_cols].skew(numeric_only=True))\n"]}, {"cell_type": "code", "execution_count": 16, "id": "e114daec", "metadata": {}, "outputs": [], "source": ["# ---------- <PERSON><PERSON>s transformació numèrica per comparar ----------\n", "X_num = num_pipe.fit_transform(df[num_cols])\n", "df_num_transformed = pd.DataFrame(X_num, columns=num_cols)\n"]}, {"cell_type": "code", "execution_count": 17, "id": "ba220c01", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Skewness després:\n", "bmi                -0.336884\n", "hr_bpm             -0.169763\n", "sbp_mmhg           -0.227175\n", "spo2_pct           -0.216845\n", "hb_g_dl             0.095356\n", "creatinine_mg_dl   -0.041380\n", "glucose_mg_dl      -0.532240\n", "age_years          -0.587967\n", "dtype: float64\n"]}], "source": ["# ---------- Skewness després ----------\n", "print(\"\\nSkewness després:\")\n", "print(df_num_transformed.skew(numeric_only=True))\n"]}, {"cell_type": "code", "execution_count": null, "id": "b0c88bc5", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Gràfics guardats a figures/hist_glucosa_creatinina.png\n"]}], "source": ["os.makedirs(\"figures\", exist_ok=True)\n", "# ---------- Histogrames ----------\n", "fig, axes = plt.subplots(2, 2, figsize=(10, 6))\n", "\n", "df['glucose_mg_dl'].hist(ax=axes[0,0], bins=30, color=\"lightgreen\")\n", "axes[0,0].set_title(\"Glucosa - Abans\")\n", "\n", "df_num_transformed['glucose_mg_dl'].hist(ax=axes[0,1], bins=30, color=\"green\")\n", "axes[0,1].set_title(\"Glucosa - Després\")\n", "\n", "df['creatinine_mg_dl'].hist(ax=axes[1,0], bins=30, color=\"tan\")\n", "axes[1,0].set_title(\"Creatinina - Abans\")\n", "\n", "df_num_transformed['creatinine_mg_dl'].hist(ax=axes[1,1], bins=30, color=\"peru\")\n", "axes[1,1].set_title(\"Creatinina - Després\")\n", "\n", "plt.tight_layout()\n", "plt.savefig(\"figures/hist_glucosa_creatinina.png\", dpi=300)\n", "plt.show()\n", "\n", "print(\"Gràfics guardats a figures/hist_glucosa_creatinina.png\")\n", "\n"]}], "metadata": {"kernelspec": {"display_name": "ia-bio", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}