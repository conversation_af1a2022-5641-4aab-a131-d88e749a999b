{"cells": [{"cell_type": "markdown", "id": "60e9ee68", "metadata": {}, "source": ["Ejercicio 2: Derivaciones clínicas simples y tabla resumenCrea shock_index = hr_bpm/sbp_mmhg, hipotension_flag = sbp_mmhg < 90 y anemia_flag = hb_g_dl < 12. Construye una tabla por edad_band y sex con n, prevalencia de readmit_30d y medianas/tasas de los nuevos indicadores. Exporta la tabla a CSV."]}, {"cell_type": "code", "execution_count": 20, "id": "0468cc49", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import os\n", "\n", "df = pd.read_csv(\"../../baseS03.csv\")\n"]}, {"cell_type": "code", "execution_count": 21, "id": "e432ee9a", "metadata": {}, "outputs": [], "source": ["# ---------- Derivacions clíniques ----------\n", "if 'hr_bpm' in df.columns and 'sbp_mmhg' in df.columns:\n", "    df['shock_index'] = df['hr_bpm'] / df['sbp_mmhg']\n", "\n", "if 'sbp_mmhg' in df.columns:\n", "    df['hipotension_flag'] = (df['sbp_mmhg'] < 90).astype(int)\n", "\n", "if 'hb_g_dl' in df.columns:\n", "    df['anemia_flag'] = (df['hb_g_dl'] < 12).astype(int)"]}, {"cell_type": "code", "execution_count": 22, "id": "f5721de4", "metadata": {}, "outputs": [], "source": ["# ---------- Agrupació ----------\n", "group_cols = [c for c in ['edad_band','sex'] if c in df.columns]\n", "\n", "summary = (\n", "    df.groupby(group_cols)\n", "    .agg(\n", "        n=('readmit_30d', 'size'),\n", "        readmit_rate=('readmit_30d', 'mean'),\n", "        shock_index_median=('shock_index', 'median'),\n", "        hypotension_rate=('hipotension_flag', 'mean'),\n", "        anemia_rate=('anemia_flag', 'mean')\n", "    )\n", "    .reset_index()\n", ")"]}, {"cell_type": "code", "execution_count": 23, "id": "f6a15726", "metadata": {}, "outputs": [], "source": ["# ---------- Passar a percentatges llegibles ----------\n", "for col in ['readmit_rate','hypotension_rate','anemia_rate']:\n", "    summary[col] = (summary[col] * 100).round(1)"]}, {"cell_type": "code", "execution_count": 24, "id": "0988939d", "metadata": {}, "outputs": [], "source": ["# ---------- Ordenació lògica de edad_band ----------\n", "ordre_edad = [\"<40\", \"40-59\", \"60-74\", \">=75\"]\n", "summary['edad_band'] = pd.Categorical(summary['edad_band'], categories=ordre_edad, ordered=True)\n", "summary = summary.sort_values(['edad_band', 'sex'])\n"]}, {"cell_type": "code", "execution_count": 25, "id": "e42a288b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Taula resum creada i guardada a: figures/taula_ex2T8.csv\n", "  edad_band sex   n  readmit_rate  shock_index_median  hypotension_rate  \\\n", "4       <40   F   5           0.0            0.733333               0.0   \n", "5       <40   M   7          14.3            0.700730               0.0   \n", "0     40-59   F  43          14.0            0.659864               0.0   \n", "1     40-59   M  40          17.5            0.665913               0.0   \n", "2     60-74   F  35          22.9            0.629310               0.0   \n", "3     60-74   M  31          16.1            0.651163               0.0   \n", "6      >=75   F  18          22.2            0.599160               0.0   \n", "7      >=75   M  21          23.8            0.620690               4.8   \n", "\n", "   anemia_rate  \n", "4         80.0  \n", "5         14.3  \n", "0         62.8  \n", "1         32.5  \n", "2         48.6  \n", "3         32.3  \n", "6         38.9  \n", "7         57.1  \n"]}], "source": ["# ---------- Crear carpeta figures si no existeix ----------\n", "os.makedirs(\"figures\", exist_ok=True)\n", "\n", "# ---------- Exportar ----------\n", "output_path = \"figures/taula_ex2T8.csv\"\n", "summary.to_csv(output_path, index=False)\n", "\n", "print(\"Taula resum creada i guardada a:\", output_path)\n", "print(summary)\n"]}], "metadata": {"kernelspec": {"display_name": "ia-bio", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}