{"cells": [{"cell_type": "markdown", "id": "7e12c42a", "metadata": {}, "source": ["Ejercicio 3: Bins e interacciones + logística en testGenera bins (4 cuantiles, one-hot) para creatinine_mg_dl y age_years; crea interacciones de grado 2 (solo interacciones) entre sbp_mmhg, hr_bpm, hb_g_dl y estandarízalas. Con sex y edad_band como categóricas (one-hot), entrena una LogisticRegression con split 80/20 estratificado y reporta ROC AUC y PR AUC en test."]}, {"cell_type": "code", "execution_count": 1, "id": "f4595636", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.preprocessing import KBinsDiscretizer, PolynomialFeatures, StandardScaler, OneHotEncoder\n", "from sklearn.impute import SimpleImputer\n", "from sklearn.compose import ColumnTransformer\n", "from sklearn.pipeline import Pipeline\n", "from sklearn.linear_model import LogisticRegression\n", "from sklearn.metrics import roc_auc_score, average_precision_score\n", "\n", "df = pd.read_csv(\"../../baseS03.csv\")\n"]}, {"cell_type": "code", "execution_count": 2, "id": "7e876ba3", "metadata": {"notebookRunGroups": {"groupValue": "1"}}, "outputs": [], "source": ["# ---------- Columnes ----------\n", "num_bins_cols = [c for c in ['creatinine_mg_dl','age_years'] if c in df.columns]\n", "num_inter_cols = [c for c in ['sbp_mmhg','hr_bpm','hb_g_dl'] if c in df.columns]\n", "cat_cols = [c for c in ['sex','edad_band'] if c in df.columns]\n"]}, {"cell_type": "code", "execution_count": 3, "id": "e09375e7", "metadata": {"notebookRunGroups": {"groupValue": "1"}}, "outputs": [], "source": ["# ---------- Separar features i target ----------\n", "X = df[num_bins_cols + num_inter_cols + cat_cols]\n", "y = df['readmit_30d']  # variable target\n", "\n", "# ---------- Split estratificat 80/20 ----------\n", "X_train, X_test, y_train, y_test = train_test_split(\n", "    X, y, test_size=0.2, stratify=y, random_state=42\n", ")\n", "\n", "# ---------- Pi<PERSON>ines ----------\n", "# Bins en 4 cuantiles, one-hot\n", "bin_pipe = Pipeline([\n", "    ('imputer', SimpleImputer(strategy='median')),\n", "    ('kbins', KBinsDiscretizer(n_bins=4, encode='onehot-dense', strategy='quantile', quantile_method='averaged_inverted_cdf'))\n", "])"]}, {"cell_type": "code", "execution_count": 4, "id": "361e11bd", "metadata": {}, "outputs": [], "source": ["# ---------- Interaccions grado 2 (solo interacciones) + est<PERSON><PERSON><PERSON><PERSON>ó ----------\n", "inter_pipe = Pipeline([\n", "    ('imputer', SimpleImputer(strategy='median')),\n", "    ('poly', PolynomialFeatures(degree=2, interaction_only=True, include_bias=False)),\n", "    ('scaler', StandardScaler())\n", "])\n", "\n", "# ---------- Categòriques: imputació + one-hot ----------\n", "cat_pipe = Pipeline([\n", "    ('imputer', SimpleImputer(strategy='most_frequent')),\n", "    ('onehot', OneHotEncoder(handle_unknown='ignore'))\n", "])\n", "\n", "# ---------- ColumnTransformer ----------\n", "preprocessor = ColumnTransformer([\n", "    ('bins', bin_pipe, num_bins_cols),\n", "    ('inter', inter_pipe, num_inter_cols),\n", "    ('cat', cat_pipe, cat_cols)\n", "])\n", "\n", "# ---------- Pipeline complet amb LogisticRegression ----------\n", "model = Pipeline([\n", "    ('preprocess', preprocessor),\n", "    ('clf', LogisticRegression(max_iter=1000))\n", "])"]}, {"cell_type": "code", "execution_count": 5, "id": "6b99f7e6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ROC AUC test: 0.364\n", "PR AUC test: 0.149\n"]}], "source": ["# ---------- Entrenament ----------\n", "model.fit(X_train, y_train)\n", "\n", "# ---------- Prediccions i avaluació ----------\n", "y_pred_proba = model.predict_proba(X_test)[:,1]\n", "\n", "roc_auc = roc_auc_score(y_test, y_pred_proba)\n", "pr_auc = average_precision_score(y_test, y_pred_proba)\n", "\n", "print(f\"ROC AUC test: {roc_auc:.3f}\")\n", "print(f\"PR AUC test: {pr_auc:.3f}\")\n"]}], "metadata": {"kernelspec": {"display_name": "ia-bio", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}