{"cells": [{"cell_type": "markdown", "id": "a3506aa9-09cd-43a4-a0a0-eb3d24b840b1", "metadata": {}, "source": ["# bins e interacciones"]}, {"cell_type": "code", "execution_count": 1, "id": "78799004-317f-410d-98df-2777473ac457", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "from sklearn.preprocessing import KBinsDiscretizer, PolynomialFeatures\n", "from sklearn.pipeline import Pipeline\n", "from sklearn.compose import ColumnTransformer\n", "from sklearn.impute import SimpleImputer\n", "from sklearn.preprocessing import OneHotEncoder, StandardScaler\n", "\n", "df = pd.read_csv(\"baseS03.csv\")\n", "\n", "# Columnas existentes (intersección para evitar KeyError)\n", "num_base = [c for c in ['sbp_mmhg','hr_bpm','hb_g_dl','creatinine_mg_dl','age_years'] if c in df.columns]\n", "cat_base = [c for c in ['sex','edad_band'] if c in df.columns]\n", "\n", "# Bins como one-hot (más seguro para modelos lineales que 'ordinal')\n", "bin_pipe = Pipeline([('imputer', SimpleImputer(strategy='median')),\n", "             ('kbins', KBinsDiscretizer(n_bins=4, encode='onehot-dense',strategy='quantile'))])\n"]}, {"cell_type": "code", "execution_count": 2, "id": "38e5a4bb-752f-4a74-b8d1-ac36cbd1c055", "metadata": {}, "outputs": [], "source": ["# Interacciones puras de grado 2 y estandarización\n", "inter_pipe = Pipeline([('imputer', SimpleImputer(strategy='median')),\n", "                       ('poly', PolynomialFeatures(degree=2, include_bias=False,interaction_only=True)),\n", "                       ('scaler', StandardScaler())])\n", "\n", "prep = ColumnTransformer([('bins', bin_pipe, [c for c in ['creatinine_mg_dl','age_years'] if c in df.columns]),\n", "                          ('inter', inter_pipe, [c for c in ['sbp_mmhg','hr_bpm','hb_g_dl'] if c in df.columns]),\n", "                          ('cat', Pipeline([('imputer', SimpleImputer(strategy='most_frequent')), \n", "                                    ('onehot', OneHotEncoder(handle_unknown='ignore'))]),cat_base)])"]}, {"cell_type": "code", "execution_count": 3, "id": "8d0dff3a-e075-4ac5-a78a-697c87ff53f0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Shape con bins+interacciones: (200, 20)\n"]}], "source": ["X = prep.fit_transform(df[num_base + cat_base])\n", "print(\"Shape con bins+interacciones:\", X.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "6ed571ef-6b9d-4d7b-80e8-345c18789c28", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "anaconda-2024.02-py310", "language": "python", "name": "conda-env-anaconda-2024.02-py310-py"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 5}