{"cells": [{"cell_type": "markdown", "id": "af014dbe-8ea7-4ad6-8722-3098036518c5", "metadata": {}, "source": ["### transformaciones básicas y derivaciones clínicas"]}, {"cell_type": "code", "execution_count": 2, "id": "fed7791e-6b1c-449b-bcea-a8161aaf5f91", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "from sklearn.compose import ColumnTransformer\n", "from sklearn.preprocessing import OneHotEncoder, StandardScaler, FunctionTransformer\n", "from sklearn.impute import SimpleImputer\n", "from sklearn.pipeline import Pipeline\n", "df = pd.read_csv(\"baseS03.csv\")"]}, {"cell_type": "code", "execution_count": 3, "id": "819931ab-d8f2-49e4-a656-7f621f61ca44", "metadata": {}, "outputs": [], "source": ["# Columnas existentes (intersección para evitar KeyError si falta alguna)\n", "num_cols = [c for c in ['bmi','hr_bpm','sbp_mmhg','spo2_pct','hb_g_dl','creatinine_mg_dl','glucose_mg_dl','egfr_ml_min_1_73m2','age_years'] if c in df.columns]\n", "cat_cols = [c for c in ['sex','edad_band','taquicardia'] if c in df.columns]"]}, {"cell_type": "code", "execution_count": 6, "id": "423a584a-a5ef-43f0-9f8f-96e23f75cdbc", "metadata": {}, "outputs": [], "source": ["# Transformación log1p segura: solo variables no negativas y con asimetría marcada\n", "def safe_log1p(X):\n", "    X = pd.DataFrame(X, columns=num_cols).copy()\n", "    for c in num_cols:\n", "        s = X[c].dropna()\n", "        if not s.empty and (s >= 0).all() and s.skew() >1:\n", "            X[c] = np.log1p(X[c])\n", "    return X.values\n", "\n", "num_pipe = Pipeline([('imputer', SimpleImputer(strategy='median')),\n", "                     ('log1p', FunctionTransformer(safe_log1p, validate=False)),\n", "                     ('scaler', StandardScaler())])  \n", "\n", "cat_pipe = Pipeline([('imputer', SimpleImputer(strategy='most_frequent')),\n", "                     ('onehot', OneHotEncoder(handle_unknown='ignore'))])\n", "\n", "prep = ColumnTransformer([('num', num_pipe, num_cols),('cat', cat_pipe, cat_cols)])"]}, {"cell_type": "code", "execution_count": 7, "id": "9b8d11cb-a9ce-48b6-a2f9-1c251cfe8448", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Shape transformado: (200, 16)\n"]}], "source": ["X_prep = prep.fit_transform(df[num_cols + cat_cols])\n", "print(\"Shape transformado:\", X_prep.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "29f2bfeb-6193-4cd6-a6ad-2b8efc3840c6", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "anaconda-2024.02-py310", "language": "python", "name": "conda-env-anaconda-2024.02-py310-py"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 5}