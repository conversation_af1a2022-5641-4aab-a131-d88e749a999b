{"cells": [{"cell_type": "code", "execution_count": 7, "id": "cfd8a0da", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "from sklearn.preprocessing import FunctionTransformer, OneHotEncoder, StandardScaler\n", "from sklearn.compose import ColumnTransformer\n", "from sklearn.pipeline import Pipeline\n", "from sklearn.impute import SimpleImputer\n", "df = pd.read_csv(\"../../baseS03.csv\")"]}, {"cell_type": "code", "execution_count": 8, "id": "7a07ffb8", "metadata": {}, "outputs": [], "source": ["# columnes existents (intersecció per eitar key error si falta alguna colulmna)\n", "num_cols = [c for c in ['bmi', 'hr_bpm', 'sbp_mmhg', 'spo2_pct', 'hb_g_dl',\n", "                        'creatinine_mg_dl', 'glucose_mg_dl', 'egfr_ml_min_1_73m2', 'age_years'] if c in df.columns]\n", "cat_cols = [c for c in [\"sex\", \"edad_band\", \"taquicardia\"] if c in df.columns]"]}, {"cell_type": "code", "execution_count": 9, "id": "0fd90ee6", "metadata": {}, "outputs": [], "source": ["# transformac<PERSON><PERSON> loglp segura: només vaiables no negatives i amb assimètria marcada\n", "def save_loglp(X):\n", "    X = pd.DataFrame(X, columns=num_cols).copy()\n", "    for c in num_cols:\n", "        s = X[c].dropna()\n", "        if not s.empty and (s >= 0).all() and s.skew() > 1:\n", "            X[c] = np.log1p(X[c])\n", "    return X.values\n", "\n", "\n", "num_pipe = Pipeline([\n", "    (\"imputer\", SimpleImputer(strategy=\"median\")),\n", "    (\"log\", FunctionTransformer(save_loglp, validate=False)),\n", "    (\"scaler\", StandardScaler())\n", "])\n", "cat_pipe = Pipeline([\n", "    (\"imputer\", SimpleImputer(strategy=\"most_frequent\")),\n", "    (\"onehot\", OneHotEncoder(handle_unknown=\"ignore\"))\n", "])\n", "prep = ColumnTransformer([\n", "    (\"num\", num_pipe, num_cols),\n", "    (\"cat\", cat_pipe, cat_cols)\n", "])"]}, {"cell_type": "code", "execution_count": 10, "id": "bfd4fc51", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Shape tansformats:  (200, 16)\n"]}], "source": ["X_prep = prep.fit_transform(df[num_cols+cat_cols])\n", "print(\"Shape tansformats: \", X_prep.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "00f613b8", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "ia-bio", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}