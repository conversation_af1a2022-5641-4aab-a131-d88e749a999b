{"cells": [{"cell_type": "markdown", "id": "de9d6042-2bd8-4286-b54c-5ea4e86b9423", "metadata": {}, "source": ["# Acerca del df\n", "\n", "## Resumen\n", "Este conjunto de datos contiene indicadores médicos, bioquímicos y de estilo de vida recopilados de pacientes para analizar y predecir el riesgo de formación de cálculos renales.\n", "El objetivo es ayudar a investigadores y científicos de datos a construir modelos predictivos, explorar correlaciones y comprender cómo diferentes factores (como la dieta, el consumo de agua y los antecedentes familiares) influyen en la probabilidad de padecer cálculos renales.\n", "\n", "La litiasis renal es uno de los trastornos urológicos más comunes en todo el mundo y afecta a aproximadamente 1 de cada 10 personas a lo largo de su vida.\n", "\n", "Al combinar los resultados de laboratorio clínico con factores de estilo de vida, este conjunto de datos proporciona una visión integral para el análisis tanto médico como basado en datos.\n", "\n", "### Estructura del conjunto de datos\n", "\n", "El conjunto de datos contiene X filas y 23 columnas, que incluyen características numéricas y categóricas. Cada fila corresponde al historial clínico de un paciente.\n", "\n", "#### Columnas\n", "**serum_creatinine (mg/dL)(float):** <PERSON>vel de creatinina en sangre, un marcador clave de la función renal (Mujeres: 0.6–1.1 mg/dL · Hombres: 0.7–1.3 mg/dL).\n", "\n", "**gfr (mL/min/1.73 m2)(float):** <PERSON><PERSON> de Filtración Glomerular, principal indicador de la eficiencia renal (Normal: >90 mL/min/1.73 m2 · Leve ↓: 60–89 · Moderado ↓: 30–59).\n", "\n", "**bun (mg/dL)(float):** Nitrógeno Ureico en Sangre, mide la acumulación de desechos en la sangre (7–20 mg/dL).\n", "\n", "**serum_calcium (mg/dL)(float):** <PERSON>vel de calcio en sangre; un desequilibrio puede causar cálculos (8.5–10.5 mg/dL).\n", "\n", "**ana (int):** Anticuerpos antinucleares, marcadores inmunitarios relacionados con la salud renal (Negativo o título <1:40 · Positivo puede indicar autoinmunidad).\n", "\n", "**c3_c4 (mg/dL)(float):** <PERSON><PERSON><PERSON><PERSON> del complemento, factores del sistema inmunitario (C3: 90–180 mg/dL · C4: 10–40 mg/dL).\n", "\n", "**hematuria (int):** Sangre en orina (0 = No, 1 = Sí).\n", "\n", "**oxalate_levels (mg/día)(float):** Concentración urinaria de oxalato, un componente principal de los cálculos (<40 mg/día).\n", "\n", "**urine_ph (float):** Acidez/alcalinidad de la orina, afecta la formación de cálculos (4.5–8.0).\n", "\n", "**blood_pressure (mmHg)(float):** Nivel de presión arterial en mmHg (Normal: <120/80 mmHg ).\n", "\n", "**physical_activity (string):** <PERSON><PERSON> de actividad (bajo, moderado, alto).\n", "\n", "**diet (string):** <PERSON><PERSON><PERSON> de dieta (p. ej., alta en proteínas, baja en sal, equilibrada). \n", "\n", "**water_intake (l)(float):** Consumo diario de agua (2–3 litros/día).\n", "\n", "**smoking (string):** <PERSON><PERSON><PERSON><PERSON>uma<PERSON> (sí/no).\n", "\n", "**alcohol (string):** Consumo de alcohol (diariamente, nunca, ocasionalmente).\n", "\n", "**painkiller_usage (int):** Uso frecuente de analgésicos (0/1).\n", "\n", "**family_history (int):** Antecedentes familiares de enfermedad renal o cálculos renales (0/1).\n", "\n", "**weight_changes (string):** Tendencia de peso (pérdida, aumento, estable).\n", "\n", "**stress_level (string):** Categoría de estrés (bajo, moderado, alto).\n", "\n", "**months (int):** Periodo de seguimiento u observación en meses.\n", "\n", "**cluster (int):** Asignación de grupo a partir del análisis de conglomerados.\n", "\n", "**ckd_pred (int):** Predicción de enfermedad renal crónica (0/1).\n", "\n", "**ckd_stage (int):** Estadio de la ERC (1-5).\n", "\n", "**stone_risk (int):** Variable objetivo (0 = Sin riesgo, 1 = En riesgo).\n", "\n", "csv descargado en: CSV descargado en:\n", "https://www.kaggle.com/datasets/omarayman15/kidney-stones?resource=download"]}, {"cell_type": "code", "execution_count": 1, "id": "f97b025e-6770-45a1-acc7-a1d43d12dbe5", "metadata": {}, "outputs": [], "source": [" import numpy as np\n", " import pandas as pd"]}, {"cell_type": "code", "execution_count": 2, "id": "76a830a0-dd9f-43ac-9c0d-2a259a519659", "metadata": {}, "outputs": [], "source": ["# Mejorar la visualización\n", "pd.set_option(\"display.max_columns\", 80)\n", "pd.set_option(\"display.width\", 120)\n", "\n", "# Semilla para reproducibilidad\n", "RANDOM_SEED = 42\n", "rng = np.random.default_rng(RANDOM_SEED)"]}, {"cell_type": "code", "execution_count": 3, "id": "83c0b39c-d41d-418f-ae1f-722674b476d4", "metadata": {}, "outputs": [], "source": ["# Carga del dataset\n", "df = pd.read_csv(\"cleaned_stone.csv\")"]}, {"cell_type": "code", "execution_count": 4, "id": "0e8b22e9-a938-4126-8902-96b9d3fd473e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(4000, 24)\n", "   serum_creatinine        gfr         bun  serum_calcium  ana       c3_c4  hematuria  oxalate_levels  urine_ph  \\\n", "0          0.683683  32.946784    7.553739      10.039896    0  138.204989          0        2.878164  7.864308   \n", "1          3.809044  32.685035  141.347494       8.330543    1   24.282343          1        4.767639  4.920015   \n", "2          1.143827   2.079805   15.979104       9.419229    0  163.970666          0        1.818613  6.188115   \n", "\n", "   blood_pressure physical_activity          diet  water_intake smoking alcohol painkiller_usage family_history  \\\n", "0      115.224217            weekly  high protein      2.314979     yes   daily               no            yes   \n", "1      130.143900            weekly  high protein      2.250649     yes   daily               no            yes   \n", "2       98.026072            weekly      low salt      2.542343      no   daily               no             no   \n", "\n", "  weight_changes stress_level  months  cluster ckd_pred  ckd_stage  stone_risk  \n", "0         stable          low      10        5      CKD          3           1  \n", "1           loss     moderate       1        2      CKD          3           1  \n", "2         stable     moderate       4        6      CKD          5           0  \n", "<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 4000 entries, 0 to 3999\n", "Data columns (total 24 columns):\n", " #   Column             Non-Null Count  Dtype  \n", "---  ------             --------------  -----  \n", " 0   serum_creatinine   4000 non-null   float64\n", " 1   gfr                4000 non-null   float64\n", " 2   bun                4000 non-null   float64\n", " 3   serum_calcium      4000 non-null   float64\n", " 4   ana                4000 non-null   int64  \n", " 5   c3_c4              4000 non-null   float64\n", " 6   hematuria          4000 non-null   int64  \n", " 7   oxalate_levels     4000 non-null   float64\n", " 8   urine_ph           4000 non-null   float64\n", " 9   blood_pressure     4000 non-null   float64\n", " 10  physical_activity  4000 non-null   object \n", " 11  diet               4000 non-null   object \n", " 12  water_intake       4000 non-null   float64\n", " 13  smoking            4000 non-null   object \n", " 14  alcohol            4000 non-null   object \n", " 15  painkiller_usage   4000 non-null   object \n", " 16  family_history     4000 non-null   object \n", " 17  weight_changes     4000 non-null   object \n", " 18  stress_level       4000 non-null   object \n", " 19  months             4000 non-null   int64  \n", " 20  cluster            4000 non-null   int64  \n", " 21  ckd_pred           4000 non-null   object \n", " 22  ckd_stage          4000 non-null   int64  \n", " 23  stone_risk         4000 non-null   int64  \n", "dtypes: float64(9), int64(6), object(9)\n", "memory usage: 750.1+ KB\n", "None\n", "stone_risk\n", "0    0.60825\n", "1    0.39175\n", "Name: proportion, dtype: float64\n"]}], "source": ["# Vista rápida\n", "print(df.shape)          # (n_filas, n_columnas)\n", "print(df.head(3))        # primeras filas\n", "print(df.info())         # tipos de datos y nulos\n", "print(df['stone_risk'].value_counts(normalize=True))     # cant de valores únicos normalizados"]}, {"cell_type": "code", "execution_count": 5, "id": "2785cf68-0424-48b6-9a85-7ced9fffdbbe", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Valores de creatinina sérica fuera de rango: 1543\n", "Valores ausentes por columnas:\n", "serum_creatinine    0.0\n", "gfr                 0.0\n", "bun                 0.0\n", "serum_calcium       0.0\n", "ana                 0.0\n", "c3_c4               0.0\n", "hematuria           0.0\n", "oxalate_levels      0.0\n", "urine_ph            0.0\n", "blood_pressure      0.0\n", "dtype: float64\n", "Nuestra variable objetivo es: stone_risk\n", "Lista de posibles predictores:\n", "['serum_creatinine', 'gfr', 'bun', 'serum_calcium', 'ana', 'c3_c4', 'hematuria', 'oxalate_levels', 'urine_ph', 'blood_pressure', 'physical_activity', 'diet', 'water_intake', 'smoking', 'alcohol', 'painkiller_usage', 'family_history', 'weight_changes', 'stress_level', 'months', 'cluster', 'ckd_pred', 'ckd_stage']\n"]}], "source": ["# Comprobaciones iniciales sugeridas\n", "# 1) Rangos fisiológicos básicos\n", "ok_serum_creatinine = df['serum_creatinine'].between(0.6, 1.3) | df['serum_creatinine'].isna()\n", "print('Valores de creatinina sérica fuera de rango:', (~ok_serum_creatinine).sum())\n", "\n", "# 2) Ausentes por columna (proporción)\n", "missing = df.isna().mean().sort_values(ascending=False)\n", "print('Valores ausentes por columnas:')\n", "print(missing.head(10))\n", "\n", "# 3) Definir outcome y lista tentativa de predictores\n", "target = 'stone_risk'\n", "print('Nuestra variable objetivo es:', target)\n", "features = [c for c in df.columns if c not in {target}]\n", "print('Lista de posibles predictores:')\n", "print(features)"]}, {"cell_type": "code", "execution_count": 8, "id": "9baf130f-0c70-48c1-b8ac-165fba570141", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Columnas sospechosas: []\n", "Posibles columnas de texto libre: []\n", "Proporción de ausentes:\n", "serum_creatinine     0.0\n", "gfr                  0.0\n", "bun                  0.0\n", "serum_calcium        0.0\n", "ana                  0.0\n", "c3_c4                0.0\n", "hematuria            0.0\n", "oxalate_levels       0.0\n", "urine_ph             0.0\n", "blood_pressure       0.0\n", "physical_activity    0.0\n", "diet                 0.0\n", "water_intake         0.0\n", "smoking              0.0\n", "alcohol              0.0\n", "dtype: float64\n"]}], "source": ["import re\n", "\n", "# 1) Búsqueda de columnas peligrosas por nombre\n", "suspect_cols = [c for c in df.columns if re.search(r'(name|surname|address|email|phone|nhc|dni)', c, flags=re.I)]\n", "print(\"Columnas sospechosas:\", suspect_cols)\n", "\n", "# 2) Heurística simple para detectar texto libre (muchos caracteres/espacios)\n", "text_like = []\n", "for c in df.columns:\n", "   if df[c].dtype == 'object':\n", "       sample = df[c].dropna().astype(str).head(50)\n", "       if sample.map(len).mean() > 60: \n", "           text_like.append(c)\n", "print(\"Posibles columnas de texto libre:\", text_like)\n", "\n", "# 3) Perfil de ausentes por columna (proporción)\n", "missing = df.isna().mean().sort_values(ascending=False)\n", "print(\"Proporción de ausentes:\")\n", "print(missing.head(15))"]}, {"cell_type": "code", "execution_count": 13, "id": "688ef747-33e4-4862-98bb-7842a552b56a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["smoking no tiene orden definido, se codifica por orden alfabético.\n", "painkiller_usage no tiene orden definido, se codifica por orden alfabético.\n", "family_history no tiene orden definido, se codifica por orden alfabético.\n"]}], "source": ["# 4) Derivaciones seguras: pasar las columnas string a int\n", "col_camb = [\"physical_activity\", \"diet\", \"smoking\", \"alcohol\", \"painkiller_usage\", \"family_history\", \n", "            \"weight_changes\", \"stress_level\", \"ckd_pred\"]\n", "# Crear diccionario con las órdenes para los códigos\n", "ordenes = {\n", "    \"physical_activity\": [\"rarely\", \"daily\", \"weekly\"],\n", "    \"diet\": [\"balanced\", \"low salt\", \"high protein\"],\n", "    \"alcohol\": [\"never\", \"occasionally\", \"daily\"],\n", "    \"weight_changes\": [\"loss\", \"stable\", \"gain\"],\n", "    \"stress_level\": [\"low\", \"moderate\", \"high\"],\n", "    \"ckd_pred\": [\"No CKD\", \"CKD\"]}\n", "\n", "for col in col_camb:\n", "    if col in ordenes:\n", "        df[col] = pd.Categorical(df[col], categories=ordenes[col], ordered=True)\n", "        df[col + \"_cod\"] = df[col].cat.codes\n", "    else:\n", "        print(f\"{col} no tiene orden definido, se codifica por orden alfabético.\")\n", "        df[col] = df[col].astype(\"category\")\n", "        df[col + \"_cod\"] = df[col].cat.codes"]}, {"cell_type": "code", "execution_count": 32, "id": "e88b7c78-656f-4558-8ca3-cdde5b4a951c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Columna modificada:\n", "'physical_activity' → 'physical_activity_cod' → 0: 'rarely',  1: 'daily',  2: 'weekly'\n", "'diet' → 'diet_cod' → 0: 'balanced',  1: 'low salt',  2: 'high protein'\n", "'smoking' → 'smoking_cod' → 0: 'no',  1: 'yes'\n", "'alcohol' → 'alcohol_cod' → 0: 'never',  1: 'occasionally',  2: 'daily'\n", "'painkiller_usage' → 'painkiller_usage_cod' → 0: 'no',  1: 'yes'\n", "'family_history' → 'family_history_cod' → 0: 'no',  1: 'yes'\n", "'weight_changes' → 'weight_changes_cod' → 0: 'loss',  1: 'stable',  2: 'gain'\n", "'stress_level' → 'stress_level_cod' → 0: 'low',  1: 'moderate',  2: 'high'\n", "'ckd_pred' → 'ckd_pred_cod' → 0: 'No CKD',  1: 'CKD'\n"]}], "source": ["# 4) Derivaciones seguras: pasar las columnas string a int\n", "cols = [\"physical_activity\", \"diet\", \"smoking\", \"alcohol\", \"painkiller_usage\", \"family_history\", \n", "            \"weight_changes\", \"stress_level\", \"ckd_pred\"]\n", "# Crear diccionario con las órdenes para los códigos\n", "ordenes = {\n", "    \"physical_activity\": [\"rarely\", \"daily\", \"weekly\"],\n", "    \"diet\": [\"balanced\", \"low salt\", \"high protein\"],\n", "    \"alcohol\": [\"never\", \"occasionally\", \"daily\"],\n", "    \"weight_changes\": [\"loss\", \"stable\", \"gain\"],\n", "    \"stress_level\": [\"low\", \"moderate\", \"high\"],\n", "    \"ckd_pred\": [\"No CKD\", \"CKD\"]}\n", "\n", "def codificar(df, cols, ordenes=None):\n", "    mapeo = {}\n", "    for c in cols:\n", "        if c not in df.columns:\n", "            continue\n", "        # Aplicar orden personalizado si existe\n", "        if ordenes and c in ordenes:\n", "            df[c] = pd.Categorical(df[c], categories=ordenes[c], ordered=True)\n", "            categorias = ordenes[c]\n", "        else:\n", "            df[c] = df[c].astype(\"category\")\n", "            categorias = list(df[c].cat.categories)\n", "        # Crear columna codificada\n", "        cod = c + \"_cod\"\n", "        df[cod] = df[c].cat.codes\n", "        # Guardar mapeo\n", "        mapeo[c] = {i: cat for i, cat in enumerate(categorias)}\n", "\n", "    print(\"Columna modificada:\")\n", "    for c in mapeo:\n", "        cod = c + \"_cod\"\n", "        pares = [f\"{k}: '{v}'\" for k, v in mapeo[c].items()]\n", "        print(f\"'{c}' → '{cod}' → \" + \",  \".join(pares))\n", "\n", "    return df, mapeo\n", "\n", "df, mapeo = codificar(df, cols, ordenes)"]}, {"cell_type": "code", "execution_count": null, "id": "74a93411-7f15-4e32-b838-31d4bf25f09f", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "e1d92ee5-d477-46e7-ae78-551349dfa3a5", "metadata": {}, "outputs": [], "source": [" # Guardar una versión minimizada para ejercicios de S02–S04\n", "keep = [\"episode_id\",\"patient_id\",\"admission_datetime\",\"discharge_datetime\", \n", "\"length_of_stay_days\",\"age_years\",\"sex\",\"bmi\",\"hr_bpm\",\"sbp_mmhg\",\"spo2_pct\", \n", "\"temp_c\",\"hb_g_dl\",\"creatinine_mg_dl\",\"glucose_mg_dl\",\"wbc_10e9_per_l\",\n", " \"egfr_ml_min_1_73m2\",\"edad_band\",\"readmit_30d\"]\n", " df[keep].to_csv(\"HOSPITAL_General_Sim_min.csv\", index=False)\n", " print(\"OK: HOSPITAL_General_Sim_min.csv generado\")\n", "\n", " # 2) Selección de columnas de interés y filtrado de adultos\n", " cols = \n", "['age_years','sex','bmi','hr_bpm','sbp_mmhg','spo2_pct','hb_g_dl','creatinine_mg\n", " _dl','glucose_mg_dl','readmit_30d']\n", " adultos = df.loc[df['age_years'] >= 18, cols].copy()\n", " # 3) Derivaciones con assign: IMC válido y flag de taquicardia (>=100 lpm)\n", " adultos = (adultos\n", "     .assign(imc=lambda d: d['bmi'],taquicardia=lambda d: (d['hr_bpm']>=100)\n", "     .astype('boolean'))\n", "          )\n", " # 4) Tipos: sexo como categoría ordenada\n", " adultos['sex'] = adultos['sex'].astype('category')\n", " # 5) Resúmenes por banda de edad\n", " def edad_band(x):\n", "    if pd.isna(x): return pd.NA\n", "    if x < 40: return '<40'\n", "    if x < 60: return '40-59'\n", "    if x < 75: return '60-74'\n", "    return '>=75'\n", " adultos['edad_band'] = adultos['age_years'].map(edad_band).astype('category')\n", " tabla = (adultos\n", "         .groupby('edad_band')\n", "         .agg(n=('readmit_30d','size'),\n", "              reingreso_media=('readmit_30d','mean'),\n", "              imc_median=('imc','median'),\n", "              hb_med=('hb_g_dl','mean'))\n", "         .reset_index()\n", "        )\n", " print(tabla.sort_values('reingreso_media', ascending=False))\n", " # 6) <PERSON><PERSON> con diccionarios (merge)\n", " mapa_band = pd.DataFrame({'edad_band': ['<40','40-59','60-74','>=75'],\n", "    'riesgo_base': ['bajo','bajo','medio','alto']})\n", " tabla = tabla.merge(mapa_band, on='edad_band', how='left')\n", " print(tabla)\n", " # 7) Guardar resultados intermedios\n", "tabla.to_csv(\"S02_tabla_resumen.csv\", index=False)"]}], "metadata": {"kernelspec": {"display_name": "Python (bioai)", "language": "python", "name": "bioai"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.18"}}, "nbformat": 4, "nbformat_minor": 5}