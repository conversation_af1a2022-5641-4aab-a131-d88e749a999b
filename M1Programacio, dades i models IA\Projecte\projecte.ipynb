import os
import random
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns

from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler, OneHotEncoder
from sklearn.compose import ColumnTransformer
from sklearn.pipeline import Pipeline

# Funció per imprimir línies amb text opcional
def print_line(text=None, width=60, char="_"):
    print()
    if text is None:
        print(char * width)
    else:
        text = f" {text} "
        side_len = (width - len(text)) // 2
        line = char * side_len + text + char * (width - len(text) - side_len)
        print(line)

# Millorem la visualització
pd.set_option("display.max_columns", 24)
pd.set_option("display.width", 120)

# Fixació de la llavor per a la reproductibilitat
RANDOM_SEED = 42
rng = np.random.default_rng(RANDOM_SEED)

# Carrega del dataset
data_path = "cleaned_stone.csv"  
df = pd.read_csv(data_path)

# Crear carpeta figures si no existeix
os.makedirs("figures", exist_ok=True)

# Separar tipus de dades
num_cols = df.select_dtypes(include=["number"]).columns.tolist()
cat_cols = df.select_dtypes(include=["object", "category"]).columns.tolist()
cat_cols = [c for c in cat_cols if not pd.api.types.is_datetime64_any_dtype(df[c])]


# Shape i tipus de dades
print("SHAPE: ", df.shape)

print_line("INFORMACIÓ GENERAL")
df.info()

# Missing values
print_line("VALORS ABSENTS")
print(df.isna().sum())

# Primeres files
print_line("PRIMERES 3 FILES")
print(df.head(3))

# Estadístiques descriptives
print_line("DESCRIPCIÓ GENERAL NUMÈRICA")
print(df[num_cols].describe())

print_line("DESCRIPCIÓ GENERAL COMPLETA")
print(df.describe(include='all'))

# Estadístiques separades per tipus
print_line("ESTADÍSTIQUES NUMÈRIQUES")
print(df[num_cols].describe().T)

print_line("ESTADÍSTIQUES CATEGÒRIQUES")
print(df[cat_cols].describe().T)

# Distribució de la variable objectiu
print_line("VALORS ÚNICS DE stone_risk")
print(df["stone_risk"].value_counts())

print_line("PROPORCIÓ stone_risk")
print(df["stone_risk"].value_counts(normalize=True))


# Balance de clases: proporción de stone_risk (0/1).
TARGET = 'stone_risk'
y= df[TARGET].astype(int)
tab = y.value_counts().sort_index().rename(index={0:'No risk', 1:'Risk'}).to_frame('n')
tab['percentatge%'] = ((tab['n'] / tab['n'].sum())*100).round(1)

display(tab)


fig=plt.figure()

tab['n'].plot(kind='bar')
plt.title('Distribució del target')
plt.ylabel('N casos')
plt.xticks(rotation=0)
plt.savefig("figures/Distribucio_del_target.png")
plt.close()

# Rangos clínicos plausibles
rangs = {
    "urine_ph": (4.5, 8.0),
    "oxalate_levels": (0, 40),
    "serum_calcium": (8.5, 10.5),
    "gfr": (30, 120)
}
# urine_ph: 4.5–8.0
# oxalate_levels: < 40 mg/día
# serum_calcium: 8.5–10.5 mg/dL 
# gfr: > 90 (normal), 60–89 (leve ↓), 30–59 (moderada ↓)

urine_ph_out_of_range = ~df['urine_ph'].between(4.5, 8.0)
print("Valors de urine_ph fora de rang", (urine_ph_out_of_range).sum())

oxalate_levels_out_of_range = ~df['oxalate_levels'].between(0, 40)
print("Valors de oxalate_levels fora de rang", (oxalate_levels_out_of_range).sum())

serum_calcium_out_of_range = ~df['serum_calcium'].between(8.5, 10.5)
print("Valors de serum_calcium fora de rang", (serum_calcium_out_of_range).sum())


#print("Valors de gfr fora de rang", (gfr_out_of_range).sum())
# gfr: > 90 (normal), 60–89 (leve ↓), 30–59 ()
def categoritzar_gfr(df: pd.DataFrame) -> pd.DataFrame:
    def gfr_band(x):
        if pd.isna(x): return pd.NA
        if x < 30: return 'out_of_range'
        if x < 60: return 'moderada ↓'
        if x < 90: return 'leve ↓'
        return 'normal'
    out = df.copy()
    out['gfr_band'] = out['gfr'].map(gfr_band)
    return out

df = categoritzar_gfr(df)
tab = df["gfr_band"].value_counts().to_frame("n")
tab["pct"] = (df["gfr_band"].value_counts(normalize=True) * 100).round(2)
display(tab)


fig=plt.figure()

tab['n'].plot(kind='bar')
plt.title('Distribució de gfr_band')
plt.ylabel('N casos')
plt.xticks(rotation=0)
plt.savefig("figures/Distribucio_de_gfr_band.png")
plt.close()

# Outliers numèrics
print("OUTLIERS NUMÈRICS")
print(df[num_cols].describe())   # min, max, mean, std

cols_to_plot = ["serum_calcium", "oxalate_levels", "gfr", "blood_pressure", "serum_creatinine", "bun", "water_intake"]

# Boxplot en un sol gràfic
fig, axes = plt.subplots(1, len(cols_to_plot), figsize=(16, 4))
for ax, col in zip(axes, cols_to_plot):
    ax.boxplot(df[col].dropna())
    ax.set_title(col)
plt.tight_layout()
plt.savefig("figures/Boxplot_outliers.png")
plt.close()

for col in num_cols:
    Q1 = df[col].quantile(0.25)
    Q3 = df[col].quantile(0.75)
    IQR = Q3 - Q1
    lower = Q1 - 1.5 * IQR
    upper = Q3 + 1.5 * IQR
    outliers = df[(df[col] < lower) | (df[col] > upper)]
    print_line(f"Outliers {col}")
    print(f"Número de valors fora de rang: {len(outliers)}")
    if len(outliers) > 0:
        print(outliers[col].sort_values().tolist()[:5], "...")  # Mostrem només els 5 primers



# Escales
print("ESCALA DE VARIABLES")
print(df[num_cols].agg(["min", "max"]).T)


# Categòriques
print("CATEGÒRIQUES")
for col in cat_cols:
    plt.figure()
    print_line(f"{col}")
    print(df[col].value_counts(dropna=False))


# Coherència semàntica
print_line("COHERÈNCIA SEMÀNTICA")
print("Exemple blood_pressure:")
print(df["blood_pressure"].head(10))


# Variables numèriques
num_cols = df.select_dtypes(include="number").columns.tolist()

# Calcula mitjana i mediana
means = df[num_cols].mean()
medians = df[num_cols].median()

# Gràfic
plt.figure()
plt.figure(figsize=(12,6))
x = range(len(num_cols))
plt.bar(x, means, width=0.4, label='Mitjana', align='center')
plt.bar(x, medians, width=0.4, label='Mediana', align='edge')
plt.xticks(x, num_cols, rotation=45, ha='right')
plt.ylabel('Valor')
plt.title('Mitjana vs Mediana per variable numèrica')
plt.legend()
plt.tight_layout()
plt.savefig("figures/Mitjana_vs_Mediana_per_variable_numerica.png")
plt.close()

# Tipos y codificación
for col in cat_cols:
    print_line(f"Categories {col}")
    print(df[col].value_counts())

# Coherència semàntica

print(df['water_intake'].describe())
plt.figure()
plt.hist(df['water_intake'].dropna(), bins=20)
plt.title("Distribució water_intake")
plt.savefig("figures/Distribucio_water_intake.png")
plt.close()

plt.figure()
print_line("urine_ph")
print(df['urine_ph'].describe())
plt.figure()
plt.hist(df['urine_ph'].dropna(), bins=20)
plt.title("Distribució urine_ph")
plt.savefig("figures/Distribucio_urine_ph.png")
plt.close()

plt.figure()
print_line("oxalate_levels")
print(df['oxalate_levels'].describe())
plt.figure()
plt.hist(df['oxalate_levels'].dropna(), bins=20)
plt.title("Distribució oxalate_levels")
plt.savefig("figures/Distribucio_oxalate_levels.png")
plt.close()

plt.figure()
print_line("serum_calcium")
print(df['serum_calcium'].describe())
plt.figure()
plt.hist(df['serum_calcium'].dropna(), bins=20)
plt.title("Distribució serum_calcium")
plt.savefig("figures/Distribucio_serum_calcium.png")
plt.close()

plt.figure()
print_line("gfr")
print(df['gfr'].describe())
plt.figure()
plt.hist(df['gfr'].dropna(), bins=20)
plt.title("Distribució gfr")
plt.savefig("figures/Distribucio_gfr.png")
plt.close()

plt.figure()


