{"cells": [{"cell_type": "code", "execution_count": 1, "id": "44678c39", "metadata": {}, "outputs": [], "source": ["%matplotlib inline\n", "import sys, numpy as np, pandas as pd, sklearn, matplotlib"]}, {"cell_type": "code", "execution_count": 2, "id": "4fd8e91b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["3.12.11 | packaged by Anaconda, Inc. | (main, Jun  5 2025, 12:58:53) [MSC v.1929 64 bit (AMD64)]\n", "numpy 2.3.1 mat<PERSON><PERSON>lib 3.10.5 sklearn 1.7.1\n"]}], "source": ["print(sys.version)\n", "print('numpy',np.__version__,'matplotlib',matplotlib.__version__,'sklearn',sklearn.__version__)"]}, {"cell_type": "code", "execution_count": 3, "id": "d3ef5537", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["   x  y\n", "0  1  2\n", "1  2  3\n", "2  3  5\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["df=pd.DataFrame({'x':[1,2,3],'y':[2,3,5]})\n", "print(df.head())\n", "ax=df.plot(x='x', y='y', kind='line')\n", "ax.set_title('Verificació entorn')\n", "df=pd.DataFrame"]}], "metadata": {"kernelspec": {"display_name": "ia-bio", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}