{"cells": [{"cell_type": "code", "execution_count": 1, "id": "73d935f9", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd"]}, {"cell_type": "code", "execution_count": 2, "id": "70cf8073", "metadata": {}, "outputs": [], "source": ["pd.set_option(\"display.max_columns\", 80)\n", "pd.set_option(\"display.width\", 120)"]}, {"cell_type": "code", "execution_count": 3, "id": "0c8f188b", "metadata": {}, "outputs": [], "source": ["RANDOM_SEED=42  \n", "rng=np.random.default_rng(RANDOM_SEED)"]}, {"cell_type": "code", "execution_count": 4, "id": "8da7e6d7", "metadata": {}, "outputs": [], "source": ["df=pd.read_csv(\"S1_demo_hospital_min.csv\")"]}, {"cell_type": "code", "execution_count": 5, "id": "82da71f0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(10, 12)\n", "  episode_id patient_id  age_years sex  hb_g_dl  creatinine_mg_dl  glucose_mg_dl  hr_bpm  sbp_mmhg  spo2_pct  temp_c  \\\n", "0       E001       P001         34   F     13.2               0.9             95      78       120        98    36.7   \n", "1       E002       P002         77   M     11.1               1.4            180      96       140        94    37.8   \n", "2       E003       P003         61   F     12.4               1.1            110      85       130        96    37.0   \n", "\n", "   readmit_30d  \n", "0            0  \n", "1            1  \n", "2            0  \n", "<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 10 entries, 0 to 9\n", "Data columns (total 12 columns):\n", " #   Column            Non-Null Count  Dtype  \n", "---  ------            --------------  -----  \n", " 0   episode_id        10 non-null     object \n", " 1   patient_id        10 non-null     object \n", " 2   age_years         10 non-null     int64  \n", " 3   sex               10 non-null     object \n", " 4   hb_g_dl           10 non-null     float64\n", " 5   creatinine_mg_dl  10 non-null     float64\n", " 6   glucose_mg_dl     10 non-null     int64  \n", " 7   hr_bpm            10 non-null     int64  \n", " 8   sbp_mmhg          10 non-null     int64  \n", " 9   spo2_pct          10 non-null     int64  \n", " 10  temp_c            10 non-null     float64\n", " 11  readmit_30d       10 non-null     int64  \n", "dtypes: float64(3), int64(6), object(3)\n", "memory usage: 1.1+ KB\n", "None\n", "readmit_30d\n", "0    0.7\n", "1    0.3\n", "Name: proportion, dtype: float64\n"]}], "source": ["print(df.shape)\n", "print(df.head(3))\n", "print(df.info())\n", "print(df['readmit_30d'].value_counts(normalize=True))"]}, {"cell_type": "code", "execution_count": 6, "id": "295dcb1d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Valors de glucosa fora de rang 10\n"]}], "source": ["ok_glucose = df['glucose_mg_dl'].between(40, 600) | df['glucose_mg_dl'].isna()\n", "print(\"Valors de glucosa fora de rang\", (ok_glucose).sum())"]}, {"cell_type": "code", "execution_count": 7, "id": "8c6e3b42", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Valors de glucosa fora de rang 7\n"]}], "source": ["ok_glucose = df['glucose_mg_dl'].between(100,200) | df['glucose_mg_dl'].isna()\n", "print(\"Valors de glucosa fora de rang\", (ok_glucose).sum())"]}, {"cell_type": "code", "execution_count": 8, "id": "c6cc7376", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["episode_id          0.0\n", "patient_id          0.0\n", "age_years           0.0\n", "sex                 0.0\n", "hb_g_dl             0.0\n", "creatinine_mg_dl    0.0\n", "glucose_mg_dl       0.0\n", "hr_bpm              0.0\n", "sbp_mmhg            0.0\n", "spo2_pct            0.0\n", "dtype: float64\n"]}], "source": ["missing = df.isna().mean().sort_values(ascending=False)\n", "print(missing.head(10))"]}, {"cell_type": "code", "execution_count": 9, "id": "ff698f1e", "metadata": {}, "outputs": [], "source": ["TARGET = 'readmit_30d'\n", "features = [c for c in df.columns if c not in {TARGET, 'patient_id', 'episode_id'}]"]}, {"cell_type": "code", "execution_count": 10, "id": "4fd5c8e0", "metadata": {}, "outputs": [{"data": {"text/plain": ["(['age_years',\n", "  'sex',\n", "  'hb_g_dl',\n", "  'creatinine_mg_dl',\n", "  'glucose_mg_dl',\n", "  'hr_bpm',\n", "  'sbp_mmhg',\n", "  'spo2_pct',\n", "  'temp_c'],\n", " 9)"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["features[:10], len(features)"]}, {"cell_type": "code", "execution_count": 11, "id": "2da29ebb", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["readmit_30d\n"]}], "source": ["print(TARGET)\n"]}, {"cell_type": "code", "execution_count": 12, "id": "4fdfa7b1", "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt"]}, {"cell_type": "code", "execution_count": null, "id": "52876bc0", "metadata": {"notebookRunGroups": {"groupValue": "1"}}, "outputs": [{"ename": "SyntaxError", "evalue": "invalid syntax (**********.py, line 2)", "output_type": "error", "traceback": ["\u001b[1;36m  Cell \u001b[1;32mIn[13], line 2\u001b[1;36m\u001b[0m\n\u001b[1;33m    tab = y.value_counts(.sort_index().rename(index={0:'No readmit', 1:'Readmit'}).to_frame)\u001b[0m\n\u001b[1;37m                         ^\u001b[0m\n\u001b[1;31mSyntaxError\u001b[0m\u001b[1;31m:\u001b[0m invalid syntax\n"]}], "source": ["y= df[TARGET].astype(int)\n", "tab = y.value_counts(sort_index().rename(index={0:'No readmit', 1:'Readmit'}).to_frame)"]}], "metadata": {"kernelspec": {"display_name": "ia-bio", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}