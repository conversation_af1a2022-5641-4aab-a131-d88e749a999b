{"cells": [{"cell_type": "code", "execution_count": 10, "id": "a5473072-acf5-4e8b-93ac-3edced8110e0", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from typing import List\n"]}, {"cell_type": "code", "execution_count": 11, "id": "89db1c40-e9c7-4d7d-9ca1-dc41b78077e5", "metadata": {}, "outputs": [], "source": ["def filtrar_adultos(df: pd.DataFrame) -> pd.DataFrame:\n", "    return df.loc[df['age_years'] >= 18].copy()"]}, {"cell_type": "code", "execution_count": 12, "id": "c91ebefe-e166-48f7-a44d-1fa926e375d1", "metadata": {}, "outputs": [], "source": ["def derivar_basicas(df: pd.DataFrame) -> pd.DataFrame:\n", "    def edad_band(x):\n", "        if pd.isna(x): return pd.NA\n", "        if x < 40: return '<40'\n", "        if x < 60: return '40-59'\n", "        if x < 75: return '60-74'\n", "        return '>=75'\n", "    out = df.copy()\n", "    out['edad_band'] = out['age_years'].map(edad_band)\n", "    out['taquicardia'] = (out['hr_bpm'] >= 100).astype('boolean')\n", "    return out"]}, {"cell_type": "code", "execution_count": 13, "id": "2c65bb11-6e29-42ee-b6c9-bca9ffec0cba", "metadata": {}, "outputs": [], "source": ["def seleccionar_campos(df: pd.DataFrame, keep: List[str]) -> pd.DataFrame:\n", "    return df[keep].copy()"]}, {"cell_type": "code", "execution_count": 14, "id": "a4881167-7e26-40d5-b1dc-1a891dc1793f", "metadata": {}, "outputs": [], "source": ["df= pd.read_csv(\"HOSPITAL_General_Sim_min.csv\")\n", "\n"]}, {"cell_type": "code", "execution_count": 15, "id": "3946cce6-851a-4f04-bfc0-d47ebfd3d3cc", "metadata": {}, "outputs": [], "source": ["#lista de columnas que queremos en el DATASET\n", "campos=['age_years','sex','bmi','hr_bpm','sbp_mmhg','spo2_pct','hb_g_dl',\n", "        'creatinine_mg_dl','glucose_mg_dl','edad_band','taquicardia','readmit_30d']\n"]}, {"cell_type": "code", "execution_count": 16, "id": "58dd42e4-e05a-4d2c-a7ab-56dc06a32b2b", "metadata": {}, "outputs": [], "source": ["base = (df\n", "        .pipe(filtrar_adultos)\n", "        .pipe(derivar_basicas)\n", "        .pipe(seleccionar_campos, keep=campos)\n", "       )\n", "\n"]}, {"cell_type": "code", "execution_count": 17, "id": "cdbc76a5-a08d-48ec-aaef-498cb81488c7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["OK: baseS03.csv creado\n"]}], "source": ["base.to_csv(\"baseS03.csv\", index=False)\n", "print (\"OK: baseS03.csv creado\")\n"]}, {"cell_type": "code", "execution_count": 18, "id": "b4c621a1-fdf8-4696-a253-ff6ae71cb0b0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["   age_years sex   bmi  hr_bpm  sbp_mmhg  spo2_pct  hb_g_dl  creatinine_mg_dl  \\\n", "0       78.3   M  23.1    80.0     136.0      96.0     11.9          1.742742   \n", "1       71.5   F  27.3    71.0     113.0      93.0     12.1          2.066113   \n", "2       66.1   M  25.9   110.0     114.0      96.0     12.7          1.026788   \n", "\n", "   glucose_mg_dl edad_band  taquicardia  readmit_30d  \n", "0      50.000000      >=75        False            0  \n", "1     132.798301     60-74        False            0  \n", "2     146.133364     60-74         True            0  \n"]}], "source": ["print(base.head(3))"]}, {"cell_type": "code", "execution_count": null, "id": "f80ee0fd-d657-4c12-8464-d16be3500609", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "ia-bio", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}